<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.4.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.mmt.hotels</groupId>
	<artifactId>clientbackend</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>war</packaging>
	<name>Hotels-ClientGateway</name>
	<description>Demo project for Spring Boot</description>
	<properties>
        <java.version>1.8</java.version>
        <httpcore.version>4.4.8</httpcore.version>
		<hotel-entity-schema.version>1.22.45</hotel-entity-schema.version>
		<hotel-ClientGateway-schema.version>0.13.52</hotel-ClientGateway-schema.version>
		<hotel-discounting-schema.version>1.5.66</hotel-discounting-schema.version>
        <sonar.sourceEncoding>UTF-8</sonar.sourceEncoding>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
		<jacoco.version>0.8.10</jacoco.version>
        <sonar-jacoco-listeners.version>3.8</sonar-jacoco-listeners.version>
        <joda-time.version>2.9.4</joda-time.version>
        <jacoco.out.ut.file>jacoco-ut.exec</jacoco.out.ut.file>
        <sonar.exclusions>
            **/src/main/java/com/mmt/hotels/clientgateway/thirdparty/**/*,
            **/src/main/java/com/mmt/hotels/drool/droolintegrator/**/*,
            **/src/main/java/com/mmt/hotels/clientgateway/businessobjects/*,
			**/src/main/java/com/mmt/hotels/clientgateway/pms/*,
			**/src/main/java/com/mmt/hotels/clientgateway/configuration/*,
			**/src/main/java/com/mmt/hotels/clientgateway/exception/*,
			**/src/main/java/com/mmt/hotels/clientgateway/enums/*,
			**/src/main/java/com/mmt/hotels/clientgateway/constants/*,
			**/src/main/java/com/mmt/hotels/clientgateway/controller/ListingControllerCB,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/response/MyPartnerTooltip.java,
			**/src/main/java/com/mmt/hotels/clientgateway/consul/consulHelper/FunnelRange.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/response/MyPartnerTooltip.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/request/TravelTipRequestTransformer.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/request/android/TravelTipRequestTransformerAndroid.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/request/desktop/TravelTipRequestTransformerDesktop.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/request/ios/TravelTipRequestTransformerIOS.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/request/pwa/TravelTipRequestTransformerPWA.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/response/android/TravelTipResponseTransformerAndroid.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/response/desktop/TravelTipResponseTransformerDesktop.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/response/ios/TravelTipResponseTransformerIOS.java,
			**/src/main/java/com/mmt/hotels/clientgateway/transformer/response/pwa/TravelTipResponseTransformerPWA.java,
			**src/main/java/com/mmt/hotels/clientgateway/restexecutors/TravelTripExecutor.java,
			<!-- remove -->
			**/src/main/java/com/mmt/hotels/clientgateway/util/*,
			**/src/main/java/com/mmt/hotels/clientgateway/controller/advice/ControllerAdvice.java,
			**/src/main/java/com/mmt/hotels/clientgateway/controller/PMSReloadController.java,
			**/src/main/java/com/mmt/hotels/clientgateway/consul/*,
			**/src/main/java/com/mmt/hotels/clientgateway/consul/consulHelper/*,
			**/src/main/java/com/mmt/hotels/clientgateway/grpcconnector/*,
			**/src/main/java/com/mmt/hotels/clientgateway/consul/consulHelper/*,
			**/src/main/java/com/mmt/hotels/clientgateway/grpcexecutor/*,
			**/src/main/java/com/mmt/hotels/clientgateway/restexecutors/*,
			**/src/main/java/com/mmt/hotels/clientgateway/operations/HotelsListingOperation.java
		</sonar.exclusions>

		<spring-cloud-version>2.2.4.RELEASE</spring-cloud-version>

	</properties>

	<dependencies>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<exclusions>
			<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
			<!--<scope>provided</scope>-->
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.6</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
		<dependency>
            <groupId>com.makemytrip.hotels</groupId>
            <artifactId>hotels-entity-schema</artifactId>
            <version>${hotel-entity-schema.version}</version>
        </dependency>
		<dependency>
			<groupId>com.mmt.hotels</groupId>
			<artifactId>Hotels-DiscountingAggregator-Schema</artifactId>
			<version>${hotel-discounting-schema.version}</version>
		</dependency>
		<dependency>
			<groupId>com.mmt.hotels</groupId>
			<artifactId>Hotels-Orchestrator-Schema</artifactId>
			<version>3.0.25</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.16</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		 <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>${httpcore.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
<!--			<version>2.7.2</version>-->
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-tcnative-boringssl-static</artifactId>
			<version>2.0.46.Final</version>
		</dependency>
		<dependency>
			<groupId>com.mmt.hotels</groupId>
			<artifactId>Hotels-UGC-Schema</artifactId>
			<version>1.3.08</version>
			<exclusions>
				<exclusion>
					<groupId>io.grpc</groupId>
					<artifactId>grpc-protobuf</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.grpc</groupId>
					<artifactId>grpc-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.grpc</groupId>
					<artifactId>grpc-protobuf-lite</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.grpc</groupId>
					<artifactId>grpc-stub</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java-util</artifactId>
			<version>3.8.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>3.21.7</version>
		</dependency>
		<dependency>
			<groupId>io.grpc</groupId>
			<artifactId>grpc-all</artifactId>
			<version>1.29.0</version>
		</dependency>
		<dependency>
			<groupId>io.grpc</groupId>
			<artifactId>grpc-services</artifactId>
			<version>1.29.0</version>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger-ui</artifactId>
			<version>2.9.2</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>

		<dependency>
			<groupId>com.ibm.icu</groupId>
			<artifactId>icu4j</artifactId>
			<version>4.8</version>
		</dependency>
        <dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>${logback.version}</version>
		</dependency>
		<dependency>
			<groupId>com.mmt.hotels</groupId>
			<artifactId>Hotels-ClientGateway-Schema</artifactId>
			<version>${hotel-ClientGateway-schema.version}</version>
		</dependency>
		<dependency>
    		<groupId>junit</groupId>
    		<artifactId>junit</artifactId>
    		<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.mmt</groupId>
			<artifactId>propertymanager</artifactId>
			<version>0.0.8</version>
		</dependency>
		<dependency>
   			<groupId>org.junit.platform</groupId>
    		<artifactId>junit-platform-launcher</artifactId>
    		<version>1.5.1</version>
    		<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>com.mmt.hotels</groupId>
            <artifactId>SecureKafkaProducer</artifactId>
            <version>2.1.2</version>
        </dependency>
		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1.1</version>
		</dependency>
		<dependency>
			<groupId>com.mmt</groupId>
			<artifactId>scrambler</artifactId>
			<version>1.1.0</version>
			</dependency>
		<dependency>
			<groupId>org.sonarsource.java</groupId>
			<artifactId>sonar-jacoco-listeners</artifactId>
			<version>${sonar-jacoco-listeners.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-consul-config</artifactId>
			<version>${spring-cloud-version}</version>
		</dependency>

		<dependency>
			<groupId>com.mmt.lib</groupId>
			<artifactId>metric-watcher</artifactId>
			<version>3.0.4</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.kafka</groupId>
					<artifactId>kafka_2.10</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.logging.log4j</groupId>
					<artifactId>log4j-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
    		<groupId>org.json</groupId>
    		<artifactId>json</artifactId>
    		<version>20171018</version>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${joda-time.version}</version><!--$NO-MVN-MAN-VER$ -->
		</dependency>
		<dependency>
			<groupId>com.mmt.hotels.util</groupId>
			<artifactId>Hotels-Utils</artifactId>
			<version>release_1.0.33</version>
		</dependency>
		<dependency>
			<groupId>com.mmt.hotels.util</groupId>
			<artifactId>Hotels-Secretsmanager</artifactId>
			<version>release_1.0.33</version>
		</dependency>

	</dependencies>

	<build>
		<finalName>clientbackend</finalName>
		<plugins>
			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>3.11.0.3922</version>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.5.3</version>
				<executions>
					<execution>
						<id>create-archive</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<tarLongFileMode>posix</tarLongFileMode>
							<descriptor>src/main/resources/assembly/assembly.xml</descriptor>
							<tarLongFileMode>posix</tarLongFileMode>
							<finalName>Hotels-${assemblyName}</finalName>
							<outputDirectory>target</outputDirectory>
							<appendAssemblyId>false</appendAssemblyId>
							<tarLongFileMode>posix</tarLongFileMode>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<argLine>${jacoco.agent.ut.arg}</argLine>

				</configuration>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco.version}</version>
				<executions>
					<execution>
						<id>prepare-ut-agent</id>
						<phase>process-test-classes</phase>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
						<configuration>
							<propertyName>jacoco.agent.ut.arg</propertyName>
							<append>true</append>
							<excludes>
								<exclude>**/CommonHelper$*.class</exclude>
							</excludes>
						</configuration>
					</execution>
					<execution>
						<id>report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
						<configuration>
							<formats>
								<format>XML</format>
							</formats>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
