package com.mmt.hotels.clientgateway.transformer.response.orchestrator;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.response.pricing.jsonviews.PIIView;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.propertymanager.config.PropertyManager;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.SHORTSTAYS_FUNNEL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.DRIVING_DURATION_ZONE_SHORTSTAY;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_FONT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT_FONT;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class OrchSearchHotelsResponseTransformerAndroidTest {

    @InjectMocks
    OrchSearchHotelsResponseTransformerAndroid orchSearchHotelsResponseTransformerAndroid;


    @Mock
    PropertyManager propManager;

    @Mock
    PropertyTextConfig propertyTextConfig;

    @Mock
    CommonConfig commonConfig;

    @Mock
    CommonConfigConsul commonConfigConsul;

    @Spy
    ObjectMapperUtil objectMapperUtil;

    @Spy
    Utility utility;

    @Spy
    CommonResponseTransformer commonResponseTransformer;

    @Spy
    DateUtil dateUtil;

    @Mock
    PolyglotService polyglotService;

    @Spy
    CommonHelper commonHelper;

    @Mock
    MobConfigHelper mobConfigHelper;

    @Spy
    PersuasionUtil persuasionUtil;

    ObjectMapper mapper;

    Gson gson = new Gson();

    @Test
    public void init() {
        orchSearchHotelsResponseTransformerAndroid.init();
    }

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(commonResponseTransformer, "polyglotService", polyglotService);
        when(polyglotService.getTranslatedData(eq(GROUP_PRICE_TEXT_ONE_NIGHT_ALT_ACCO))).thenReturn("{NIGHT_COUNT} Night");
        when(polyglotService.getTranslatedData(eq("GROUP_PRICE_TEXT_ONE_NIGHT_PERNEW"))).thenReturn("Total <b>₹{AMOUNT}</b> for {NIGHT_COUNT} Night, {ROOM_COUNT} Rooms");
        when(polyglotService.getTranslatedData(eq(SHORTSTAY_LOCATION_PERSUASION_PREFIX_TEXT_FONT))).thenReturn("<font color='#FFFFFF'><b>{city_text}</b></font>");
        when(polyglotService.getTranslatedData(eq(SHORTSTAY_LOCATION_PERSUASION_SUFFIX_TEXT_FONT))).thenReturn("<font color='#FD9C35'><b>{driving_text}</b></font>");
        when(polyglotService.getTranslatedData(eq(DRIVING_DURATION_ZONE_SHORTSTAY))).thenReturn("({duration} drive from {city_name})");
        mapper = new ObjectMapper();
        mapper.writerWithView(PIIView.External.class);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ReflectionTestUtils.setField(objectMapperUtil, "mapper", mapper);
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "placeHoldersToShowConfig", "{\"SIMILAR_HOTELS\":[\"PLACEHOLDER_IMAGE_LEFT_TOP\",\"PLACEHOLDER_CARD_M4\",\"PLACEHOLDER_CARD_M1\"]}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "filterConditionsConfig", "{\"range\":{\"minValue\":0,\"maxValue\":3000},\"categoriesIncluded\":[\"Premium Properties\"],\"categoriesExcluded\":[\"MMT Value Stays\"]}");
        ReflectionTestUtils.setField(utility, "dateUtil", dateUtil);

        String specialFarePersuasionConfig = "{\"DESKTOP\":{\"PC_RIGHT_1_1\":{\"style\":{\"styleClasses\":[\"specialFareTag\",\"pushRight\"]}},\"PC_RIGHT_3\":{\"hover\":{\"style\":{\"styleClasses\":[\"specialFareInfo-tooltip\"]}},\"style\":{\"styleClasses\":[\"specialFareInfo\"]}}},\"APPS\":{\"PLACEHOLDER_BOTTOM_BOX_M\":{\"style\":{\"bgColor\":\"#FFF6E8\",\"textColor\":\"#CF8100\",\"fontType\":\"B\",\"fontSize\":\"SMALL\",\"iconHeight\":16,\"iconWidth\":16},\"iconurl\":\"https://promos.makemytrip.com/images/myBiz/hotels/Info_Icon.png\", \"topLevelStyle\":{\"gravity\":\"center\"}},\"PLACEHOLDER_PRICE_BOTTOM_M\":{\"topLevelStyle\":{\"bgGradient\":{\"start\":\"#EEAF4C\",\"end\":\"#CE6112\"}},\"style\":{\"textColor\":\"#FFFFFF\",\"fontType\":\"B\",\"fontSize\":\"SMALL\",\"maxLines\":1}}}}";
        Map<String, Map<String, PersuasionData>> specialFarePersuasionConfigMap = gson.fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
        }.getType());

        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "specialFarePersuasionConfigMap", specialFarePersuasionConfigMap);
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "basicDetailDeeplink", "https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "basicDetailDeeplinkGlobal", "https://www.makemytrip.com/hotels/hotel-details?hotelId={0}&checkin={1}&checkout={2}&country={3}&city={4}&openDetail=true&currency={5}&roomStayQualifier={6}&locusId={7}&locusType={8}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "rootLevelSharingUrl", "https://app.mmyt.co/Xm2V/hotelListingShare?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "rootLevelDeeplink", "https://www.makemytrip.com/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}");
        ReflectionTestUtils.setField(orchSearchHotelsResponseTransformerAndroid, "rootLevelDeeplinkGlobal", "https://www.makemytrip.com/hotels/hotel-listing/?checkin={0}&checkout={1}&city={2}&country={3}&roomStayQualifier={4}&checkAvailability={5}&_uCurrency={6}");
    }

    @Test
    public void convertSearchHotelsResponseTest() throws JsonProcessingException {
        String listingResponseJson = "{\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"RECENTLY_VIEWED_HOTELS\",\"heading\":\"Recently Viewed\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201106120942397721\",\"name\":\"The Ashok\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201106120942397721&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/the_ashok-details-delhi.html\",\"stayType\":\"Entire\",\"threeSixtyViewIconUrl\":\"https://promos.makemytrip.com/Hotels_product/Listing/3603x.png\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Festive Weekend Deals\",\"PREMIND\",\"Deals for Vaccinated Travellers\",\"Child Friendly\",\"Inside BD\",\"Inmarket\",\"Hills\",\"Central Heating\",\"Great Value Packages\",\"kids_stay_free\",\"Premium Properties\",\"Last Minute Deals\",\"premium_hotels\"],\"locationPersuasions\":[\"Chanakyapuri\",\"10.0 km drive to T1 - Delhi Airport (IGI Airport)\"],\"facilityHighlights\":[\"Spa\",\"Restaurant\",\"Bar\"],\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"media\":{\"images\":[{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-afe9941a2d1811eea3cd0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-3ce394a835be11ee982f0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r2imghtlak.mmtcdn.com/r2-mmt-htl-image/htl-imgs/201106120942397721-21bae8ca35be11eeb7ae0a58a9feac02.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":3738,\"totalRatingCount\":7157,\"subRatings\":[{\"name\":\"Location\",\"rating\":4.5,\"reviewCount\":851,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":2096,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.8,\"reviewCount\":1106,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"The Ashok\",\"code\":\"201106120942397721\",\"ratePlans\":[{\"code\":\"1618459331976956240\",\"inclusions\":[{\"category\":\"Free Breakfast\",\"name\":\"Complimentary  Breakfast is available.\"}],\"price\":{\"basePrice\":14999.0,\"displayPrice\":12555.0,\"totalTax\":3375.0,\"savingPerc\":0.0,\"couponCode\":\"MMTBESTBUY\",\"couponDiscount\":944.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"MMTBESTBUY\",\"discount\":944.0,\"specialPromoCoupon\":false,\"type\":\"Get INR 944  Off\"}],\"taxBreakUp\":{\"hotelTax\":2430.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":945.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"Free Cancellation (100% refund) if you cancel this booking before 2024-10-24 13:59:59 (destination time). Cancellations post that will be subject to a hotel fee as follows:From 2024-10-24 14:00:00 (destination time) till 2024-10-26 13:59:59 (destination time) - 100% of booking amount.After 2024-10-26 14:00:00 (destination time) - 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":\"2024-10-24 13:59:59\",\"penaltyValue\":\"FREE_CANCELLATION\",\"penaltyType\":\"F\"}]},\"mealPlans\":[{\"code\":\"CP\",\"value\":\"Breakfast\"}]}]}],\"altAcco\":false}]}],\"lastHotelId\":\"201512161105316614\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"CTDEL\",\"type\":\"znshim\",\"countryId\":\"IN\",\"countryName\":null,\"cityId\":\"ZNSHIM\",\"cityName\":\"\",\"stateId\":null,\"geo\":{\"latitude\":\"\",\"longitude\":\"\"}},\"sortCriteria\":{\"field\":\"S_hsq610_dspers_v2_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_hsq610_dspers_v2_LC_Per\"}}";
        ListingResponse listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        searchHotelsRequest.getRequestDetails().setMetaInfo(true);
        searchHotelsRequest.getFeatureFlags().setPersuasionSuppression(true);
        CommonModifierResponse commonModifierResponse = getCommonModifierResponse();
        UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
        userGlobalInfo.setUserCountry("US");
        userGlobalInfo.setEntityName("global");
        searchHotelsRequest.getSearchCriteria().setUserGlobalInfo(userGlobalInfo);
        SearchHotelsResponse searchHotelsResponse = orchSearchHotelsResponseTransformerAndroid.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);


        listingResponseJson = "{\"requestId\":\"bca1d743-ea59-4539-a515-0271a43f5284\",\"journeyId\":\"82909517392a9898b-5da3-4458-b986-2741f54ac819\",\"hotelCount\":1,\"personalizedSections\":[{\"name\":\"NOT_MYBIZ_ASSURED_SHOWN\",\"hotelCardType\":\"default\",\"orientation\":\"V\",\"hotelCount\":1,\"cardInsertionAllowed\":false,\"hotels\":[{\"id\":\"201211081220304096\",\"name\":\"Park Plaza, Shahdara\",\"propertyType\":\"Hotel\",\"propertyLabel\":\"Hotel\",\"detailDeeplinkUrl\":\"https://www.makemytrip.com/hotels/hotel-details?hotelId=201211081220304096&checkin=date_3&checkout=date_4&country=IN&city=CTDEL&roomStayQualifier=2e0e&openDetail=true&currency=INR&region=in&checkAvailability=true&locusId=CTDEL&locusType=city\",\"seoUrl\":\"https://www.makemytrip.com/hotels/park_plaza_shahdara-details-delhi.html\",\"stayType\":\"Entire\",\"starRating\":5,\"totalRoomCount\":1,\"soldOut\":false,\"showCallToBook\":false,\"budgetHotel\":false,\"groupBookingHotel\":false,\"groupBookingPrice\":false,\"categories\":[\"Chain\",\"HighFiveV2_Chain\",\"MMTFest\",\"Couple Friendly\",\"Premium PropertiesFlyer Deal\",\"Premium\",\"Workation\",\"MyBiz_Assured\",\"Daily Dhamaka\"],\"locationPersuasions\":[\"East Delhi, Delhi\",\"2.2 km drive to Shahdra Railway Station\"],\"facilityHighlights\":[\"Spa\",\"Swimming Pool\",\"Gym\",\"Restaurant\",\"Steam and Sauna\"],\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"media\":{\"images\":[{\"url\":\"http://r1imghtlak.mmtcdn.com/82090bda780511e7bf27025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"},{\"url\":\"http://r1imghtlak.mmtcdn.com/84a383ca780511e78bf8025f77df004f.jpg?output-quality=75&downsize=243:162&output-format=webp\"}]},\"omnitureFlags\":{\"rtb\":false,\"abso\":false,\"abo\":false,\"wishlisted\":false},\"reviewDetails\":{\"rating\":3.8,\"totalReviewCount\":2083,\"totalRatingCount\":4118,\"subRatings\":[{\"name\":\"Location\",\"rating\":3.8,\"reviewCount\":1385,\"show\":false},{\"name\":\"Hospitality\",\"rating\":3.9,\"reviewCount\":1017,\"show\":true},{\"name\":\"Facilities\",\"rating\":3.9,\"reviewCount\":1045,\"show\":true},{\"name\":\"Food\",\"rating\":3.7,\"reviewCount\":836,\"show\":true},{\"name\":\"Room\",\"rating\":4.0,\"reviewCount\":837,\"show\":true},{\"name\":\"Cleanliness\",\"rating\":4.1,\"reviewCount\":2290,\"show\":true},{\"name\":\"Value For Money\",\"rating\":3.9,\"reviewCount\":778,\"show\":true},{\"name\":\"Child Friendliness\",\"rating\":3.5,\"reviewCount\":46,\"show\":true}],\"ratingText\":\"Very Good\",\"ota\":\"MMT\"},\"rooms\":[{\"name\":\"Park Plaza, Shahdara\",\"code\":\"201211081220304096\",\"ratePlans\":[{\"code\":\"3028728164758747328\",\"price\":{\"basePrice\":5000.0,\"displayPrice\":4500.0,\"totalTax\":540.0,\"savingPerc\":0.0,\"couponCode\":\"DHCASHBACK\",\"couponDiscount\":225.0,\"hotelDiscount\":0.0,\"applicableCoupons\":[{\"autoApplicable\":false,\"couponCode\":\"DHCASHBACK\",\"description\":\"Get  INR 225 Cashback to Card on payments via credit/debit cards\",\"discount\":225.0,\"specialPromoCoupon\":true,\"type\":\"Cashback to Card\"}],\"taxBreakUp\":{\"hotelTax\":540.0,\"hotelServiceCharge\":0.0,\"hcpGst\":0.0,\"serviceFee\":0.0,\"totalTax\":0.0}},\"paymentMode\":\"PAS\",\"cancellationPolicy\":{\"cancelPaneltyDesc\":\"This is a Non-refundable and non-amendable tariff. Cancellations, or no-shows will be subject to a hotel fee equal to the 100% of booking amount.Cancellations are only allowed before CheckIn.\",\"penalties\":[{\"startDate\":null,\"endDate\":null,\"penaltyValue\":\"NON_REFUNDABLE\",\"penaltyType\":null}]},\"mealPlans\":[{\"code\":\"EP\",\"value\":\"Room Only\"}]}]}],\"calendarCriteria\":{},\"soldOutInfo\":{},\"altAcco\":true}]}],\"lastHotelId\":\"201512151820116224\",\"lastFetchedWindowInfo\":\"000#0#3#false\",\"location\":{\"id\":\"RGNCR\",\"type\":\"region\",\"countryId\":\"IN\",\"countryName\":\"India\",\"cityId\":\"CTDEL\",\"cityName\":\"Delhi\",\"stateId\":null,\"geo\":{\"latitude\":\"28.658848\",\"longitude\":\"77.297844\"}},\"sortCriteria\":{\"field\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\",\"order\":\"asc\"},\"trackingInfo\":{\"exp\":\"S_MM_DHS_DEFAULT_v.1_rec_LC_Per\"}}";
        listingResponse = mapper.readValue(listingResponseJson, ListingResponse.class);
        searchHotelsRequest.getRequestDetails().setFunnelSource("GROUP");
        searchHotelsResponse = orchSearchHotelsResponseTransformerAndroid.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);

        listingResponse.setLastFetchedHotelCategory(SectionsType.FILTER_REMOVAL.name());
        searchHotelsRequest.setFilterCriteria(new ArrayList<>());
        Filter filter = new Filter();
        filter.setFilterValue("5");
        filter.setFilterGroup(FilterGroup.STAR_RATING);
        searchHotelsRequest.getFilterCriteria().add(filter);
        searchHotelsRequest.getRequestDetails().setFunnelSource(SHORTSTAYS_FUNNEL);
        searchHotelsResponse = orchSearchHotelsResponseTransformerAndroid.convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);
        Assert.assertNotNull(searchHotelsResponse);
        Assert.assertFalse(searchHotelsResponse.getFilterRemovedCriteria().isEmpty());
        assertEquals(searchHotelsResponse.getFilterRemovedCriteria().get(0).getFilterGroup(), FilterGroup.STAR_RATING);
    }


    @Test
    public void addBookingConfirmationPersuasionTest() {
        HotelDetails hotelEntity = new HotelDetails();
        orchSearchHotelsResponseTransformerAndroid.addBookingConfirmationPersuasion(hotelEntity);
    }


    @Test
    public void addLocationPersuasionToHotelPersuasionsTest() throws JsonProcessingException {
        Hotel hotelEntity = new Hotel();
        List<String> locationPersuasions = Collections.singletonList("Chanakyapuri");
        LinkedHashSet<String> facilities = new LinkedHashSet<>(Arrays.asList("Spa", "Restaurant", "Bar"));
        TransportPoi nearestGroundTransportPoi = new TransportPoi();
        LocationDetails locusData = new LocationDetails();
        locusData.setCityName("Delhi");
        SearchHotelsRequest searchHotelsRequest = createSearchHotelsRequest();
        String dayUsePersuasionsText = "Day Use";
        String drivingTimeText = "10.0 km drive to T1 - Delhi Airport (IGI Airport)";
        orchSearchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);

        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);

        hotelEntity.setHotelPersuasions(null);
        searchHotelsRequest.getRequestDetails().setFunnelSource("SHORTSTAYS");
        orchSearchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, null, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 0);

        orchSearchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);


        locationPersuasions = Arrays.asList("Chanakyapuri", "10.0 km drive to T1 - Delhi Airport (IGI Airport)", "Delhi");
        hotelEntity.setHotelPersuasions(null);
        searchHotelsRequest.getRequestDetails().setFunnelSource("DAYUSE");
        orchSearchHotelsResponseTransformerAndroid.addLocationPersuasionToHotelPersuasions(hotelEntity, locationPersuasions, facilities, searchHotelsRequest, true,  false, dayUsePersuasionsText, nearestGroundTransportPoi, drivingTimeText, locusData, true);
        Assert.assertTrue(hotelEntity.getHotelPersuasions() instanceof HashMap);
        assertEquals(((HashMap) hotelEntity.getHotelPersuasions()).size(), 2);

    }


    @Test
    public void buildStaticCardTest() {
        orchSearchHotelsResponseTransformerAndroid.buildStaticCard(null, null);
    }


    private CommonModifierResponse getCommonModifierResponse() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.setHydraResponse(new HydraResponse());
        commonModifierResponse.getHydraResponse().setHydraMatchedSegment(new HashSet<>(Arrays.asList("r1", "r2", "r3")));
        return commonModifierResponse;
    }

    private SearchHotelsRequest createSearchHotelsRequest() throws JsonProcessingException {
        String requestString = "{\"correlationKey\":null,\"brand\":null,\"client\":null,\"blackInfo\":null,\"deviceDetails\":{\"appVersion\":\"128.0.0.0\",\"deviceId\":\"d7bb97cf-762b-4484-91fc-a224c03cdc96\",\"deviceType\":\"Android\",\"bookingDevice\":\"Android\",\"networkType\":\"WiFi\",\"deviceName\":null,\"appVersionIntGi\":null,\"simSerialNo\":null},\"lastProductId\":null,\"limit\":null,\"requestDetails\":{\"visitorId\":\"d23c479b373ee283\",\"visitNumber\":1,\"trafficSource\":null,\"srCon\":null,\"srCty\":null,\"srcState\":null,\"srLat\":null,\"srLng\":null,\"funnelSource\":\"HOTELS\",\"idContext\":\"B2C\",\"notifCoupon\":null,\"callBackType\":null,\"pushDataToCallToBookQ\":null,\"pushDataToListAllPropQ\":null,\"payMode\":null,\"loggedIn\":true,\"couponCount\":10,\"siteDomain\":\"in\",\"channel\":\"B2Cweb\",\"pageContext\":\"LISTING\",\"firstTimeUserState\":0,\"uuid\":null,\"corpAuthCode\":null,\"corpUserId\":null,\"seoCorp\":false,\"requestor\":null,\"wishCode\":null,\"preApprovedValidity\":null,\"metaInfo\":false,\"zcp\":null,\"requisitionID\":null,\"myBizFlowIdentifier\":null,\"brand\":\"MMT\",\"previousTxnKey\":null,\"oldWorkflowId\":null,\"journeyId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"requestId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"sessionId\":\"CG_QA_6ea818f8-aaff-41bf-a321-705cc0ef6ecb\",\"promoConsent\":false,\"flyerInfo\":null,\"premium\":false,\"semanticSearchDetails\":null,\"forwardBookingFlow\":false,\"extendedPackageCall\":false,\"isIgnoreSEO\":false,\"isRequestCallBack\":false,\"isListAllPropCall\":false},\"detailDeepLinkUrl\":null,\"sortCriteria\":null,\"filterCriteria\":[],\"appliedBatchKeys\":[],\"filterGroupsToRemove\":null,\"filtersToRemove\":null,\"featureFlags\":{\"staticData\":true,\"reviewSummaryRequired\":true,\"walletRequired\":true,\"shortlistingRequired\":false,\"noOfCoupons\":0,\"noOfAddons\":0,\"noOfPersuasions\":0,\"noOfSoldouts\":0,\"coupon\":true,\"mmtPrime\":false,\"persuationSeg\":null,\"persuasionsEngineHit\":true,\"checkAvailability\":true,\"liteResponse\":false,\"applyAbsorption\":false,\"bestOffersLimit\":0,\"dealOfTheDayRequired\":false,\"addOnRequired\":false,\"roomInfoRequired\":false,\"allInclusions\":false,\"hotelCatAndPropNotRequiredInMeta\":false,\"extraAltAccoRequired\":false,\"limitedFilterCall\":false,\"corpMMRRequired\":false,\"unmodifiedAmenities\":false,\"poisRequiredOnMap\":true,\"persuasionsRequired\":true,\"similarHotel\":false,\"locus\":false,\"comparator\":false,\"originListingMap\":false,\"mostBooked\":false,\"detailMap\":false,\"showUpsell\":false,\"filterRanking\":false,\"quickReview\":false,\"dayUsePersuasion\":false,\"selectiveHotels\":false,\"persuasionSuppression\":false,\"hidePrice\":false,\"showBnplCard\":false,\"modifyBooking\":false,\"cardRequired\":false,\"topCard\":false,\"filters\":false,\"seoDS\":false,\"seoCohort\":null,\"roomPreferenceEnabled\":false,\"flashDealClaimed\":false,\"upsellRateplanRequired\":false,\"orientation\":null,\"elitePackageEnabled\":false,\"premiumThemesCardRequired\":false,\"isGoTribe3_0\":null},\"matchMakerDetails\":{},\"imageDetails\":{\"types\":[\"professional\"],\"categories\":[{\"type\":\"H\",\"count\":1,\"height\":162,\"width\":243,\"imageFormat\":\"webp\"}]},\"reviewDetails\":{\"otas\":[\"MMT\",\"TA\"],\"tagTypes\":[\"BASE\",\"WHAT_GUESTS_SAY\"]},\"expData\":\"{\\\"EMIDT\\\":2,\\\"UGCV2\\\":\\\"T\\\",\\\"HFC\\\":\\\"F\\\",\\\"VIDEO\\\":0,\\\"APT\\\":\\\"T\\\",\\\"CHPC\\\":\\\"T\\\",\\\"LSTNRBY\\\":\\\"T\\\",\\\"AARI\\\":\\\"T\\\",\\\"RCPN\\\":\\\"T\\\",\\\"MRS\\\":\\\"T\\\",\\\"ADDON\\\":\\\"T\\\",\\\"NLP\\\":\\\"Y\\\",\\\"PERNEW\\\":\\\"T\\\",\\\"GRPN\\\":\\\"T\\\",\\\"BNPL\\\":\\\"T\\\",\\\"MCUR\\\":\\\"T\\\",\\\"HAFC\\\":\\\"T\\\",\\\"PLRS\\\":\\\"T\\\",\\\"MMRVER\\\":\\\"V3\\\",\\\"PDO\\\":\\\"PN\\\",\\\"BLACK\\\":\\\"T\\\",\\\"CV2\\\":\\\"T\\\",\\\"FLTRPRCBKT\\\":\\\"T\\\",\\\"RTBC\\\":\\\"T\\\",\\\"MLOS\\\":\\\"T\\\",\\\"WPAH\\\":\\\"F\\\",\\\"AIP\\\":\\\"T\\\",\\\"BNPL0\\\":\\\"T\\\",\\\"HIS\\\":\\\"DEFAULT\\\",\\\"APE\\\":10,\\\"PAH\\\":5,\\\"IAO\\\":\\\"F\\\",\\\"CRF\\\":\\\"B\\\",\\\"ALC\\\":\\\"T\\\",\\\"SOU\\\":\\\"T\\\",\\\"PAH5\\\":\\\"T\\\",\\\"rearch\\\":\\\"True\\\"}\",\"expVariantKeys\":null,\"cohertVar\":null,\"multiCityFilter\":null,\"additionalProperties\":null,\"cardId\":null,\"manthanExpDataMap\":null,\"expDataMap\":null,\"contentExpDataMap\":null,\"userLocation\":{\"country\": \"IN\"},\"clusterId\":null,\"orgId\":null,\"validExpList\":null,\"variantKeys\":null,\"businessIdentificationEnableFromUserService\":false,\"selectedTabId\":null,\"searchCriteria\":{\"checkIn\":\"2024-10-26\",\"checkOut\":\"2024-10-27\",\"countryCode\":\"IN\",\"cityCode\":\"ZNSHIM\",\"cityName\":null,\"locationId\":\"CTDEL\",\"locationType\":\"znshim\",\"lat\":null,\"lng\":null,\"currency\":\"INR\",\"personalCorpBooking\":false,\"rmDHS\":false,\"boostProperty\":null,\"baseRateplanCode\":null,\"selectedRatePlan\":null,\"multiCurrencyInfo\":null,\"preAppliedFilter\":false,\"roomStayCandidates\":[{\"rooms\":1,\"adultCount\":2,\"childAges\":[]}],\"parentLocationId\":null,\"parentLocationType\":null,\"tripType\":null,\"slot\":null,\"giHotelId\":null,\"hotelIds\":null,\"limit\":1,\"lastHotelId\":null,\"lastFetchedWindowInfo\":null,\"lastHotelCategory\":null,\"personalizedSearch\":true,\"nearBySearch\":false,\"wishListedSearch\":false,\"totalHotelsShown\":null,\"sectionsType\":null,\"collectionCriteria\":null,\"bookingForGuest\":false,\"travellerEmailID\":null,\"vcId\":null},\"lastPeekedOnMapHotelIds\":null,\"mapDetails\":null,\"nearbyFilter\":null,\"filterRemovedCriteria\":null}";
        return mapper.readValue(requestString, SearchHotelsRequest.class);
    }

    @Test
    public void testBuildBottomSheetImplementation() {
        // Create test data
        PersonalizedSectionDetails personalizedSectionDetails = new PersonalizedSectionDetails();
        personalizedSectionDetails.setName("TEST_SECTION");
        
        // Execute the method - Android implementation returns null
        BottomSheet bottomSheet = orchSearchHotelsResponseTransformerAndroid.buildBottomSheet(personalizedSectionDetails);
        
        // Verify the result
        Assert.assertNull(bottomSheet);
    }

    @Test
    public void testAddPersuasionHoverDataImplementation() {
        // Create test data
        Hotel hotel = new Hotel();
        hotel.setHotelPersuasions(new HashMap<>());
        
        HotelDetails hotelEntity = new HotelDetails();
        Map<String, Object> persuasionsMap = new HashMap<>();
        hotelEntity.setHotelPersuasions(persuasionsMap);
        
        // Setup mocks
        when(persuasionUtil.checkIfIndianessPersuasionExists(any())).thenReturn(true);
        
        // Execute the method
        orchSearchHotelsResponseTransformerAndroid.addPersuasionHoverData(hotel, hotelEntity, null, null, null);
        
        // Verify that the method sets lovedByIndians flag based on persuasion data
        Assert.assertTrue(hotel.isLovedByIndians());
    }

    @Test
    public void testBuildBGColorImplementation() {
        // Test RECENTLY_VIEWED_HOTELS section
        String bgColor = orchSearchHotelsResponseTransformerAndroid.buildBGColor("RECENTLY_VIEWED_HOTELS", "H", "default");
        Assert.assertNull(bgColor); // Android implementation returns null
        
        // Test SIMILAR_HOTELS section
        bgColor = orchSearchHotelsResponseTransformerAndroid.buildBGColor("SIMILAR_HOTELS", "H", "default");
        Assert.assertNull(bgColor); // Android implementation returns null
    }

    @Test
    public void testBuildStaticCardImplementation() {
        // Test with null section
        MyBizStaticCard result = orchSearchHotelsResponseTransformerAndroid.buildStaticCard(null, null);
        Assert.assertNull(result); // Returns null for null input
        
        // Test with valid section and hotels data
        List<HotelDetails> hotels = new ArrayList<>();
        HotelDetails hotel = new HotelDetails();
        Set<String> categories = new HashSet<>();
        categories.add("MyBiz_Assured");
        hotel.setCategories(categories);
        hotels.add(hotel);

        // Execute the method
        result = orchSearchHotelsResponseTransformerAndroid.buildStaticCard("MYBIZ_ASSURED_SHOWN", hotels);
        
        // Android implementation should create a card with MyBiz info
        Assert.assertNull(result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithAllValuesPresent_ShouldReturnSum() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(2);
        List<Integer> childAges = Arrays.asList(5, 8);
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerAndroid, roomStayCandidate);

        // Assert
        assertEquals("Should return sum of adults and children", 4, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullAdultCount_ShouldReturnChildrenCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl",com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(null);
        List<Integer> childAges = Arrays.asList(5, 8);
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerAndroid, roomStayCandidate);

        // Assert
        assertEquals("Should return only children count when adult count is null", 2, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullChildAges_ShouldReturnAdultCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(3);
        roomStayCandidate.setChildAges(null);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerAndroid, roomStayCandidate);

        // Assert
        assertEquals("Should return only adult count when child ages is null", 3, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithEmptyChildAges_ShouldReturnAdultCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(3);
        roomStayCandidate.setChildAges(Collections.emptyList());

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerAndroid, roomStayCandidate);

        // Assert
        assertEquals("Should return only adult count when child ages is empty", 3, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithZeroAdults_ShouldReturnChildrenCount() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(0);
        List<Integer> childAges = Arrays.asList(5, 8, 10);
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerAndroid, roomStayCandidate);

        // Assert
        assertEquals("Should return only children count when adult count is zero", 3, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithBothValuesNull_ShouldReturnZero() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(null);
        roomStayCandidate.setChildAges(null);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerAndroid, roomStayCandidate);

        // Assert
        assertEquals("Should return zero when both adult count and child ages are null", 0, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithLargeNumbers_ShouldReturnCorrectSum() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.clientgateway.request.RoomStayCandidate();
        roomStayCandidate.setAdultCount(10);

        // Create a list with 15 child ages
        List<Integer> childAges = new ArrayList<>();
        for (int i = 0; i < 15; i++) {
            childAges.add(i + 1);
        }
        roomStayCandidate.setChildAges(childAges);

        // Act
        int result = (int) method.invoke(orchSearchHotelsResponseTransformerAndroid, roomStayCandidate);

        // Assert
        assertEquals("Should return correct sum for large numbers", 25, result);
    }

    @Test
    public void testGetGuestCountForPixelUrl_WithNullRoomStayCandidate_ShouldHandleNPE() throws Exception {
        // Arrange
        Method method = OrchSearchHotelsResponseTransformer.class.getDeclaredMethod("getGuestCountForPixelUrl", com.mmt.hotels.clientgateway.request.RoomStayCandidate.class);
        method.setAccessible(true);

        // Act & Assert
        try {
            method.invoke(orchSearchHotelsResponseTransformerAndroid, (Object) null);
            // If we reach here, the test has failed because we expect an exception
            org.junit.Assert.fail("Expected NullPointerException was not thrown");
        } catch (Exception e) {
            // We expect an InvocationTargetException wrapping an NullPointerException
            assertEquals("Should throw InvocationTargetException for null input",
                    java.lang.reflect.InvocationTargetException.class, e.getClass());
            assertEquals("Cause should be NullPointerException",
                    NullPointerException.class, e.getCause().getClass());
        }
    }

    @Test
    public void testModifyDeeplinkDomain() throws JsonProcessingException {
        String originalUrl = "https://www.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN";
        
        // Test SA region
        SearchHotelsRequest searchRequest = createSearchHotelsRequest();
        searchRequest.getSearchCriteria().setCountryCode("SA");
        searchRequest.getUserLocation().setCountry("AE");
        String result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals("https://sa.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN", result);

        // Test SA region case insensitive
        searchRequest.getSearchCriteria().setCountryCode("sa");
        searchRequest.getUserLocation().setCountry("ae");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals("https://sa.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN", result);

        // Test AE region with AE user country
        searchRequest.getSearchCriteria().setCountryCode("AE");
        searchRequest.getUserLocation().setCountry("AE");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals("https://ae.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN", result);

        // Test AE region with non-AE user country
        searchRequest.getSearchCriteria().setCountryCode("AE");
        searchRequest.getUserLocation().setCountry("IN");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals("https://makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN", result);

        // Test AE region case insensitive
        searchRequest.getSearchCriteria().setCountryCode("ae");
        searchRequest.getUserLocation().setCountry("ae");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals("https://ae.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN", result);

        // Test other region (no change)
        searchRequest.getSearchCriteria().setCountryCode("IN");
        searchRequest.getUserLocation().setCountry("IN");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals(originalUrl, result);

        // Test URL with @ symbol
        String urlWithAt = "@https://www.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN";
        searchRequest.getSearchCriteria().setCountryCode("SA");
        searchRequest.getUserLocation().setCountry("AE");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(urlWithAt, searchRequest);
        Assert.assertEquals("https://sa.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN", result);

        // Test empty deeplink
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain("", searchRequest);
        Assert.assertEquals("", result);

        // Test null deeplink
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(null, searchRequest);
        Assert.assertNull(result);

        // Test null country code
        searchRequest.getSearchCriteria().setCountryCode(null);
        searchRequest.getUserLocation().setCountry("AE");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals(originalUrl, result);

        // Test null user country
        searchRequest.getSearchCriteria().setCountryCode("SA");
        searchRequest.getUserLocation().setCountry(null);
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, searchRequest);
        Assert.assertEquals("https://sa.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN", result);

        // Test URL with complex parameters
        String complexUrl = "https://www.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN&openDetail=true&currency=AED&roomStayQualifier=2e0e&locusId=CTGGN&locusType=city&region=ae&viewType=PREMIUM&funnelName=HOTELS&rsc=1e2e&mpn=false";
        searchRequest.getSearchCriteria().setCountryCode("SA");
        searchRequest.getUserLocation().setCountry("AE");
        result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(complexUrl, searchRequest);
        Assert.assertEquals("https://sa.makemytrip.global/hotels/hotel-details?hotelId=201203031137312841&checkin=06252025&checkout=06262025&country=UAE&city=CTGGN&openDetail=true&currency=AED&roomStayQualifier=2e0e&locusId=CTGGN&locusType=city&region=ae&viewType=PREMIUM&funnelName=HOTELS&rsc=1e2e&mpn=false", result);

        // Test with null SearchHotelsRequest
        try {
            result = orchSearchHotelsResponseTransformerAndroid.modifyDeeplinkDomain(originalUrl, null);
            // Should return original deeplink if request is null or has issues
            Assert.assertEquals(originalUrl, result);
        } catch (Exception e) {
            // Exception is acceptable as null request is invalid
            Assert.assertTrue("Should handle null request gracefully", true);
        }
    }

}