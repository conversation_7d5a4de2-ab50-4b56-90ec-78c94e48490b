package com.mmt.hotels.clientgateway.transformer.response;

import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MobConfigPropsConsul;
import com.mmt.hotels.clientgateway.consul.properties.ExtraAdultChildInclusionConfig;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.helpers.FareHoldHelper;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.wrapper.AppInstallStrip;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.desktop.AvailRoomsResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.LuckyUserContext;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.request.RoomCriterion;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.addon.LOB;
import com.mmt.hotels.model.response.FetchLocationsResponseBody;
import com.mmt.hotels.model.response.FetchLocationsResult;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.addon.AddOnNode;
import com.mmt.hotels.model.response.addon.InsuranceDetails;
import com.mmt.hotels.model.response.corporate.CorpTravellerDetails;
import com.mmt.hotels.model.response.corporate.RecentTravellerDetails;
import com.mmt.hotels.model.response.emi.Emi;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.persuasion.HotelTag;
import com.mmt.hotels.clientgateway.response.corporate.CorpTravellerInfo;
import com.mmt.hotels.clientgateway.response.corporate.RecentTravellerItem;
import com.mmt.hotels.model.response.persuasion.HotelTagType;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
import com.mmt.hotels.model.response.pricing.FlexiDetailBottomSheet;
import com.mmt.hotels.model.response.pricing.FullPayment;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.Selected;
import com.mmt.hotels.model.response.pricing.TotalPricing;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.prime.DoubleBlackValidateResponse;
import com.mmt.hotels.model.response.txn.UpdatedUpsellOptions;
import com.mmt.hotels.util.Tuple;
import com.mmt.model.*;
import com.mmt.propertymanager.config.PropertyManager;
import junit.framework.Assert;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.ResourceUtils;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.HOSTEL_TITLE;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;

@SuppressWarnings("deprecation")
@RunWith(MockitoJUnitRunner.class)
public class AvailRoomsResponseTransformerTest {

    private RoomDetailsResponse roomDetailsResponse;
    private AvailRoomsRequest availRoomsRequest;
    private Map<String, String> expDataMap;

    @InjectMocks
    AvailRoomsResponseTransformerDesktop availRoomsResponseTransformerDesktop;

    @InjectMocks
    CommonResponseTransformer commonResponseTransformer;

    @InjectMocks
    private Utility utility;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    MetricAspect metricAspect;

    @Mock
    PropertyManager propManager;;

    @Mock
    DateUtil dateUtil;
    @Mock
    PersuasionUtil persuasionUtil;
    @Mock
    FareHoldHelper fareHoldHelper;
    @Spy
    PricingEngineHelper pricingEngineHelper;

    @Mock
    MobConfigPropsConsul mobConfigPropsConsul;
    @Mock
    FetchLocationsResponseBody mockResponseBody;

    @Mock
    private MobConfigHelper mobConfigHelper;

    @Mock
    CommonConfigConsul commonConfigConsul;

    private BestCoupon coupon;
    private Map<String, PersuasionResponse> persuasionMap;

    private String cityCode;
    private String cabsDeepLink;
    private boolean bookingDeviceDesktop;


    @Before
    public void setup() {
        coupon = new BestCoupon();
        persuasionMap = new HashMap<>();
        commonResponseTransformer = Mockito.spy(new CommonResponseTransformer());
            MockitoAnnotations.initMocks(this);
            when(propManager.getProperty("commonConfig", CommonConfig.class)).thenReturn(mock(CommonConfig.class));
            this.availRoomsResponseTransformerDesktop.init();
        roomDetailsResponse = new RoomDetailsResponse();
        availRoomsRequest = new AvailRoomsRequest();
        expDataMap = new HashMap<>();
        expDataMap = new HashMap<>();
        cityCode = "DEL";
        cabsDeepLink = "http://example.com";
        bookingDeviceDesktop = true;
        utility = Mockito.spy(new Utility());
        MockitoAnnotations.initMocks(this);
        when(polyglotService.getTranslatedData(Mockito.anyString())).thenAnswer(new Answer<Object>() {
            @Override
            public Object answer(InvocationOnMock invocationOnMock) throws Throwable {
                return invocationOnMock.getArgument(0);
            }
        });
        Map<String,Map<String,Map<String,String>>> ratePlanNameMap = new HashMap<>();
        ratePlanNameMap.put(Constants.DEFAULT,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_FC,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_FC).put(Constants.SELLABLE_BED_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).put(Constants.CANCELLATION_TYPE_NR,new HashMap<>());
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_ROOM_TYPE,"{NR}");
        ratePlanNameMap.get(Constants.DEFAULT).get(Constants.CANCELLATION_TYPE_NR).put(Constants.SELLABLE_BED_TYPE,"{NR}");
        Set<String> flightsBookerHydraSegment = new HashSet<>();
        flightsBookerHydraSegment.add("r2021");flightsBookerHydraSegment.add("r2020");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "flightsBookerHydraSegment", flightsBookerHydraSegment);
        ReflectionTestUtils.setField(commonResponseTransformer, "intlNrSupplierExclusionList", new ArrayList<>());
        ReflectionTestUtils.setField(commonResponseTransformer, "propertyRulesMaxCount", 4);
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "mealPlanMapPolyglot", new HashMap<>());
        ReflectionTestUtils.setField(commonResponseTransformer, "corpSegments", new HashSet<>(Arrays.asList("1135", "1152")));
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "groupBookingCardKeys", Arrays.asList("BEST_PRICE_GUARANTEE_CARD","POST_BOOKING_CARD"));
        ReflectionTestUtils.setField(utility, "ratePlanNameMap", ratePlanNameMap);
        ReflectionTestUtils.setField(utility, "ratePlanNameMapRedesign", ratePlanNameMap);
        ReflectionTestUtils.setField(utility,"apLimitForInclusionIcons",1);
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "rtbCardConfigs", new HashMap<>());
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "appInstallDeeplink", "deeplink");
        Map<String, CardData> reviewPageCards = new HashMap<>();
        reviewPageCards.put("BEST_PRICE_GUARANTEE_CARD", new CardData());
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "reviewPageCards", reviewPageCards);

        Map<String,Map<String,List<String>>> supplierToRateSegmentMapping = new HashMap<>();
        supplierToRateSegmentMapping.put(Constants.PACKAGE_RATE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.put(Constants.TC_CLAUSE_KEY,new HashMap<>());
        supplierToRateSegmentMapping.get(Constants.PACKAGE_RATE_KEY).put("EPXX0034",Arrays.asList("1180"));
        supplierToRateSegmentMapping.get(Constants.TC_CLAUSE_KEY).put("EPXX0034",Arrays.asList("1180"));
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop,"supplierToRateSegmentMapping",supplierToRateSegmentMapping);
        ReflectionTestUtils.setField(commonResponseTransformer, "addOnInfoTag", new HashMap<>());
        Map<String, String> rtbTimeLineMap = new HashMap<>();
        rtbTimeLineMap.put("05:00 - 07:00","earlyMorningTime");
        rtbTimeLineMap.put("07:00 - 18:00","dayTime");
        rtbTimeLineMap.put("18:00 - 20:00","earlyNightTime");
        rtbTimeLineMap.put("20:00 - 23:59","lateNightTime");
        rtbTimeLineMap.put("00:00  - 05:00","lateNightTime");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop,"rtbTimeLineMap", rtbTimeLineMap);
        List<String> cabCardCityList = Arrays.asList("CTCLU","CTABER","CTVIC07186251","CTALMER", "CTDUB");
        ReflectionTestUtils.setField(commonResponseTransformer, "cabCardCityList", new HashSet<>(cabCardCityList));
    }

    @Test
    public void testAvailRoomsResponse(){

        HotelRates hotelRates = new HotelRates();
        hotelRates.setIsExtraAdultChild(false);
        hotelRates.setBnplBaseAmount(10d);
        hotelRates.setBlackEligible(false);
        hotelRates.setIsBNPLAvailable(false);
        hotelRates.setPahWalletApplicable(false);
        hotelRates.setPnAvlbl(true);
        hotelRates.setPanCardRequired(true);
        hotelRates.setShowFcBanner(true);
        hotelRates.setGroupBookingHotel(true);
        hotelRates.setSoldOut(false);
        hotelRates.setBreakFast(true);
        hotelRates.setFreeCancellation(true);
        hotelRates.setBreakFastAvailable(true);
        hotelRates.setFreeCancellationAvailable(true);
        hotelRates.setPAHTariffAvailable(true);
        hotelRates.setUserEligiblePayMode(PaymentMode.PAH_WITHOUT_CC);
        hotelRates.setAddressLines(new ArrayList<>());
        hotelRates.getAddressLines().add("Delhi");
        Map<String, RoomInfo> roomInfoMap = new HashMap<>();
        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomSize("300");
        roomInfo.setRoomSizeUnit(" sq.ft.");
        roomInfo.setBedInfoText("1 King Size Bed");
        roomInfoMap.put("1323434",roomInfo);
        hotelRates.setRoomInfo(roomInfoMap);
        hotelRates.setNegotiatedRateFlag(true);

        Map<String, HotelTag> hotelTagMap = new HashMap<>();
        HotelTag hotelTag = new HotelTag();
        hotelTag.background = "test";
        hotelTag.type = HotelTagType.DRIVING_DISTANCE;
        hotelTag.icon = "test";
        hotelTag.title = "test";
        hotelTagMap.put("test", hotelTag);
        hotelRates.setHotelTags(hotelTagMap);

        hotelRates.setRoomTypeDetails(new RoomTypeDetails());
        hotelRates.getRoomTypeDetails().setTotalDisplayFare(new DisplayFare());
        hotelRates.getRoomTypeDetails().getTotalDisplayFare().setOtherCouponByPaymode(new HashMap<>());
        List<BestCoupon> otherCouponsList = new ArrayList<>();
        otherCouponsList.add(new BestCoupon());
        hotelRates.getRoomTypeDetails().getTotalDisplayFare().getOtherCouponByPaymode().put(PaymentMode.PAS.toString(), otherCouponsList);

        DisplayPriceBreakDown displayPriceBreakDown = new DisplayPriceBreakDown();
        displayPriceBreakDown.setDisplayPrice(12d);
        displayPriceBreakDown.setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDown.setNonDiscountedPrice(12d);
        displayPriceBreakDown.setSavingPerc(5.05);
        displayPriceBreakDown.setBasePrice(13.05);
        displayPriceBreakDown.setHotelTax(4d);
        displayPriceBreakDown.setMmtDiscount(0.5);
        displayPriceBreakDown.setBlackDiscount(0.5);
        displayPriceBreakDown.setCdfDiscount(1d);
        displayPriceBreakDown.setWallet(12d);
        displayPriceBreakDown.setPricingKey("key");
        displayPriceBreakDown.setCouponInfo(new BestCoupon());
        displayPriceBreakDown.getCouponInfo().setDescription("Coupon");
        displayPriceBreakDown.getCouponInfo().setCouponCode("code");
        displayPriceBreakDown.getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDown.getCouponInfo().setType("promotional");
        displayPriceBreakDown.getCouponInfo().setBnplAllowed(false);

        List<DisplayPriceBreakDown> displayPriceBreakDownList = new ArrayList<>();
        displayPriceBreakDownList.add(new DisplayPriceBreakDown());
        displayPriceBreakDownList.get(0).setDisplayPrice(12d);
        displayPriceBreakDownList.get(0).setDisplayPriceAlternateCurrency(12d);
        displayPriceBreakDownList.get(0).setNonDiscountedPrice(12d);
        displayPriceBreakDownList.get(0).setSavingPerc(5.05);
        displayPriceBreakDownList.get(0).setBasePrice(13.05);
        displayPriceBreakDownList.get(0).setHotelTax(4d);
        displayPriceBreakDownList.get(0).setMmtDiscount(0.5);
        displayPriceBreakDownList.get(0).setBlackDiscount(0.5);
        displayPriceBreakDownList.get(0).setCdfDiscount(1d);
        displayPriceBreakDownList.get(0).setWallet(12d);
        displayPriceBreakDownList.get(0).setPricingKey("key");
        displayPriceBreakDownList.get(0).setCouponInfo(new BestCoupon());
        displayPriceBreakDownList.get(0).getCouponInfo().setDescription("Coupon");
        displayPriceBreakDownList.get(0).getCouponInfo().setCouponCode("code");
        displayPriceBreakDownList.get(0).getCouponInfo().setSpecialPromoCoupon(false);
        displayPriceBreakDownList.get(0).getCouponInfo().setType("promotional");
        displayPriceBreakDownList.get(0).getCouponInfo().setBnplAllowed(false);

        hotelRates.getRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        hotelRates.getRoomTypeDetails().getTotalDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);

        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<String, RoomType>());

        RoomType roomType = new RoomType();
        roomType.setRoomTypeCode("abc");
        roomType.setRatePlanList(new HashMap<String, RatePlan>()); // fill this from 115
        com.mmt.hotels.model.response.pricing.RatePlan ratePlanCB = new RatePlan();

        ratePlanCB.setAvailDetails(new AvailDetails());
        ratePlanCB.getAvailDetails().setOccupancyDetails(new OccupancyDetails());
        ratePlanCB.setCancelPenaltyList(new ArrayList<CancelPenalty>());
        ratePlanCB.getCancelPenaltyList().add(new CancelPenalty());
        ratePlanCB.getCancelPenaltyList().get(0).setPenaltyDescription(new Penalty());
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.setInclusions(new ArrayList<Inclusion>());
        ratePlanCB.getInclusions().add(new Inclusion());
        ratePlanCB.getInclusions().get(0).setCode("abcd");
        ratePlanCB.getInclusions().get(0).setValue("abcd");
        ratePlanCB.getInclusions().get(0).setSegmentIdentifier("BLACK");
        ratePlanCB.setMealPlans(new ArrayList<MealPlan>());
        ratePlanCB.getMealPlans().add(new MealPlan());
        ratePlanCB.getMealPlans().get(0).setCode("abcd");
        ratePlanCB.getMealPlans().get(0).setValue("abcd");
        ratePlanCB.setPaymentDetails(new PaymentDetails());
        ratePlanCB.getPaymentDetails().setPaymentMode(PaymentMode.PAS);
        ratePlanCB.setDisplayFare(new DisplayFare());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDown(displayPriceBreakDown);
        ratePlanCB.getDisplayFare().getDisplayPriceBreakDown().setEmiDetails(new Emi());
        ratePlanCB.getDisplayFare().setDisplayPriceBreakDownList(displayPriceBreakDownList);
        ratePlanCB.setSupplierDetails(new SupplierDetails());
        ratePlanCB.getSupplierDetails().setCostPrice(19d);
        ratePlanCB.setAddOns(new ArrayList<>());
        ratePlanCB.getAddOns().add(new AddOnNode());
        ratePlanCB.setCampaingText("Free cancellation till 24 hrs");
        ratePlanCB.setExclusiveFlyerRateAvailable(true);
        hotelRates.setAddOns(new ArrayList<>());
        hotelRates.getAddOns().add(new AddOnNode());
        List<GenericCardPayloadData> addonDescList = new ArrayList<>();
        GenericCardPayloadData addonDesc = new GenericCardPayloadData();
        addonDesc.setTitleText("charity");
        addonDescList.add(addonDesc);
        hotelRates.getAddOns().get(0).setDescriptions(addonDescList);
        Map<String,List<String>> addonTncMap = new HashMap<>();
        List<String> tncList = new ArrayList<>();
        tncList.add("addonTnc");
        addonTncMap.put("tncList", tncList);
        hotelRates.getAddOns().get(0).setTnc(addonTncMap);
        ratePlanCB.setCancellationTimeline(new CancellationTimeline());
        List<RoomTariff> roomTarrif = new ArrayList<>();
        RoomTariff tarrif = new RoomTariff();
        tarrif.setNumberOfAdults(2);
        tarrif.setNumberOfChildren(2);
        tarrif.setChildAges(new ArrayList<>());
        tarrif.getChildAges().add(1);
        roomTarrif.add(tarrif);
        RoomTariff tarrif2 = new RoomTariff();
        tarrif2.setNumberOfAdults(2);
        tarrif2.setNumberOfChildren(2);
        tarrif2.setChildAges(new ArrayList<>());
        tarrif2.getChildAges().add(1);
        roomTarrif.add(tarrif2);
        ratePlanCB.setRoomTariff(roomTarrif);
        ratePlanCB.setSegmentId(Constants.MYPARTNER_SEGMENT_ID);
        ratePlanCB.getSupplierDetails().setSupplierCode("EPXX0034");

        roomType.getRatePlanList().put("abc", ratePlanCB);


        hotelRates.getRoomTypeDetails().getRoomType().put("abc", roomType);
        hotelRates.setEmiDetails(new EMIAbridgeResponse());
        hotelRates.setDoubleBlackInfo(new DoubleBlackValidateResponse());
        SpecialRequest spclReq = new SpecialRequest();
        List<SpecialRequestCategory> categories = new ArrayList<>();
        SpecialRequestCategory spclReqCat = new SpecialRequestCategory();
        spclReqCat.setCode("123");
        spclReqCat.setName("Early Checkin");
        List<SpecialRequestCategory> subCategories = new ArrayList<>();
        SpecialRequestCategory spclReqSubCat = new SpecialRequestCategory();
        spclReqSubCat.setName("6 AM");
        subCategories.add(spclReqSubCat);
        spclReqCat.setSubCategories(subCategories);
        categories.add(spclReqCat);
        spclReq.setCategories(categories);
        hotelRates.setSpecialRequestAvailable(spclReq);
        hotelRates.setMustReadRules(new ArrayList<>());
        hotelRates.getMustReadRules().add("Unmarried couples not allowed");

        List<HotelRates> hotelRatesList = new ArrayList<HotelRates>();
        hotelRatesList.add(hotelRates);

        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse.Builder().buildHotelRates(hotelRatesList).build();

        // Add corpTravellerDetails to test the mapping
        CorpTravellerDetails corpTravellerDetails = new CorpTravellerDetails();
        corpTravellerDetails.setAllowedToInviteUser(true);
        corpTravellerDetails.setEditTravellerFlowEnabled(true);

        // Add recently booked travellers
        List<RecentTravellerDetails> recentlyBookedTravellers = new ArrayList<>();
        RecentTravellerDetails traveller = new RecentTravellerDetails();
        traveller.setEmail("<EMAIL>");
        traveller.setFirstName("John");
        traveller.setLastName("Doe");
        traveller.setGender("Male");
        traveller.setMobileNumber("1234567890");
        recentlyBookedTravellers.add(traveller);
        corpTravellerDetails.setRecentlyBookedTravellers(recentlyBookedTravellers);

        roomDetailsResponse.setCorpTravellerDetails(corpTravellerDetails);


        List<HtlRmInfo> htlRmInfoList = new ArrayList<HtlRmInfo>();
        htlRmInfoList.add(new HtlRmInfo());
        htlRmInfoList.get(0).setHotelRoomInfo(new HashMap<String, RoomInfo>());

        roomInfo.setMaster(true);
        roomInfo.setBedInfoText("1 Double Bed,1 King Bed,1 Queen Bed,6 Sofa Cum Beds");
        roomInfo.setMaxAdultCount(5);
        roomInfo.setBeds(new ArrayList<>());
        roomInfo.getBeds().add(new SleepingArrangement());

        htlRmInfoList.get(0).getHotelRoomInfo().put("abc", roomInfo);

        HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity = new HotelsRoomInfoResponseEntity.Builder().buildHtlRmInfo(htlRmInfoList).build();

        AvailRoomsResponse availRoomsResponse;

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        LinkedHashMap<String, String> expDataMap = new LinkedHashMap<>();
        expDataMap.put(Constants.EXP_BNPL_ZERO_VARIANT, Constants.TRUE);
        expDataMap.put(MYPARTNER_EXCLUSIVE_DEAL,"true");
        commonModifierResponse.setExpDataMap(expDataMap);
        commonModifierResponse.setExtendedUser(new ExtendedUser());
        commonModifierResponse.getExtendedUser().setProfileType("CTA");
        commonModifierResponse.getExtendedUser().setAffiliateId("MYPARTNER");

        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setBookingAmount(0f);
        mpFareHoldStatus.setHoldEligible(true);
        mpFareHoldStatus.setExpiry(new Long(123456));
        mpFareHoldStatus.setEligibleForHoldBooking(true);

        when(fareHoldHelper.getMpFareHoldStatus((HotelRates) Mockito.any())).thenReturn(null);
        when(fareHoldHelper.getBookNowDetails(Mockito.any(),Mockito.anyBoolean(),Mockito.anyString())).thenReturn(new BookNowDetails());
        when(utility.isExperimentTrue(Mockito.any(),Mockito.eq(MYPARTNER_EXCLUSIVE_DEAL))).thenReturn(true);
        when(polyglotService.getTranslatedData(ConstantsTranslation.RECENTLY_ADDED_GUESTS_TITLE)).thenReturn("Recently Added Guests");
        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.LUCKY);
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        // Add DeviceDetails to prevent NullPointerException in host calling logic
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("DESKTOP");
        availRoomsRequest.setDeviceDetails(deviceDetails);
        
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity,"", "", null, "{\"ratePlanRedesign\":\"true\"}", null, false, Mockito.anyString(),commonModifierResponse,false,"desktop");

        boolean isXPercentSellOn = MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())
                && commonModifierResponse.getExpDataMap().containsKey(X_PERCENT_SELL_ON) &&
                TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(X_PERCENT_SELL_ON));

        String isXPercentSellExpText = isXPercentSellOn ? X_PERCENT_SELL_ON_TEXT : X_PERCENT_SELL_OFF_TEXT;

        Assert.assertNotNull(availRoomsResponse);
        Assert.assertNotNull(availRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + PRIVILEGED_USER, availRoomsResponse.getLuckyUserContext());
        Assert.assertNotNull(availRoomsResponse.getRateplanlist().get(0).getRatePlanPersuasions());
        Assert.assertNotNull(availRoomsResponse.getTcClauseDetails());
        Assert.assertNotNull(availRoomsResponse.getInstantFareInfo());

        // Verify corpTravellerDetails mapping
        Assert.assertNotNull("CorpTravellerDetails should not be null", availRoomsResponse.getCorpTravellerDetails());
        Assert.assertTrue("AllowedToInviteUser should be true", availRoomsResponse.getCorpTravellerDetails().isAllowedToInviteUser());
        Assert.assertTrue("EditTravellerFlowEnabled should be true", availRoomsResponse.getCorpTravellerDetails().isEditTravellerFlowEnabled());

        // Verify recently booked travellers
        Assert.assertNotNull("RecentlyBooked should not be null", availRoomsResponse.getCorpTravellerDetails().getRecentlyBooked());
        Assert.assertNotNull("Travellers list should not be null", availRoomsResponse.getCorpTravellerDetails().getRecentlyBooked().getTravellers());
        Assert.assertEquals("Should have 1 traveller", 1, availRoomsResponse.getCorpTravellerDetails().getRecentlyBooked().getTravellers().size());

        // Verify traveller details
        RecentTravellerItem travellerItem = availRoomsResponse.getCorpTravellerDetails().getRecentlyBooked().getTravellers().get(0);
        Assert.assertEquals("Email should match", "<EMAIL>", travellerItem.getEmail());
        Assert.assertEquals("FirstName should match", "John", travellerItem.getFirstName());
        Assert.assertEquals("LastName should match", "Doe", travellerItem.getLastName());
        Assert.assertEquals("Gender should match", "Male", travellerItem.getGender());
        Assert.assertEquals("MobileNumber should match", "1234567890", travellerItem.getMobileNumber());
        when(utility.isExperimentOn(Mockito.any(),Mockito.anyString())).thenReturn(true);
        roomDetailsResponse.getHotelRates().get(0).setRequestToBook(true);
        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.LUCKY_UNLUCKY);
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse,hotelsRoomInfoResponseEntity,"","",null,"{\"ratePlanRedesign\":\"true\",\"RTBC\":\"T\"}", null, false,"HOTELS",commonModifierResponse,false, "desktop");
        Assert.assertNotNull(availRoomsResponse);
        Assert.assertNotNull(availRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + CURSED_USER, availRoomsResponse.getLuckyUserContext());
        Assert.assertNotNull(availRoomsResponse.getRtbCard());

        for (Map.Entry<String, RoomType> entry : roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().getRoomType().entrySet()) {
            List<com.mmt.hotels.model.response.pricing.RatePlan> ratePlanlist = entry.getValue().getRatePlanList().entrySet().stream().map(ratePlan -> ratePlan.getValue()).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(ratePlanlist) && ratePlanlist.get(0).getPaymentDetails() !=null &&
                    ratePlanlist.get(0).getPaymentDetails().getPaymentMode() !=null) {
                        ratePlanlist.get(0).getPaymentDetails().setPaymentMode(PaymentMode.PAH_WITH_CC);
            }
        }

        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "AE");

        roomDetailsResponse.getHotelRates().get(0).setCurrencyCode("AED");
        ratePlanCB.getSupplierDetails().setHotelierCurrencyCode("INR");

        List<AdditionalFees> additionalFeesList = new ArrayList<AdditionalFees>();
        AdditionalFees mandatoryCharge = new AdditionalFees();
        mandatoryCharge.setCurrency("AED");
        mandatoryCharge.setAmount(100.00);
        additionalFeesList.add(mandatoryCharge);
        roomDetailsResponse.getHotelRates().get(0).setMandatoryCharges(additionalFeesList);
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);

        roomDetailsResponse.setLuckyUserContext(LuckyUserContext.UNLUCKY);

        // Test with null corpTravellerDetails
        roomDetailsResponse.setCorpTravellerDetails(null);
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity,"", "", null, "{\"ratePlanRedesign\":\"true\"}", null, false, "GROUP",commonModifierResponse, false,"desktop");
        Assert.assertNotNull(availRoomsResponse);
        Assert.assertNotNull(availRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(isXPercentSellExpText + "|" + UNFORTUNATE_USER, availRoomsResponse.getLuckyUserContext());
        Assert.assertNull("CorpTravellerDetails should be null when input is null", availRoomsResponse.getCorpTravellerDetails());
        for (com.mmt.hotels.clientgateway.response.PricingDetails priceDetail : availRoomsResponse.getTotalpricing().getDetails()) {
            if (Constants.TOTAL_AMOUNT_KEY.equalsIgnoreCase(priceDetail.getKey())) {

                Assert.assertNull(priceDetail.getHotelierCurrencyCode());
                Assert.assertNull(priceDetail.getHotelierCurrencyAmount());
                break;
            }
        }


        for (AdditionalMandatoryChargesBreakup breakup : availRoomsResponse.getAdditionalFees().getBreakup()) {

            Assert.assertNull(breakup.getHotelierCurrency());
            Assert.assertNull(breakup.getHotelierCurrencyAmount());
        }

        roomDetailsResponse.getHotelRates().get(0).getRoomTypeDetails().getRoomType().get("abc").getRatePlanList().get("abc").setMpFareHoldStatus(mpFareHoldStatus);
        hotelRates.setNegotiatedRateFlag(false);
        roomDetailsResponse.setLuckyUserContext(null);
        when(fareHoldHelper.getMpFareHoldStatus((HotelRates) Mockito.any())).thenReturn(mpFareHoldStatus);
        when(fareHoldHelper.getBookNowDetails(Mockito.any(),Mockito.anyBoolean(),Mockito.anyString())).thenReturn(new BookNowDetails());
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity,"", "", null, "", null, false, "HOTELS",commonModifierResponse,false,"desktop");
        Assert.assertNotNull(availRoomsResponse);
        Assert.assertNotNull(availRoomsResponse.getLuckyUserContext());
        Assert.assertEquals(X_PERCENT_SELL_OFF_TEXT, availRoomsResponse.getLuckyUserContext());
        Assert.assertNotNull(availRoomsResponse.getBookNowDetails());
        Assert.assertNull(availRoomsResponse.getInstantFareInfo());

        roomDetailsResponse.getHotelRates().get(0).setExpressCheckoutDetail(new ExpressCheckoutDetail());
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity,"", "", null, "", null, false, "HOTELS",commonModifierResponse,false,"desktop");
        Assert.assertNotNull(availRoomsResponse.getTotalpricing().getExpressCheckoutDetail());

        //test for bnpl disabled case text subtext(shown on review page)
        BestCoupon bestCoupon = new BestCoupon();
        bestCoupon.setCouponCode("non_bnpl_coupon");
        bestCoupon.setAutoApplicable(true);
        bestCoupon.setBnplAllowed(false);
        displayPriceBreakDown.setCouponInfo(bestCoupon);
        commonModifierResponse.getExpDataMap().put(Constants.SHOW_DISABLED_BNPL_DETAILS, "true");
        commonModifierResponse.setExtendedUser(null); //for non-myPartner cases
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(),"");
        roomDetailsResponse.getHotelRates().get(0).setIsBNPLAvailable(true);
        ratePlanCB.setExclusiveFlyerRateAvailable(false);
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, "", "", null, "", null, false, "HOTELS", commonModifierResponse, false,"desktop");
        Assert.assertNull(availRoomsResponse.getBnplDetails());

        String expData = "{\"mmt.backend.hotel.default.detail.default.myp_extra_adult_inclusion\":\"true\"}";
        roomDetailsResponse.getHotelRates().get(0).setIsExtraAdultChild(true);
        ExtraAdultChildInclusionConfig extraAdultChildInclusionConfig = new ExtraAdultChildInclusionConfig();
        extraAdultChildInclusionConfig.setEnabled(true);
        extraAdultChildInclusionConfig.setIhSupplierCodeCheckEnabled(false);
        extraAdultChildInclusionConfig.setDhSupplierCodeCheckEnabled(false);
        ReflectionTestUtils.setField(utility, "extraAdultChildInclusionConfig", extraAdultChildInclusionConfig);
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "maxInclusionsThankyou", 3);
        availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, hotelsRoomInfoResponseEntity, "", "", null, expData, null, false, "HOTELS", commonModifierResponse, false,"desktop");
        Assert.assertTrue(availRoomsResponse.getRateplanlist().stream().anyMatch(ratePlan -> ratePlan.getInclusionsList().stream().anyMatch(inclusion -> inclusion.getCode().equals(EXTRA_ADULT_CHILD))));
    }

    @Test
    public void testTotalPricingResponse(){
    	TotalPricingResponse totalPricingResponseOld = new TotalPricingResponse();
    	TotalPricing totalPricing = new TotalPricing();
        String bnplUnavailableMsg = "bnpl unavailable";
        totalPricing.setBnplUnavailableMsg(bnplUnavailableMsg);
    	totalPricingResponseOld.setPriceBreakdown(totalPricing);
    	Map<LOB,DisplayPriceBreakDown> lobWisePricing = new HashMap<>();
    	totalPricing.setDisplayPrice(4505.0);
    	totalPricing.setDisplayPriceAlternateCurrency(76);
    	DisplayPriceBreakDown displayPriceBrkDwnHotel = new DisplayPriceBreakDown();
    	displayPriceBrkDwnHotel.setDisplayPrice(4495.0);
    	displayPriceBrkDwnHotel.setBasePrice(5500.0);
    	displayPriceBrkDwnHotel.setHotelTax(594.0);
    	displayPriceBrkDwnHotel.setMmtServiceCharge(297.0);
    	displayPriceBrkDwnHotel.setCdfDiscount(1346.0);
    	displayPriceBrkDwnHotel.setMmtDiscount(550.0);
    	displayPriceBrkDwnHotel.setWallet(100.0);
    	displayPriceBrkDwnHotel.setAffiliateFee(100.0);
    	lobWisePricing.put(LOB.HOTEL, displayPriceBrkDwnHotel);
    	DisplayPriceBreakDown displayPriceBrkDwnAddon = new DisplayPriceBreakDown();
    	displayPriceBrkDwnAddon.setDisplayPrice(5.0);
    	lobWisePricing.put(LOB.ADDON, displayPriceBrkDwnAddon);
    	totalPricing.setLobWisePricing(lobWisePricing);
        DisplayPriceBreakDown displayPriceBrkDwnAddonInsurance = new DisplayPriceBreakDown();
        displayPriceBrkDwnAddonInsurance.setDisplayPrice(5.0);
        displayPriceBrkDwnAddonInsurance.setInsuranceBreakupMap(new HashMap<>());
        InsuranceDetails id1 = new InsuranceDetails();
        id1.setAmount(2.0d);
        id1.setDisplayLabel("and");
        InsuranceDetails id2 = new InsuranceDetails();
        id2.setAmount(3.0d);
        id2.setDisplayLabel("and");
        displayPriceBrkDwnAddonInsurance.getInsuranceBreakupMap().put("12",id1);
        displayPriceBrkDwnAddonInsurance.getInsuranceBreakupMap().put("13",id2);
        lobWisePricing.put(LOB.INSURANCE, displayPriceBrkDwnAddonInsurance);
        totalPricingResponseOld.getPriceBreakdown().setFullPayment(new FullPayment());
        com.mmt.hotels.model.response.txn.UpdatedUpsellOptions updatedUpsellOptions = new UpdatedUpsellOptions();
        updatedUpsellOptions.setSuccessDisplayText("Yes");
        updatedUpsellOptions.setAddOnType("Flexi Cancel");
        totalPricingResponseOld.setUpdatedUpsellOptions(new ArrayList<>());
        totalPricingResponseOld.getUpdatedUpsellOptions().add(updatedUpsellOptions);
    	TotalPriceResponse totalPriceRsp = availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN");
    	Assert.assertNotNull(totalPriceRsp);
    	Assert.assertNotNull(totalPriceRsp.getTotalPricing());
    	//Assert.assertEquals(bnplUnavailableMsg, totalPriceRsp.getTotalPricing().getBnplUnavailableMsg());
    	Assert.assertNotNull(totalPriceRsp.getAddon());
    	Assert.assertNotNull(totalPriceRsp.getTotalPricing().getDetails());
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(0).getAmount(), 5500.0); // base price
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(1).getAmount(), 1996.0); // total discount
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(2).getAmount(), 3504.0); // price after discount
        Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(3).getAmount(), 100.0); // wallet
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(4).getAmount(), 991.0); // taxes
    	Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(5).getAmount(), 4505.0); // total amount
    	Assert.assertEquals(totalPriceRsp.getAddon().get(0).getAmount(), 5.0); // addon amount
        Assert.assertEquals(totalPriceRsp.getTotalPricing().getDetails().get(6).getAmount(),5.0d);//insurance

        totalPricingResponseOld.getPriceBreakdown().setExpressCheckoutDetail(new ExpressCheckoutDetail());
        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN").getTotalPricing().getExpressCheckoutDetail());

        BestCoupon bestCoupon = new BestCoupon();
        bestCoupon.setCouponCode("non_bnpl_coupon");
        bestCoupon.setBnplAllowed(false);
        totalPricingResponseOld.getPriceBreakdown().setInsuranceStartDateBeforeBnplChargeDate(true);
        totalPricingResponseOld.getPriceBreakdown().setBnplNewVariantSubText("Test");
        totalPricingResponseOld.getPriceBreakdown().setBnplNewVariantText("Test");
        totalPricingResponseOld.setShowDisabledBnplDetails(true);
        totalPricingResponseOld.setBnplVariant(BNPLVariant.BNPL_AT_0);
        totalPriceRsp = availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN");
        Assert.assertFalse(totalPriceRsp.getBnplDetails().isBnplApplicable());
        Assert.assertNotNull(totalPriceRsp.getBnplDetails().getBnplNewVariantText());
        Assert.assertNotNull(totalPriceRsp.getBnplDetails().getBnplNewVariantSubText());




        totalPricingResponseOld.setUserLevelBnplDisabled(true);
        totalPricingResponseOld.setActiveBnplBookingCount(3);
        totalPricingResponseOld.setShowDisabledBnplDetails(true);
        totalPricingResponseOld.setBnplVariant(BNPLVariant.BNPL_AT_0);
        totalPriceRsp = availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN");
        Assert.assertFalse(totalPriceRsp.getBnplDetails().isBnplApplicable());
        Assert.assertNotNull(totalPriceRsp.getBnplDetails().getBnplNewVariantText());
        Assert.assertNotNull(totalPriceRsp.getBnplDetails().getBnplNewVariantSubText());

        ThemifiedDetails themifiedDetails = new ThemifiedDetails();
        themifiedDetails.setThemificationEnabled(true);
        PriceFooterDetail priceFooter =new PriceFooterDetail();
        themifiedDetails.setPriceFooter(priceFooter);
        totalPricingResponseOld.setThemifiedDetails(themifiedDetails);
        totalPriceRsp = availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN");
        Assert.assertFalse(totalPriceRsp.getBnplDetails().isBnplApplicable());

        totalPriceRsp = availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN");
        Assert.assertFalse(totalPriceRsp.getBnplDetails().isBnplApplicable());

        totalPricingResponseOld.setSbppExpValue("1");
        totalPricingResponseOld.setAdultCount(2);
        totalPricingResponseOld.setRoomCount(1);
        totalPriceRsp = availRoomsResponseTransformerDesktop.convertTotalPricingResponse(totalPricingResponseOld, new TotalPricingRequest(),"IN");
        Assert.assertFalse(totalPriceRsp.getBnplDetails().isBnplApplicable());
    }

    @Test
    public void testAlertsNoticesAndMandatoryCharges() throws IOException {
        RoomDetailsResponse roomDetailsResponse = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:review/reviewdata.json")),
                RoomDetailsResponse.class);

        roomDetailsResponse.getHotelRates().get(0).setFlexiDetailBottomSheet(new FlexiDetailBottomSheet());
        roomDetailsResponse.getHotelRates().get(0).getFlexiDetailBottomSheet().setSelected(new Selected());
        roomDetailsResponse.getHotelRates().get(0).getFlexiDetailBottomSheet().getSelected().setSubTitle("abc");
        roomDetailsResponse.getHotelRates().get(0).getFlexiDetailBottomSheet().setUnselected(new Selected());
        roomDetailsResponse.getHotelRates().get(0).getFlexiDetailBottomSheet().getUnselected().setSubTitle("abc");
        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.getExpDataMap().put(MYPARTNER_EXCLUSIVE_DEAL,"true");
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        // Add DeviceDetails to prevent NullPointerException in host calling logic
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("DESKTOP");
        availRoomsRequest.setDeviceDetails(deviceDetails);
        
        AvailRoomsResponse cgAvailRoomResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, null, "", "", null, "{\"ratePlanRedesign\":\"true\"}", null, false, Mockito.anyString(),commonModifierResponse,false,"desktop");

        Assert.assertEquals(3, cgAvailRoomResponse.getAlerts().size());
        Assert.assertEquals(2, cgAvailRoomResponse.getAdditionalFees().getBreakup().size());

        roomDetailsResponse.getHotelRates().get(0).setPreApprovalExpired(true);
        cgAvailRoomResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, null, "", "", null, "{\"ratePlanRedesign\":\"true\"}", null, false, Mockito.anyString(),commonModifierResponse,false,"desktop");
        Assert.assertEquals(3, cgAvailRoomResponse.getAlerts().size());
        Assert.assertEquals(2, cgAvailRoomResponse.getAdditionalFees().getBreakup().size());


//        when(utility.isExperimentTrue(Mockito.any(), Mockito.any())).thenReturn(true);
        cgAvailRoomResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, null, "", "", null, "{\"ratePlanRedesign\":\"true\"}", null, false, Mockito.anyString(),commonModifierResponse,false,"desktop");
        Assert.assertEquals(3, cgAvailRoomResponse.getAlerts().size());
        Assert.assertEquals(2, cgAvailRoomResponse.getAdditionalFees().getBreakup().size());

        roomDetailsResponse.setVistaraDealAvailable(true);
        cgAvailRoomResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, null, "", "", null, "{\"ratePlanRedesign\":\"true\"}", null, false, Mockito.anyString(),commonModifierResponse,false,"desktop");
        Assert.assertNotNull(cgAvailRoomResponse.getTotalpricing().getPricePersuasions());


        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setRoomCriteria(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().add(new AvailRoomsSearchCriteria());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).setRoomStayCandidates(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().add(new com.mmt.hotels.clientgateway.request.RoomStayCandidate());
        availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates().get(0).setAdultCount(1);

        cgAvailRoomResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, null, "", "", null, "{\"SBPP\":\"1\"}", null, false, Mockito.anyString(),commonModifierResponse,false,"desktop");
        Assert.assertNotNull(cgAvailRoomResponse.getTotalpricing().getPricePersuasions());
    }

    @Test
    public void testBuildGuestRoomValue_private() {
        // Arrange
        Mockito.when(polyglotService.getTranslatedData(HOSTEL_TITLE)).thenReturn("Hostel");
        HotelRates hotelRates = new HotelRates();
        hotelRates.setCountryCode("IN");
        hotelRates.setRoomCount(3);
        hotelRates.setAltAcco(true);
        hotelRates.setListingType("entire");
        hotelRates.setSellableUnit("notRoom");
        hotelRates.setStayTypeWithSizeBed("Deluxe Room");
        hotelRates.setStayTypeWithSize("Room");
        Tuple<String, String> guestKeyValueTuple = new Tuple<>("Guests", "Guests");

        // OHS experiment enabled and country is DOM_COUNTRY
        boolean isOHSExpEnable = true;
        boolean isIHAltAccoNodesExp = true;
        String countryCode = "IN";
        String result = (String) org.springframework.test.util.ReflectionTestUtils.invokeMethod(
                availRoomsResponseTransformerDesktop,
                "buildGuestRoomValue",
                isOHSExpEnable, hotelRates, countryCode, isIHAltAccoNodesExp
        );
        Assert.assertEquals("Hostel", result);

        // OHS experiment disabled, pluralization logic
        isOHSExpEnable = false;
        result = (String) org.springframework.test.util.ReflectionTestUtils.invokeMethod(
                availRoomsResponseTransformerDesktop,
                "buildGuestRoomValue",
                isOHSExpEnable, hotelRates, countryCode, isIHAltAccoNodesExp
        );
        Assert.assertTrue(result.startsWith("3 "));
        Assert.assertTrue(result.contains("Deluxe Room"));

        // Not entire, not plural
        hotelRates.setRoomCount(1);
        hotelRates.setListingType("private");
        result = (String) org.springframework.test.util.ReflectionTestUtils.invokeMethod(
                availRoomsResponseTransformerDesktop,
                "buildGuestRoomValue",
                isOHSExpEnable, hotelRates, countryCode, isIHAltAccoNodesExp
        );
        Assert.assertEquals("Deluxe Room", result);

        // Not India, fallback to value
        countryCode = "US";
        result = (String) org.springframework.test.util.ReflectionTestUtils.invokeMethod(
                availRoomsResponseTransformerDesktop,
                "buildGuestRoomValue",
                isOHSExpEnable, hotelRates, countryCode, isIHAltAccoNodesExp
        );
        Assert.assertEquals("Deluxe Room", result);

        // No StayTypeWithSizeBed, fallback to StayTypeWithSize
        hotelRates.setStayTypeWithSizeBed(null);
        result = (String) org.springframework.test.util.ReflectionTestUtils.invokeMethod(
                availRoomsResponseTransformerDesktop,
                "buildGuestRoomValue",
                isOHSExpEnable, hotelRates, countryCode, isIHAltAccoNodesExp
        );
        Assert.assertEquals("Room", result);
    }

    private com.mmt.hotels.model.response.PayLaterEligibilityResponse buildPayLaterResponse(boolean eligible)
    {
        com.mmt.hotels.model.response.PayLaterEligibilityResponse hes = new com.mmt.hotels.model.response.PayLaterEligibilityResponse();
        hes.setEligible(eligible);
        hes.setCorrelationKey(Constants.CORRELATIONKEY);
        hes.setAmount(1051.20);
        return hes;
    }

    @Test
    public void testConvertPayLaterEligibilityResponse()
    {

        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(true),true));
        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(true),false));
        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(false),true));
        Assert.assertNotNull(availRoomsResponseTransformerDesktop.convertPayLaterEligibilityResponse(Constants.CLIENT,buildPayLaterResponse(false),false));


    }

    @Test
    public void testGetBnplDetails() {
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        String bnplNewVariantText = "Book now @ just Rs. 1";
        String bnplNewVariantSubText = "Pay the remaining amount using any paymode any time before 19 Jun";
        roomTypeDetails.setBnplNewVariantText(bnplNewVariantText);
        roomTypeDetails.setBnplNewVariantSubText(bnplNewVariantSubText);
        roomTypeDetails.setTotalDisplayFare(new DisplayFare());
        roomTypeDetails.getTotalDisplayFare().setIsBNPLApplicable(true);
        BNPLDetails bnplDetails = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getBnplDetails", roomTypeDetails, true);
        Assert.assertNotNull(bnplDetails);
        Assert.assertEquals(bnplNewVariantText, bnplDetails.getBnplNewVariantText());
        Assert.assertNotNull(bnplNewVariantSubText, bnplDetails.getBnplNewVariantSubText());
    }

    @Test
    public void buildHotelPersuasionsTest() {

        HotelRates hotelrates = new HotelRates();
        hotelrates.setRoomTypeDetails(new RoomTypeDetails());
        hotelrates.getRoomTypeDetails().setRoomType(new HashMap<>());

        RatePlan ratePlan2 = new RatePlan();
        ratePlan2.setMpFareHoldStatus(new MpFareHoldStatus());
        ratePlan2.getMpFareHoldStatus().setExpiry(new Long(123456));
        ratePlan2.getMpFareHoldStatus().setHoldEligible(true);
        ratePlan2.getMpFareHoldStatus().setHoldEligible(true);
        ratePlan2.getMpFareHoldStatus().setBookingAmount(0.0f);
        RoomType roomType = new RoomType();
        roomType.setRatePlanList(new HashMap<>());
        roomType.getRatePlanList().put("xyz",ratePlan2);
        hotelrates.getRoomTypeDetails().getRoomType().put("abc",roomType);

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);

        when(polyglotService.getTranslatedData(Mockito.anyString())).thenReturn("test");
        Map<String, PersuasionResponse> hotelPersuasions = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "buildHotelPersuasions", hotelrates , true, false, commonModifierResponse, "TEST_ORGANIZATION");
        Assert.assertNotNull(hotelPersuasions.get(Constants.BOOK_NOW_PERSUASION_KEY));
        hotelPersuasions = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "buildHotelPersuasions", hotelrates , false, true, commonModifierResponse, "TEST_ORGANIZATION");
        Assert.assertNotNull(hotelPersuasions.get(Constants.GST_ASSURED_PERSUASION_KEY));
    }


    @Test
    public void getWarningTextForBookNowDetails_Test_return_empty_string() throws Exception {
        HotelRates hotelRates = new HotelRates();
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setRoomType(new HashMap<>());
        hotelRates.setRoomTypeDetails(roomTypeDetails);

        String resp = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getWarningTextForBookNowDetails" , hotelRates);
        assertEquals("", resp);
    }

    @Test
    public void getWarningTextForBookNowDetails_Test_return_proper() throws Exception {
        HotelRates hotelRates = new HotelRates();
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        Map<String, RoomType> roomTypeMap = new HashMap<>();
        RoomType roomType = new RoomType();
        Map<String, RatePlan> ratePlanMap = new HashMap<>();
        RatePlan ratePlan = new RatePlan();
        MpFareHoldStatus mpFareHoldStatus = new MpFareHoldStatus();
        mpFareHoldStatus.setHoldWarningText("warningText");
        ratePlan.setMpFareHoldStatus(mpFareHoldStatus);
        ratePlanMap.put("KEY", ratePlan);
        roomType.setRatePlanList(ratePlanMap);
        roomTypeMap.put("KEY11", roomType);
        roomTypeDetails.setRoomType(roomTypeMap);
        hotelRates.setRoomTypeDetails(roomTypeDetails);

        String resp = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getWarningTextForBookNowDetails" , hotelRates);
        assertEquals("warningText", resp);
    }

    @Test
    public void getTcsInfoCard_testForGCC() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "INTL");
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getTcsInfoCard", commonModifierResponse, "IN", "IN", true, PaymentMode.PAS);
        MDC.clear();
    }

    @Test
    public void getTcsInfoCard_testForNonGCC() {
        CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
        ExtendedUser user=new ExtendedUser();
        user.setProfileType("CTA");
        user.setAffiliateId("MYPARTNER");
        commonModifierResponse.setExtendedUser(user);
        MDC.put(MDCHelper.MDCKeys.COUNTRY.getStringValue(), "INTL");
        MDC.put(MDCHelper.MDCKeys.REGION.getStringValue(), "IN");
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getTcsInfoCard", commonModifierResponse, "INTL", "CAN", true, PaymentMode.PAS);
        MDC.clear();
    }

    @Test
    public void isBusExclusiveRateAvailableInRatePlanTest(){
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        RoomType roomType = new RoomType();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setBusExclusiveRateAvailable(true);
        roomType.setRatePlanList(new HashMap<>());
        roomType.getRatePlanList().put("123",ratePlan);
        roomTypeDetails.setRoomType(new HashMap<>());
        roomTypeDetails.getRoomType().put("45",roomType);

        assertTrue(ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "isBusExclusiveRateAvailableInRatePlan",roomTypeDetails));
        ratePlan.setBusExclusiveRateAvailable(false);

        assertFalse(ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "isBusExclusiveRateAvailableInRatePlan",roomTypeDetails));
    }


    @Test
    public void isTrainExclusiveRateAvailableInRatePlanTest(){
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        RoomType roomType = new RoomType();
        RatePlan ratePlan = new RatePlan();
        ratePlan.setTrainExclusiveRateAvailable(true);
        roomType.setRatePlanList(new HashMap<>());
        roomType.getRatePlanList().put("123",ratePlan);
        roomTypeDetails.setRoomType(new HashMap<>());
        roomTypeDetails.getRoomType().put("45",roomType);

        assertTrue(ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "isTrainExclusiveRateAvailableInRatePlan",roomTypeDetails));
        ratePlan.setTrainExclusiveRateAvailable(false);

        assertFalse(ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "isTrainExclusiveRateAvailableInRatePlan",roomTypeDetails));
    }

    @Test
    public void testGetCorpTravellerDetails() {
        // Setup test data
        CorpTravellerDetails travellerDetailsHES = new CorpTravellerDetails();
        travellerDetailsHES.setAllowedToInviteUser(true);
        travellerDetailsHES.setEditTravellerFlowEnabled(true);

        // Test with empty recently booked travellers
        CorpTravellerInfo result = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,
                "getCorpTravellerDetails", travellerDetailsHES);

        // Verify results
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isAllowedToInviteUser());
        Assert.assertTrue(result.isEditTravellerFlowEnabled());
        Assert.assertNull(result.getRecentlyBooked());

        // Setup recently booked travellers
        List<RecentTravellerDetails> recentlyBookedTravellers = new ArrayList<>();
        RecentTravellerDetails traveller1 = new RecentTravellerDetails();
        traveller1.setEmail("<EMAIL>");
        traveller1.setFirstName("John");
        traveller1.setLastName("Doe");
        traveller1.setGender("Male");
        traveller1.setMobileNumber("1234567890");

        recentlyBookedTravellers.add(traveller1);
        travellerDetailsHES.setRecentlyBookedTravellers(recentlyBookedTravellers);

        // Mock polyglot service for title translation
        when(polyglotService.getTranslatedData(ConstantsTranslation.RECENTLY_ADDED_GUESTS_TITLE))
                .thenReturn("Recently Added Guests");

        // Test with recently booked travellers
        result = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,
                "getCorpTravellerDetails", travellerDetailsHES);

        // Verify results
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isAllowedToInviteUser());
        Assert.assertTrue(result.isEditTravellerFlowEnabled());
        Assert.assertNotNull(result.getRecentlyBooked());
        Assert.assertEquals("Recently Added Guests", result.getRecentlyBooked().getTitle());
        Assert.assertEquals(1, result.getRecentlyBooked().getTravellers().size());
    }

    @Test
    public void testGetRecentTravellerItems() {
        CorpTravellerDetails travellerDetailsHES = new CorpTravellerDetails();

        List<RecentTravellerDetails> recentlyBookedTravellers = new ArrayList<>();

        RecentTravellerDetails traveller1 = new RecentTravellerDetails();
        traveller1.setEmail("<EMAIL>");
        traveller1.setFirstName("John");
        traveller1.setLastName("Doe");
        traveller1.setGender("Male");
        traveller1.setMobileNumber("1234567890");

        RecentTravellerDetails traveller2 = new RecentTravellerDetails();
        traveller2.setEmail("<EMAIL>");
        traveller2.setFirstName("Jane");
        traveller2.setLastName("Smith");
        traveller2.setGender("Female");
        traveller2.setMobileNumber("9876543210");

        recentlyBookedTravellers.add(traveller1);
        recentlyBookedTravellers.add(traveller2);

        travellerDetailsHES.setRecentlyBookedTravellers(recentlyBookedTravellers);

        // Test the method
        List<RecentTravellerItem> result = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,
                "getRecentTravellerItems", travellerDetailsHES);

        // Verify results
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());

        // Verify first traveller
        RecentTravellerItem item1 = result.get(0);
        Assert.assertEquals("<EMAIL>", item1.getEmail());
        Assert.assertEquals("John", item1.getFirstName());
        Assert.assertEquals("Doe", item1.getLastName());
        Assert.assertEquals("Male", item1.getGender());
        Assert.assertEquals("1234567890", item1.getMobileNumber());

        // Verify second traveller
        RecentTravellerItem item2 = result.get(1);
        Assert.assertEquals("<EMAIL>", item2.getEmail());
        Assert.assertEquals("Jane", item2.getFirstName());
        Assert.assertEquals("Smith", item2.getLastName());
        Assert.assertEquals("Female", item2.getGender());
        Assert.assertEquals("9876543210", item2.getMobileNumber());
    }

    @Test
    public void testGetRecentTravellerItems_EmptyList() {
        // Setup test data with empty list
        CorpTravellerDetails travellerDetailsHES = new CorpTravellerDetails();
        travellerDetailsHES.setRecentlyBookedTravellers(new ArrayList<>());

        // Test the method
        List<RecentTravellerItem> result = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,
                "getRecentTravellerItems", travellerDetailsHES);

        // Verify results
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testGetRecentTravellerItems_NullList() {
        // Setup test data with null list
        CorpTravellerDetails travellerDetailsHES = new CorpTravellerDetails();
        travellerDetailsHES.setRecentlyBookedTravellers(null);

        // Test the method
        List<RecentTravellerItem> result = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,
                "getRecentTravellerItems", travellerDetailsHES);

        // Verify results
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void populateBenefitDealsTest(){
        availRoomsResponseTransformerDesktop.populateBenefitDeals(null, null,false,new ArrayList<>(),0.0,0, true);
        availRoomsResponseTransformerDesktop.populateBenefitDeals(null, new AvailRoomsResponse(),false,new ArrayList<>(),0.0,0, true);
        availRoomsResponseTransformerDesktop.populateBenefitDeals(new RoomTypeDetails(), new AvailRoomsResponse(),false,new ArrayList<>(),0.0,0, true);
        AvailRoomsResponse availRoomsResponse = new AvailRoomsResponse();
        availRoomsResponse.setTotalpricing(new com.mmt.hotels.clientgateway.response.TotalPricing());
        availRoomsResponse.getTotalpricing().setPricingKey("123");
        availRoomsResponse.getTotalpricing().setCurrency("INR");
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setTotalDisplayFare(new DisplayFare());
        roomTypeDetails.getTotalDisplayFare().setForexCoupons(new ArrayList<>());
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().add(new BestCoupon());
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setCouponCode("MMTIHFOREX");
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setPromoIconLink("abc");
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setDescription("enjoy 300 off");
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setForexCashbackAmount(300);
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setForexDealMessage("Free forex for your deal");
        availRoomsResponseTransformerDesktop.populateBenefitDeals(new RoomTypeDetails(),availRoomsResponse,true,new ArrayList<>(),0.0,0, true);
        Assert.assertNull(availRoomsResponse.getTotalpricing().getBenefitDeals());
        List<String> hydraSegments = new ArrayList<>();
        hydraSegments.add("r2021");hydraSegments.add("r2022");
        availRoomsResponseTransformerDesktop.populateBenefitDeals(roomTypeDetails,availRoomsResponse,true,hydraSegments,0.0,0, true);
        Assert.assertNotNull(availRoomsResponse.getTotalpricing().getBenefitDeals());
        Assert.assertEquals(1,availRoomsResponse.getTotalpricing().getBenefitDeals().getDealCoupons().size());
    }

    @Test
    public void populateForexCouponsTest(){
        List<Coupon> forexCoupons = availRoomsResponseTransformerDesktop.populateForexCoupons(null,false,0.0,0, true);
        Assert.assertEquals(0,forexCoupons.size());
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        roomTypeDetails.setTotalDisplayFare(new DisplayFare());
        roomTypeDetails.getTotalDisplayFare().setForexCoupons(new ArrayList<>());
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().add(new BestCoupon());
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setCouponCode("MMTIHFOREX");
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setPromoIconLink("abc");
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setDescription("enjoy 300 off");
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setForexCashbackAmount(300);
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setDiscountAmount(123.0);
        roomTypeDetails.getTotalDisplayFare().getForexCoupons().get(0).setForexDealMessage("Free forex for your deal");
        forexCoupons = availRoomsResponseTransformerDesktop.populateForexCoupons(roomTypeDetails,false,0.0,0, true);
        Assert.assertEquals(1,forexCoupons.size());
        Assert.assertEquals("Free forex for your deal",forexCoupons.get(0).getTitle());
        Assert.assertEquals(300.0,forexCoupons.get(0).getCouponAmount());
        Assert.assertNull(forexCoupons.get(0).getPersuasionText());
    }


    @Test
    public void buildFormUrlForReviewTest(){
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setVisitNumber(2);
        requestDetails.setVisitorId("XYZ");
        requestDetails.setFunnelSource("HOTELS");
        availRoomsRequest.setRequestDetails(requestDetails);
        SupportDetails supportDetails = new SupportDetails();
        supportDetails.setFormUrl("https://www.makemytrip.com/hotels/group-booking/?checkin={0}&checkout={1}&city={2}&country={3}&locusId={4}&locusType={5}&rsc={6}&_uCurrency={7}&appVersion={8}&deviceId={9}&bookingDevice={10}&deviceType={11}&visitorId={12}&visitNumber={13}&funnelSource={14}&idContext={15}&funnelName={16}&propertyType={17}&hotelName={18}");
        String res = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,"buildFormUrlForReview",availRoomsRequest,supportDetails,"Resort","The Taj");
        org.junit.Assert.assertEquals("https://www.makemytrip.com/hotels/group-booking/?checkin=&checkout=&city=&country=IN&locusId=&locusType=city&rsc=&_uCurrency=INR&appVersion=&deviceId=&bookingDevice=&deviceType=&visitorId=XYZ&visitNumber=2&funnelSource=HOTELS&idContext=null&funnelName=HOTELS&propertyType=Resort&hotelName=The+Taj",res);
    }
    @Test
    public void convertFetchLocationsResponseTest(){
        when(mockResponseBody.getCorrelationKey()).thenReturn("correlationKey");
        FetchLocationsResult result1 = new FetchLocationsResult();
        result1.setName("Location 1");
        result1.setLocId("1");
        result1.setPostalCode("12345");

        FetchLocationsResult result2 = new FetchLocationsResult();
        result2.setName("Location 2");
        result2.setLocId("2");
        result2.setPostalCode("67890");

        List<FetchLocationsResult> resultList = Arrays.asList(result1, result2);
        when(mockResponseBody.getResult()).thenReturn(resultList);

        FetchLocationsResponse response = availRoomsResponseTransformerDesktop.convertFetchLocationsResponse(mockResponseBody);

        assertNotNull(response);
        assertEquals("correlationKey", response.getRequestId());
        List<LocationDetail> locations = response.getLocations();
        assertNotNull(locations);
        assertEquals(2, locations.size());

        LocationDetail location1 = locations.get(0);
        assertEquals("Location 1", location1.getName());
        assertEquals("1", location1.getId());
        assertEquals("12345", location1.getPostalCode());

        LocationDetail location2 = locations.get(1);
        assertEquals("Location 2", location2.getName());
        assertEquals("2", location2.getId());
        assertEquals("67890", location2.getPostalCode());
    }
    @Test
    public void convertFetchLocationsResponseTestNull(){
        FetchLocationsResponse response = availRoomsResponseTransformerDesktop.convertFetchLocationsResponse(null);
        assertNull(response);
    }

    @Test
    public void convertAvailRoomsResponse_testNullRequest() throws IOException {
        RoomDetailsResponse roomDetailsResponse = new Gson().fromJson(
                FileUtils.readFileToString(ResourceUtils.getFile("classpath:review/reviewdata.json")),
                RoomDetailsResponse.class);

        CommonModifierResponse commonModifierResponse=new CommonModifierResponse();
        commonModifierResponse.setExpDataMap(new LinkedHashMap<>());
        commonModifierResponse.getExpDataMap().put(MYPARTNER_EXCLUSIVE_DEAL,"true");
//        String countryCode = persistedData.getAvailReqBody().getCountryCode();
//        String siteDomain = persistedData.getAvailReqBody().getSiteDomain();
//        String cityCode = persistedData.getAvailReqBody().getCityCode();
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setSearchCriteria(new AvailPriceCriteria());
        availRoomsRequest.getSearchCriteria().setRoomCriteria(new ArrayList<>());
        availRoomsRequest.getSearchCriteria().setCountryCode("AE");
        availRoomsRequest.getSearchCriteria().getRoomCriteria().add(new AvailRoomsSearchCriteria());
        
        // Add DeviceDetails to prevent NullPointerException in host calling logic
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("DESKTOP");
        availRoomsRequest.setDeviceDetails(deviceDetails);

        AvailRoomsResponse availRoomsResponse = availRoomsResponseTransformerDesktop.convertAvailRoomsResponse(availRoomsRequest,roomDetailsResponse, null,"", "", null, "{\"ratePlanRedesign\":\"true\"}", null, false, Mockito.anyString(),commonModifierResponse,false,"desktop");
        Assert.assertNotNull(availRoomsResponse);
        Assert.assertNotNull(availRoomsResponse);
    }

    @Test
    public void makeUpsellOptionsReturnsCorrectUpsellOptions() {
        UpsellOptions pricingUpsellOption = new UpsellOptions();
        pricingUpsellOption.setMtKey("testMtKey");
        pricingUpsellOption.setRoomCode("testRoomCode");
        pricingUpsellOption.setRatePlanCode("testRatePlanCode");
        pricingUpsellOption.setDisplayText("testDisplayText");
        pricingUpsellOption.setSuccessDisplayText("testSuccessDisplayText");
        pricingUpsellOption.setFailureDisplayText("testFailureDisplayText");
        pricingUpsellOption.setDisplaySubText("testDisplaySubText");
        pricingUpsellOption.setDescriptionText("testDescriptionText");
        List<com.mmt.hotels.model.response.pricing.UpsellOptions> list = new ArrayList<>();
        list.add(pricingUpsellOption);
        List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> resp = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,"makeUpsellOptions",list);
        Assert.assertEquals(1,resp.size());
    }

    @Test
    public void getBlackBenefitsReturnsBlackBenefitsTest() {
        HotelRates hotelRates = new HotelRates();
        RoomTypeDetails roomTypeDetails = new RoomTypeDetails();
        hotelRates.setRoomTypeDetails(roomTypeDetails);
        Map<String, RoomType> roomTypeMap = new HashMap<>();
        RoomType roomType = new RoomType();
        Map<String, RatePlan> ratePlanMap = new HashMap<>();
        RatePlan ratePlan = new RatePlan();
        BlackBenefits blackBenefits = new BlackBenefits();
        ratePlan.setBlackBenefits(blackBenefits);
        ratePlanMap.put("rpc123",ratePlan);
        hotelRates.getRoomTypeDetails().setRoomType(new HashMap<>());
        roomType.setRatePlanList(ratePlanMap);
        hotelRates.getRoomTypeDetails().getRoomType().put("rpc1234",roomType);
        BlackBenefits blackBenefitsResponse = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "getBlackBenefits", hotelRates);
        Assert.assertNotNull(blackBenefitsResponse);
    }

    @Test
    public void testSetGroupBookingParamsInHotelInfo() {
        HotelRates hotelRates = new HotelRates();
        hotelRates.setGroupBookingHotel(true);
        hotelRates.setGroupBookingPrice(true);
        hotelRates.setMaskedPrice(true);
        String funnelSource = "group";
        HotelResult hotelResult = new HotelResult();
        boolean groupFunnelEnhancement = true;

        // Act
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setGroupBookingParamsInHotelInfo", hotelRates, funnelSource, hotelResult, false);
        assertTrue(hotelResult.getGroupBookingPrice());
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setGroupBookingParamsInHotelInfo", hotelRates, funnelSource, hotelResult, true);
        assertFalse(hotelResult.getGroupBookingPrice());
    }


    @Test
    public void buildRoomCriteriaTest() {
        List<RoomCriterion> roomCriteria = new ArrayList<>();
        RoomCriterion roomCriteriaObj = new RoomCriterion();
        roomCriteriaObj.setRatePlanCode("RPC123");
        roomCriteriaObj.setRoomCode("RC123");
        roomCriteriaObj.setMtKey("MT123");
        List<RoomStayCandidate> roomStayCandidate = new ArrayList<>();
        RoomStayCandidate roomStayCand = new RoomStayCandidate();
        roomStayCand.setAdultCount(2);
        List<Integer> list = new ArrayList<>();
        list.add(3);
        roomStayCand.setChildAges(list);
        roomStayCandidate.add(roomStayCand);
        roomCriteriaObj.setRoomStayCandidates(roomStayCandidate);
        roomCriteria.add(roomCriteriaObj);
        List<AvailRoomsSearchCriteria> result = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "buildRoomCriteria", roomCriteria);
        Assert.assertNotNull(result);
        Assert.assertEquals(result.get(0).getRoomCode(), "RC123");
        Assert.assertEquals(result.get(0).getMtKey(), "MT123");
        Assert.assertEquals(result.get(0).getRatePlanCode(), "RPC123");
        Assert.assertEquals(result.get(0).getRoomStayCandidates().get(0).getAdultCount(), new Integer(2));
    }

    @Test
    public void buildRoomCriteriaTest_FailCase() {
        List<RoomCriterion> list = null;
        List<AvailRoomsSearchCriteria> result = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "buildRoomCriteria", list);
        Assert.assertNull(result);
    }

    @Test
    public void testBuildAppInstallStrip() {
        // Arrange
        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        RequestDetails requestDetails = new RequestDetails();
        requestDetails.setIdContext("B2C");
        availRoomsRequest.setRequestDetails(requestDetails);
        DeviceDetails deviceDetails = new DeviceDetails();
        deviceDetails.setBookingDevice("PWA");
        availRoomsRequest.setDeviceDetails(deviceDetails);

     //   when(polyglotService.getTranslatedData(ConstantsTranslation.APP_INSTALL_TEXT)).thenReturn("Install App");
    //    when(polyglotService.getTranslatedData(ConstantsTranslation.APP_INSTALL_BUTTON_TEXT)).thenReturn("Install");

        // Act
        AppInstallStrip appInstallStrip = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,"buildAppInstallStrip",availRoomsRequest);

        // Assert
        //assertNotNull(appInstallStrip);
//        assertEquals("Install App", appInstallStrip.getText());
//        assertEquals("Install", appInstallStrip.getButtonText());
//        assertEquals("deeplink", appInstallStrip.getDeeplink());
//
//        deviceDetails.setBookingDevice("DESKTOP");
//        availRoomsRequest.setDeviceDetails(deviceDetails);
//        assertNull(ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop,"buildAppInstallStrip",availRoomsRequest));
    }

    @Test
    public void buildLosInclusionTest(){
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        List<BookedInclusion> inclusions = new ArrayList<>();
        BookedInclusion inclusion1 = new BookedInclusion();
        inclusion1.setInclusionCode("LONGSTAY");
        inclusion1.setText("los val");
        BookedInclusion inclusion2 = new BookedInclusion();
        inclusion1.setInclusionCode("LONGSTAY");
        inclusion2.setText("los val");
        BookedInclusion inclusion3 = new BookedInclusion();
        inclusion3.setType("ABC");
        inclusion3.setText("los val");
        inclusions.addAll(Arrays.asList(inclusion1,inclusion2,inclusion3));
        ratePlan.setInclusionsList(inclusions);

        // Discoun inclusion
        BookedInclusion losDiscountInclusion = new BookedInclusion();
        losDiscountInclusion.setText("los discount code");
        ratePlan.setLosDiscountInclusion(losDiscountInclusion);

        BookedInclusion res = ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "buildLosInclusion", ratePlan);
        Assert.assertNotNull(res);
        Assert.assertEquals("LONG_STAY_BENEFITS_HEADING los discount code , los val",res.getText());
    }

    @Test
    public void reorderUpsellOptions_shouldGroupByAddOnType() {
        List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
        com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions option1 = new com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions();
        option1.setAddOnType("TypeA");
        com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions option2 = new com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions();
        option2.setAddOnType("TypeB");
        com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions option3 = new com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions();
        option3.setAddOnType("TypeA");
        upsellOptionsList.add(option1);
        upsellOptionsList.add(option2);
        upsellOptionsList.add(option3);

        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "reorderUpsellOptions", upsellOptionsList);

        assertEquals(3, upsellOptionsList.size());
        assertEquals("TypeA", upsellOptionsList.get(0).getAddOnType());
        assertEquals("TypeA", upsellOptionsList.get(1).getAddOnType());
        assertEquals("TypeB", upsellOptionsList.get(2).getAddOnType());
    }

    @Test
    public void reorderUpsellOptions_shouldHandleEmptyList() {
        List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();

        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "reorderUpsellOptions", upsellOptionsList);

        assertTrue(upsellOptionsList.isEmpty());
    }

    @Test
    public void reorderUpsellOptions_shouldHandleSingleElementList() {
        List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
        com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions option = new com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions();
        option.setAddOnType("TypeA");
        upsellOptionsList.add(option);

        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "reorderUpsellOptions", upsellOptionsList);

        assertEquals(1, upsellOptionsList.size());
        assertEquals("TypeA", upsellOptionsList.get(0).getAddOnType());
    }

    @Test
    public void reorderUpsellOptions_shouldHandleMultipleTypes() {
        List<com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions> upsellOptionsList = new ArrayList<>();
        com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions option1 = new com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions();
        option1.setAddOnType("TypeA");
        com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions option2 = new com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions();
        option2.setAddOnType("TypeB");
        com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions option3 = new com.mmt.hotels.clientgateway.response.availrooms.UpsellOptions();
        option3.setAddOnType("TypeC");
        upsellOptionsList.add(option1);
        upsellOptionsList.add(option2);
        upsellOptionsList.add(option3);

        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "reorderUpsellOptions", upsellOptionsList);

        assertEquals(3, upsellOptionsList.size());
        assertEquals("TypeA", upsellOptionsList.get(0).getAddOnType());
        assertEquals("TypeB", upsellOptionsList.get(1).getAddOnType());
        assertEquals("TypeC", upsellOptionsList.get(2).getAddOnType());
    }

    @Test
    public void testBuildSpecialFareTagPersuasion_withCorpAlias() {
        String corpAlias = "TestCorp";
        PersuasionResponse response = availRoomsResponseTransformerDesktop.buildSpecialFareTagPersuasion(corpAlias);

        assertEquals(SPECIAL_FARE_TAG_SMALL_STYLE, response.getStyle().getStyleClass());
        assertEquals("SPECIAL_FARE_TAG", response.getTitle());
    }

    @Test
    public void testBuildSpecialFareTagPersuasion_withoutCorpAlias() {
        PersuasionResponse response = availRoomsResponseTransformerDesktop.buildSpecialFareTagPersuasion(null);

        assertEquals(SPECIAL_FARE_TAG_SMALL_STYLE, response.getStyle().getStyleClass());
        assertEquals("SPECIAL_FARE_TAG", response.getTitle());
    }

    @Test
    public void testBuildLoyaltyCashbackPersuasions() {
        availRoomsResponseTransformerDesktop.buildLoyaltyCashbackPersuasions(coupon, persuasionMap);

        verify(persuasionUtil).buildLoyaltyCashbackPersuasions(coupon, persuasionMap);
    }

    @Test
    public void testBuildCardsMap_withValidData() { // Setup test data
        RoomDetailsResponse roomDetailsResponse = new RoomDetailsResponse();
        roomDetailsResponse.setCardDataMap(new HashMap<>());

        AvailRoomsRequest availRoomsRequest = new AvailRoomsRequest();
        availRoomsRequest.setDeviceDetails(new DeviceDetails());
        availRoomsRequest.getDeviceDetails().setBookingDevice("DESKTOP");

        Map<String, String> expDataMap = new HashMap<>();
        expDataMap.put("someKey", "someValue");

        String siteDomain = "example.com";
        String cabsDeepLinkUrl = "http://example.com";
        boolean bookingDeviceDesktop = true;

        // Mock internal method calls
//        when(utility.isIHFunnel(anyString(), anyString())).thenReturn(true);
//        when(utility.isExperimentOn(anyMap(), anyString())).thenReturn(true);
        when(mobConfigHelper.convertCardDataToCardInfoMap(anyMap())).thenReturn(new HashMap<>());

        // Call the method
        Map<String, CardInfo> result = availRoomsResponseTransformerDesktop.buildCardsMap(
                roomDetailsResponse, availRoomsRequest, siteDomain, expDataMap, cabsDeepLinkUrl
        );

        // Verify the interactions and assert the results
        assertNotNull(result);
        verify(mobConfigHelper, times(1)).convertCardDataToCardInfoMap(anyMap());

        // Additional assertions to cover internal method logic
        assertTrue(result.isEmpty()); // Assuming the mock returns an empty map

        // Test with different data to cover more branches
        expDataMap.put("anotherKey", "anotherValue");
//        when(utility.isIHFunnel(anyString(), anyString())).thenReturn(false);
//        when(utility.isExperimentOn(anyMap(), anyString())).thenReturn(false);

        result = availRoomsResponseTransformerDesktop.buildCardsMap(
                roomDetailsResponse, null, siteDomain, expDataMap, cabsDeepLinkUrl
        );

        assertNotNull(result);
        verify(mobConfigHelper, times(2)).convertCardDataToCardInfoMap(anyMap());
    }

    @Test
    public void testBuildForexAndCabCardPayload_withValidData() {
        // Setup test data
        String cityCode = "CTDUB";
        String cabsDeepLinkUrl = "http://example.com";
        boolean bookingDeviceDesktop = true;
        String pageContext = "context";


        // Mock utility methods
        when(utility.isExperimentOn(expDataMap, "mmt.backend.hotel.default.default.default.cabCard")).thenReturn(true);

        when(utility.isExperimentOn(expDataMap, "mmt.backend.hotel.default.default.default.forexCard")).thenReturn(true);

        // Call the method
        CardPayloadData result = commonResponseTransformer.buildForexAndCabCardPayload(expDataMap, cityCode, cabsDeepLinkUrl, bookingDeviceDesktop, pageContext);

        // Verify the interactions and assert the results
        assertNotNull(result);
        List<GenericCardPayloadDataCG> genericCardData = result.getGenericCardData();
        assertNotNull(genericCardData);
    }
    @Test
    public void testIsB2CFunnel_withDefaultCase() {
        doReturn(true).when(utility).isB2CFunnel();
        assertTrue(utility.isB2CFunnel("ANY_CONTEXT"));
    }

    @Test
    public void testBuildForexAndCabCard_withValidData() {
        // Arrange
        when(polyglotService.getTranslatedData("FOREX_CAB_CARD_TITLE_REVIEW_PAGE")).thenReturn("Forex & Cab Card Title");
        when(polyglotService.getTranslatedData("FOREX_CAB_CARD_SUBTEXT_REVIEW_PAGE")).thenReturn("Forex & Cab Card Subtext");
//        when(commonResponseTransformer.buildForexAndCabCardPayload(expDataMap, cityCode, cabsDeepLink,bookingDeviceDesktop , "PAGE_CONTEXT_REVIEW"))
//                .thenReturn(new CardPayloadData());

        // Mock utility methods
        when(utility.isExperimentOn(expDataMap, "mmt.backend.hotel.default.default.default.cabCard")).thenReturn(true);

        when(utility.isExperimentOn(expDataMap, "mmt.backend.hotel.default.default.default.forexCard")).thenReturn(true);

        // Act
        Map<String, CardInfo> result = availRoomsResponseTransformerDesktop.buildForexAndCabCard(expDataMap, cityCode, cabsDeepLink, bookingDeviceDesktop);

        // Assert
        assertNotNull(result);
        org.junit.Assert.assertEquals(1, result.size());
        CardInfo cardInfo = result.get("FOREX_CAB_CARD_ID");
 }

    @Test
    public void testGetTopTagTitleColor_OnlyForToday() {
        String result = availRoomsResponseTransformerDesktop.getTopTagTitleColor(HotelTagType.ONLY_FOR_TODAY);
        assertEquals("#4A4A4A", result);
    }

    @Test
    public void testGetTopTagTitleColor_OtherType() {
        String result = availRoomsResponseTransformerDesktop.getTopTagTitleColor(HotelTagType.VALUE_STAYS);
        assertNull(result);
    }

    @Test
    public void testGetTopTagBackground_OnlyForToday() {
        String result = availRoomsResponseTransformerDesktop.getTopTagBackground(HotelTagType.ONLY_FOR_TODAY);
        assertEquals("#E6FFF9", result);
    }

    @Test
    public void testGetTopTagBackground_OtherType() {
        String result = availRoomsResponseTransformerDesktop.getTopTagBackground(HotelTagType.VALUE_STAYS);
        assertNull(result);
    }

    @Test
    public void testGetTimerStyle_OnlyForToday() {
        PersuasionStyle mockStyle = new PersuasionStyle();
        when(commonConfigConsul.getOneDayDealTimerStyleConfig()).thenReturn(mockStyle);

        PersuasionStyle result = availRoomsResponseTransformerDesktop.getTimerStyle(HotelTagType.ONLY_FOR_TODAY);
        assertEquals(mockStyle, result);
    }

    @Test
    public void testGetTimerStyle_OtherType() {
        PersuasionStyle result = availRoomsResponseTransformerDesktop.getTimerStyle(HotelTagType.VALUE_STAYS);
        assertNull(result);
    }

    @Test
    public void testSetCanTranslateFlag_WhenSupplierCodeInEnabledList_ShouldSetTrue() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode("SUPPLIER_A");
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B", "SUPPLIER_C");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertTrue(ratePlan.isCanTranslate());
    }
    
    @Test
    public void testSetCanTranslateFlag_WhenSupplierCodeNotInEnabledList_ShouldSetFalse() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode("SUPPLIER_D");
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B", "SUPPLIER_C");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertFalse(ratePlan.isCanTranslate());
    }
    
    @Test
    public void testSetCanTranslateFlag_WhenEnabledListIsEmpty_ShouldSetFalse() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode("SUPPLIER_A");
        List<String> enabledSupplierCodes = Collections.emptyList();
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertFalse(ratePlan.isCanTranslate());
    }
    
    @Test
    public void testSetCanTranslateFlag_WhenEnabledListIsNull_ShouldSetFalse() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode("SUPPLIER_A");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", null);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertFalse(ratePlan.isCanTranslate());
    }
    
    @Test
    public void testSetCanTranslateFlag_WhenSupplierCodeIsNull_ShouldSetFalse() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode(null);
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertFalse(ratePlan.isCanTranslate());
    }
    
    @Test
    public void testSetCanTranslateFlag_WhenSupplierCodeIsEmpty_ShouldSetFalse() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode("");
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertFalse(ratePlan.isCanTranslate());
    }
    
    @Test
    public void testSetCanTranslateFlag_WhenRatePlanIsNull_ShouldNotThrowException() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = null;
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When/Then - Should not throw exception
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        // If we reach this point without exception, test passes
    }
    
    @Test
    public void testSetCanTranslateFlag_CaseSensitiveMatch_ShouldSetTrue() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode("supplier_A");
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "supplier_A", "SUPPLIER_B");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertTrue(ratePlan.isCanTranslate());
    }
    
    @Test
    public void testSetCanTranslateFlag_CaseMismatch_ShouldSetFalse() {
        // Given
        com.mmt.hotels.clientgateway.response.rooms.RatePlan ratePlan = new com.mmt.hotels.clientgateway.response.rooms.RatePlan();
        ratePlan.setSuppliercode("supplier_a");
        List<String> enabledSupplierCodes = Arrays.asList("SUPPLIER_A", "SUPPLIER_B");
        ReflectionTestUtils.setField(availRoomsResponseTransformerDesktop, "translateEnabledSupplierCodes", enabledSupplierCodes);
        
        // When
        ReflectionTestUtils.invokeMethod(availRoomsResponseTransformerDesktop, "setCanTranslateFlag", ratePlan);
        
        // Then
        assertFalse(ratePlan.isCanTranslate());
    }
}
