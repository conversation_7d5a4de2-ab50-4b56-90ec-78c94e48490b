package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.FilterPillConfigurationWrapper;
import com.mmt.hotels.clientgateway.response.filter.SortList;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.FilterPillConfig;
import com.mmt.hotels.model.request.AccessPoint;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.FUNNEL_SOURCE_GETAWAY;
import static com.mmt.hotels.clientgateway.constants.Constants.LUXE_LOCATION_TYPE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SORT_CRITERIA_ACCESS_POINT_TITLE;


@RunWith(MockitoJUnitRunner.class)
public class FilterPillFactoryTest {

    @InjectMocks
    FilterPillFactory filterPillFactory;

    @Mock
    PolyglotService polyglotService;

    FilterPillConfig filterPillsB2C;
    FilterPillConfig filterPillsMyBiz;
    FilterPillConfig filterPillsLuxe;
    FilterPillConfig filterPillsGetaway;

    SortList sortList;
    private SortCriteria sortCriteriaCheapestAndBestReviewed;
    private SortCriteria sortCriteriaMostAndBestReviewed;
    private SortCriteria sortCriteriaHeroPoiDrivingDistance;
    private SortCriteria sortCriteriaBeachCityPoiDrivingDistance;
    Gson gson = new Gson();

    @Before
    public void init() {

        String pillConfigMyBiz = "{\"stickyFilterPills\":{\"SORT\":{\"id\":\"SORT\",\"title\":\"SORT_PILL_TEXT\",\"sequence\":1,\"categories\":[]},\"FILTERS\":{\"id\":\"FILTERS\",\"sequence\":2,\"title\":\"FILTER_PILL_TEXT\",\"categories\":[]}},\"dynamicFilterPills\":{\"PRICE\":{\"id\":\"PRICE\",\"sequence\":3,\"title\":\"PRICE_PILL_TEXT\",\"categories\":[\"PRICE\"]},\"BUSINESS\":{\"id\":\"BUSINESS\",\"sequence\":4,\"title\":\"BUSINESS_PILL_TEXT\",\"categories\":[\"BUSINESS\"]},\"LOCALITY\":{\"id\":\"LOCALITY\",\"sequence\":5,\"title\":\"LOCALITY_PILL_TEXT\",\"categories\":[]},\"STAR_RATING\":{\"id\":\"STAR_RATING\",\"sequence\":6,\"title\":\"STAR_RATING_PILL_TEXT\",\"categories\":[\"STAR_CATEGORY\"]},\"SAVED_FOR_COMPANY\":{\"id\":\"SAVED_FOR_COMPANY\",\"pillType\":\"LOCATION_FILTER\",\"title\":\"SAVED_FOR_COMPANY\",\"icon\":\"icon url\",\"categories\":[\"SAVED_FOR_COMPANY\"]}},\"dynamicFilterPillsDPT\":{\"PRICE\":{\"id\":\"PRICE\",\"sequence\":3,\"title\":\"PRICE_PILL_TEXT\",\"categories\":[\"PRICE\"]},\"MEAL_PREFERENCES\":{\"id\":\"MEAL_PREFERENCES\",\"sequence\":7,\"title\":\"MEAL_PREFERENCES_PILL_TEXT\",\"categories\":[\"MEAL_PREFERENCES\"]},\"POPULAR\":{\"id\":\"POPULAR\",\"sequence\":4,\"title\":\"POPULAR_PILL_TEXT\",\"categories\":[\"POPULAR\"]},\"STAR_CATEGORY\":{\"id\":\"STAR_RATING\",\"sequence\":6,\"title\":\"STAR_RATING_PILL_TEXT\",\"categories\":[\"STAR_CATEGORY\"]},\"USER_RATING\":{\"id\":\"USER_RATING\",\"sequence\":8,\"title\":\"USER_RATING_PILL_TEXT\",\"categories\":[\"USER_RATING_MMT_BRAND\"]},\"USER_RATING_MMT_BRAND\":{\"id\":\"USER_RATING\",\"sequence\":8,\"title\":\"USER_RATING_PILL_TEXT\",\"categories\":[\"USER_RATING_MMT_BRAND\"]},\"LOCALITY\":{\"id\":\"LOCALITY\",\"sequence\":5,\"title\":\"LOCALITY_PILL_TEXT\",\"categories\":[]},\"MERGE_PROPERTY_TYPE\":{\"id\":\"MERGE_PROPERTY_TYPE\",\"sequence\":9,\"title\":\"MERGE_PROPERTY_PILL_TEXT\",\"categories\":[\"MERGE_PROPERTY_TYPE\"]},\"AMENITIES\":{\"id\":\"AMENITIES\",\"sequence\":10,\"title\":\"AMENITIES_PILL_TEXT\",\"categories\":[\"AMENITIES\"]},\"PRICE_BUCKET\":{\"id\":\"PRICE\",\"sequence\":3,\"title\":\"PRICE_PILL_TEXT\",\"categories\":[\"PRICE\"]}}}";
        String pillConfigB2C = "{\"stickyFilterPills\":{\"SORT\":{\"id\":\"SORT\",\"title\":\"SORT_PILL_TEXT\",\"sequence\":1,\"categories\":[]},\"FILTERS\":{\"id\":\"FILTERS\",\"sequence\":2,\"title\":\"FILTER_PILL_TEXT\",\"categories\":[]}},\"dynamicFilterPills\":{\"PRICE\":{\"id\":\"PRICE\",\"sequence\":3,\"title\":\"PRICE_PILL_TEXT\",\"categories\":[\"PRICE\"]},\"POPULAR\":{\"id\":\"POPULAR\",\"sequence\":4,\"title\":\"POPULAR_PILL_TEXT\",\"categories\":[\"POPULAR\"]},\"LOCALITY\":{\"id\":\"LOCALITY\",\"sequence\":5,\"title\":\"LOCALITY_PILL_TEXT\",\"categories\":[]},\"STAR_RATING\":{\"id\":\"STAR_RATING\",\"sequence\":6,\"title\":\"STAR_RATING_PILL_TEXT\",\"categories\":[\"STAR_CATEGORY\"]}}}";
        String pillConfigLuxe = "{\"stickyFilterPills\":{\"SORT\":{\"id\":\"SORT\",\"title\":\"SORT_PILL_TEXT\",\"sequence\":1,\"categories\":[]},\"FILTERS\":{\"id\":\"FILTERS\",\"sequence\":2,\"title\":\"FILTER_PILL_TEXT\",\"categories\":[]}},\"dynamicFilterPills\":{\"PRICE\":{\"id\":\"PRICE\",\"title\":\"PRICE_PILL_TEXT\",\"sequence\":3,\"categories\":[\"PRICE\"]},\"PROPERTY\":{\"id\":\"PROPERTY_TYPE\",\"title\":\"PROPERTY_PILL_TEXT\",\"sequence\":4,\"categories\":[\"MERGE_PROPERTY_TYPE\"]},\"THEMES\":{\"id\":\"EXCLUSIVE_THEMES\",\"title\":\"THEMES_PILL_TEXT\",\"sequence\":5,\"categories\":[\"THEMES_LUXURY\"]},\"BRANDS\":{\"id\":\"LUXURY_CHAINS\",\"title\":\"BRANDS_PILL_TEXT\",\"sequence\":6,\"categories\":[\"THEMES_BRAND\"]}}}";
        String pillConfigGetaway = "{\"stickyFilterPills\":{\"SORT\":{\"id\":\"SORT\",\"title\":\"SORT_PILL_TEXT\",\"sequence\":1,\"categories\":[]},\"FILTERS\":{\"id\":\"FILTERS\",\"sequence\":2,\"title\":\"FILTER_PILL_TEXT\",\"categories\":[]}},\"dynamicFilterPills\":{\"DISTANCE\":{\"id\":\"DRIVING_DISTANCE_KM\",\"title\":\"DISTANCE_PILL_TEXT\",\"sequence\":3,\"categories\":[\"DRIVING_DISTANCE_KM\"]},\"PRICE\":{\"id\":\"PRICE\",\"title\":\"PRICE_PILL_TEXT\",\"sequence\":4,\"categories\":[\"PRICE\"]},\"POPULAR\":{\"id\":\"POPULAR\",\"title\":\"POPULAR_PILL_TEXT\",\"sequence\":5,\"categories\":[\"POPULAR\"]},\"PROPERTY\":{\"id\":\"PROPERTY_TYPE\",\"title\":\"PROPERTY_PILL_TEXT\",\"sequence\":6,\"categories\":[\"MERGE_PROPERTY_TYPE\"]},\"STAR_RATING\":{\"id\":\"STAR_RATING\",\"title\":\"STAR_RATING_PILL_TEXT\",\"sequence\":7,\"categories\":[\"STAR_CATEGORY\"]}}}";
        String sortPillData = "{\"title\":\"SORT_BY_TEXT\",\"sortCriteria\":[{\"title\":\"POPULARITY_SORT_TITLE\", \"pillText\":\"POPULARITY_SORT_TEXT\",\"field\":\"popularity\",\"order\":\"desc\"},{\"title\":\"PRICE_LOW_TO_HIGH_TITLE\",\"pillText\":\"PRICE_SORT_TEXT\",\"field\":\"price\",\"order\":\"asc\"},{\"title\":\"PRICE_HIGH_TO_LOW_TITLE\",\"pillText\":\"PRICE_SORT_TEXT\",\"field\":\"price\",\"order\":\"desc\"},{\"title\":\"USER_RATING_HIGHEST_TITLE\",\"pillText\":\"RATING_SORT_TEXT\",\"field\":\"reviewRating\",\"order\":\"desc\"}]}";
        String sortCriteriaCheapestAndBestReviewedJson = "{\"title\":\"CHEAPEST_BEST_REVIEWED_TITLE\",\"pillText\":\"CHEAPEST_BEST_REVIEWED_TEXT\",\"field\":\"cheapestBestReviewed\",\"order\":\"asc\"}";
        String sortCriteriaMostAndBestReviewedJson = "{\"title\":\"MOST_AND_BEST_REVIEWED_TITLE\",\"pillText\":\"MOST_AND_BEST_REVIEWED_TEXT\",\"field\":\"mostAndBestReviewed\",\"order\":\"desc\"}";
        String sortCriteriaHeroPoiDrivingDistanceJson = "{\"title\":\"HERO_POI_DRIVING_DISTANCE_TITLE\",\"pillText\":\"HERO_POI_DRIVING_DISTANCE_TEXT\",\"field\":\"heroPoiDrivingDistanceSort\",\"order\":\"asc\"}";
        String sortCriteriaBeachCityPoiDrivingDistanceJson = "{\"title\":\"BEACH_CITY_POI_DRIVING_DISTANCE_TITLE\",\"pillText\":\"BEACH_CITY_POI_DRIVING_DISTANCE_TEXT\",\"field\":\"beachCityPoiDrivingDistanceSort\",\"order\":\"asc\"}";

        filterPillsB2C = gson.fromJson(pillConfigB2C, new TypeToken<FilterPillConfig>() {}.getType());
        filterPillsMyBiz = gson.fromJson(pillConfigMyBiz, new TypeToken<FilterPillConfig>() {}.getType());
        filterPillsLuxe = gson.fromJson(pillConfigLuxe, new TypeToken<FilterPillConfig>() {}.getType());
        filterPillsGetaway = gson.fromJson(pillConfigGetaway, new TypeToken<FilterPillConfig>() {}.getType());

        sortList = gson.fromJson(sortPillData, new TypeToken<SortList>() {}.getType());
        sortCriteriaCheapestAndBestReviewed = gson.fromJson(sortCriteriaCheapestAndBestReviewedJson, new TypeToken<SortCriteria>() {
        }.getType());
        sortCriteriaMostAndBestReviewed = gson.fromJson(sortCriteriaMostAndBestReviewedJson, new TypeToken<SortCriteria>() {
        }.getType());
        sortCriteriaHeroPoiDrivingDistance = gson.fromJson(sortCriteriaHeroPoiDrivingDistanceJson, new TypeToken<SortCriteria>() {
        }.getType());
        sortCriteriaBeachCityPoiDrivingDistance = gson.fromJson(sortCriteriaBeachCityPoiDrivingDistanceJson, new TypeToken<SortCriteria>() {
        }.getType());

        ReflectionTestUtils.setField(filterPillFactory, "filterPillsB2C", filterPillsB2C);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsMyBiz", filterPillsMyBiz);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsLuxe", filterPillsLuxe);
        ReflectionTestUtils.setField(filterPillFactory, "filterPillsGetaway", filterPillsGetaway);
        ReflectionTestUtils.setField(filterPillFactory, "sortList", sortList);
        ReflectionTestUtils.setField(filterPillFactory, "sortCriteriaCheapestAndBestReviewed", sortCriteriaCheapestAndBestReviewed);
        ReflectionTestUtils.setField(filterPillFactory, "sortCriteriaMostAndBestReviewed", sortCriteriaMostAndBestReviewed);
        ReflectionTestUtils.setField(filterPillFactory, "sortCriteriaHeroPoiDrivingDistance", sortCriteriaHeroPoiDrivingDistance);
        ReflectionTestUtils.setField(filterPillFactory, "sortCriteriaBeachCityPoiDrivingDistance", sortCriteriaBeachCityPoiDrivingDistance);

        // Mock PolyglotService behavior
        Mockito.when(polyglotService.getTranslatedData(SORT_CRITERIA_ACCESS_POINT_TITLE))
                .thenReturn("Sort by distance to {poi_name}");
    }

    @Test
    public void getFilterPillConfigurationTest() {

        FilterPillConfigurationWrapper filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration("test", "test", "", false, false, null, false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(4, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration("test", LUXE_LOCATION_TYPE, "", false, false, null, false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(4, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", false, false, null, false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(4, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, false, null, false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(5, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, false, null, true, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(6, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, false, "Taj Mahal", false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(6, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, false, "Taj Mahal", true, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(7, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, true, "Taj Mahal", true, false,false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(8, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, true, null, true, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(7, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, true, "Taj Mahal", false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(7, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "", true, true, null, false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(6, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(FUNNEL_SOURCE_GETAWAY, "test", "CORP", true, true, null, false, false, false, new HashMap<>());
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        Assertions.assertEquals(6, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());
    }

    @Test
    public void getFilterPillConfigurationWithAccessPointsTest() {
        // Test case for access point functionality
        
        // Create mock AccessPoint objects
        AccessPoint accessPoint1 = Mockito.mock(AccessPoint.class);
        Mockito.when(accessPoint1.getAccessPointName()).thenReturn("Delhi Airport");
        Mockito.when(accessPoint1.getPoi()).thenReturn("poi_123");
        
        AccessPoint accessPoint2 = Mockito.mock(AccessPoint.class);
        Mockito.when(accessPoint2.getAccessPointName()).thenReturn("Mumbai Airport");
        Mockito.when(accessPoint2.getPoi()).thenReturn("poi_456");
        
        // Test with one valid access point
        Map<String, AccessPoint> accessPointsMap = new HashMap<>();
        accessPointsMap.put("airport1", accessPoint1);
        
        FilterPillConfigurationWrapper filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(
                "test", "test", "", false, false, null, false, false, false, accessPointsMap);
        
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        // Base 4 + 1 access point = 5 sort criteria
        Assertions.assertEquals(5, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());
        
        // Verify that the access point sort criteria was added correctly
        SortCriteria accessPointCriteria = filterPillConfigurationWrapper.getSortList().getSortCriteria().get(4);
        Assertions.assertTrue(accessPointCriteria.isAccessPoint());
        Assertions.assertEquals("accessPointDrivingDistanceSort|poi_123", accessPointCriteria.getField());
        
        // Test with two access points
        accessPointsMap.put("airport2", accessPoint2);
        
        filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(
                "test", "test", "", false, false, null, false, false, false, accessPointsMap);
        
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        // Base 4 + 2 access points = 6 sort criteria
        Assertions.assertEquals(6, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());
        
        // Verify polyglot service was called for translation
        Mockito.verify(polyglotService, Mockito.atLeastOnce()).getTranslatedData(SORT_CRITERIA_ACCESS_POINT_TITLE);
    }

    @Test
    public void getFilterPillConfigurationWithInvalidAccessPointsTest() {
        // Test with access point having null or empty values
        AccessPoint invalidAccessPoint1 = Mockito.mock(AccessPoint.class);
        Mockito.when(invalidAccessPoint1.getAccessPointName()).thenReturn(null);
        // Note: getPoi() not stubbed for invalidAccessPoint1 as it won't be called due to null name
        
        AccessPoint invalidAccessPoint2 = Mockito.mock(AccessPoint.class);
        Mockito.when(invalidAccessPoint2.getAccessPointName()).thenReturn("Test Airport");
        Mockito.when(invalidAccessPoint2.getPoi()).thenReturn("");
        
        AccessPoint invalidAccessPoint3 = Mockito.mock(AccessPoint.class);
        Mockito.when(invalidAccessPoint3.getAccessPointName()).thenReturn("");
        // Note: getPoi() not stubbed for invalidAccessPoint3 as it won't be called due to empty name
        
        Map<String, AccessPoint> invalidAccessPointsMap = new HashMap<>();
        invalidAccessPointsMap.put("invalid1", invalidAccessPoint1);
        invalidAccessPointsMap.put("invalid2", invalidAccessPoint2);
        invalidAccessPointsMap.put("invalid3", invalidAccessPoint3);
        
        FilterPillConfigurationWrapper filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(
                "test", "test", "", false, false, null, false, false, false, invalidAccessPointsMap);
        
        Assertions.assertNotNull(filterPillConfigurationWrapper);
        // Should only have base 4 sort criteria as invalid access points should be ignored
        Assertions.assertEquals(4, filterPillConfigurationWrapper.getSortList().getSortCriteria().size());

    }

}