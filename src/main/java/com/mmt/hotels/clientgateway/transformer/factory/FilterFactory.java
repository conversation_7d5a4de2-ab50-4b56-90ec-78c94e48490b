package com.mmt.hotels.clientgateway.transformer.factory;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.FilterCategory;
import com.mmt.hotels.clientgateway.consul.FilterConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.FilterConfig;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.dpt.UserCohort;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class FilterFactory {

    @Value("${consul.enable}")
    private boolean consulFlag;
    @Autowired
    FilterConfigConsul filterConfigConsul;

    @Autowired
    FilterResponseTransformer filterResponseTransformer;

    @Autowired
    FilterRequestTransformer filterRequestTransformer;

    @Autowired
    FilterHelper filterHelper;

    @Autowired
    PropertyManager propertyManager;

    private final Gson gson = new Gson();

    @Autowired
    PolyglotHelper polyglotHelper;
    private String baseFilterSettings;
    private String baseFilterSettingsV2;
    private String appsDomFilterSettingsV2;
    private String appsIntlFilterSettingsV2;
    private String appsHomestayFilterSettingsV2;
    private String appsCorpIntlFilterSettingsV2;
    private String appsCorpDomFilterSettingsV2;
    private String dayUseFilterSettingsV2;
    private String gccFilterSettingsV2;
    private String premiumFunnelFilterSettingsV2;
    private String desktopCorpFilterSettings;
    private String desktopB2CFilterSettings;
    private String pwaCorpFilterSettings;
    private String pwaB2CFilterSettings;
    private String androidCorpFilterSettings;
    private String androidB2CFilterSettings;
    private String iosCorpFilterSettings;
    private String iosB2CFilterSettings;
    private String pwaMyPartnerFilterSettings;
    private String desktopMyPartnerFilterSettings;
    private String desktopGCCFilterSetting;
    private String androidGCCFilterSetting;
    private String iosGCCFilterSetting;
    private String pwaGCCFilterSetting;
    private String seoIntlFilterSetting;
    private String seoDomFilterSetting;
    private String metaIntlFilterSetting;
    private String metaDomFilterSetting;
    private String semIntlFilterSetting;
    private String semDomFilterSetting;
    private String phonePeFilterSetting;
    private String desktopIntlHotelsFilterSetting;
    private String pwaIntlHotelsFilterSetting;
    private String appsIntlHotelsFilterSetting;
    private String desktopDomHotelsFilterSetting;
    private String pwaDomHotelsFilterSetting;
    private String appsDomHotelsFilterSetting;
    private String desktopIntlHomestayFilterSetting;
    private String pwaIntlHomestayFilterSetting;
    private String appsIntlHomestayFilterSetting;
    private String desktopDomHomestayFilterSetting;
    private String pwaDomHomestayFilterSetting;
    private String appsDomHomestayFilterSetting;
    private String dayuseFilterSetting;
    private String desktopCorpIntlFilterSetting;
    private String appsCorpIntlFilterSetting;
    private String pwaCorpIntlFilterSetting;
    private String desktopCorpDomFilterSetting;
    private String appsCorpDomFilterSetting;
    private String pwaCorpDomFilterSetting;

    private Map<String, List<Filter>> compositeFilterMapping;


    /**
     * below categories will be added to base filter settings for FILTER_PILL_EXP HTL-38235
     **/
    @Value("${filter.meal.preferences.category}")
    private String mealPrefConfig;

    @Value("${filter.toggle.category}")
    private String toggleFilterConfig;

    @Value("${filter.homestay.toggle.category}")
    private String toggleFilterConfigHomestay;

    FilterConfigCategory mealPrefConfigCategory;

    FilterConfigCategory toggleConfigCategory;

    private static final Logger logger = LoggerFactory.getLogger(FilterFactory.class);



    @PostConstruct
    public void init() {

        if (consulFlag){
            baseFilterSettings = filterConfigConsul.getBaseFilterSettings();
            baseFilterSettingsV2 = filterConfigConsul.getBaseFilterSettingsV2();
            appsDomFilterSettingsV2 = filterConfigConsul.getAppsDomFilterSettingsV2();
            appsIntlFilterSettingsV2 = filterConfigConsul.getAppsIntlFilterSettingsV2();
            appsHomestayFilterSettingsV2 = filterConfigConsul.getAppsHomestayFilterSettingsV2();
            appsCorpIntlFilterSettingsV2 = filterConfigConsul.getAppsCorpIntlFilterSettingsV2();
            appsCorpDomFilterSettingsV2 = filterConfigConsul.getAppsCorpDomFilterSettingsV2();
            dayUseFilterSettingsV2 = filterConfigConsul.getDayUseFilterSettingsV2();
            gccFilterSettingsV2 = filterConfigConsul.getGccFilterSettingsV2();
            premiumFunnelFilterSettingsV2 = filterConfigConsul.getPremiumFunnelFilterSettingsV2();
            desktopCorpFilterSettings = filterConfigConsul.getDesktopCorpFilterSettings();
            desktopB2CFilterSettings = filterConfigConsul.getDesktopB2CFilterSettings();
            pwaCorpFilterSettings = filterConfigConsul.getPwaCorpFilterSettings();
            pwaB2CFilterSettings = filterConfigConsul.getPwaB2CFilterSettings();
            androidB2CFilterSettings = filterConfigConsul.getAndroidB2CFilterSettings();
            androidCorpFilterSettings = filterConfigConsul.getAndroidCorpFilterSettings();
            iosB2CFilterSettings = filterConfigConsul.getIosB2CFilterSettings();
            iosCorpFilterSettings = filterConfigConsul.getIosCorpFilterSettings();
            compositeFilterMapping = filterConfigConsul.getCompositeFilterConfig();
            pwaMyPartnerFilterSettings = filterConfigConsul.getPwaMyPartnerFilterSettings();
            desktopMyPartnerFilterSettings = filterConfigConsul.getDesktopMyPartnerFilterSettings();

            desktopGCCFilterSetting = filterConfigConsul.getDesktopGCCFilterSetting();
            androidGCCFilterSetting = filterConfigConsul.getAndroidGCCFilterSetting();
            iosGCCFilterSetting = filterConfigConsul.getIosGCCFilterSetting();
            pwaGCCFilterSetting = filterConfigConsul.getPwaGCCFilterSetting();
            seoIntlFilterSetting = filterConfigConsul.getSeoIntlFilterSetting();
            seoDomFilterSetting = filterConfigConsul.getSeoDomFilterSetting();
            metaIntlFilterSetting = filterConfigConsul.getMetaIntlFilterSetting();
            metaDomFilterSetting = filterConfigConsul.getMetaDomFilterSetting();
            semIntlFilterSetting = filterConfigConsul.getSemIntlFilterSetting();
            semDomFilterSetting = filterConfigConsul.getSemDomFilterSetting();
            phonePeFilterSetting = filterConfigConsul.getPhonePeFilterSetting();
            desktopIntlHotelsFilterSetting = filterConfigConsul.getDesktopIntlHotelsFilterSetting();
            appsIntlHotelsFilterSetting = filterConfigConsul.getAppsIntlHotelsFilterSetting();
            pwaIntlHotelsFilterSetting = filterConfigConsul.getPwaIntlHotelsFilterSetting();
            desktopDomHotelsFilterSetting = filterConfigConsul.getDesktopDomHotelsFilterSetting();
            appsDomHotelsFilterSetting = filterConfigConsul.getAppsDomHotelsFilterSetting();
            pwaDomHotelsFilterSetting = filterConfigConsul.getPwaDomHotelsFilterSetting();
            desktopIntlHomestayFilterSetting = filterConfigConsul.getDesktopIntlHomestayFilterSetting();
            appsIntlHomestayFilterSetting = filterConfigConsul.getAppsIntlHomestayFilterSetting();
            pwaIntlHomestayFilterSetting = filterConfigConsul.getPwaIntlHomestayFilterSetting();
            desktopDomHomestayFilterSetting = filterConfigConsul.getDesktopDomHomestayFilterSetting();
            appsDomHomestayFilterSetting = filterConfigConsul.getAppsDomHomestayFilterSetting();
            pwaDomHomestayFilterSetting = filterConfigConsul.getPwaDomHomestayFilterSetting();
            dayuseFilterSetting = filterConfigConsul.getDayuseFilterSetting();
            desktopCorpIntlFilterSetting = filterConfigConsul.getDesktopCorpIntlFilterSetting();
            appsCorpIntlFilterSetting = filterConfigConsul.getAppsCorpIntlFilterSetting();
            pwaCorpIntlFilterSetting = filterConfigConsul.getPwaCorpIntlFilterSetting();
            desktopCorpDomFilterSetting = filterConfigConsul.getDesktopCorpDomFilterSetting();
            appsCorpDomFilterSetting = filterConfigConsul.getAppsCorpDomFilterSetting();
            pwaCorpDomFilterSetting = filterConfigConsul.getPwaCorpDomFilterSetting();

            logger.debug("Fetching values from commonConfig consul");
        }
        else{
            logger.warn("Fetching values from property manager (PMS)");
            FilterConfig filterConfig = propertyManager.getProperty("cgFilterConfig", FilterConfig.class);
            baseFilterSettings = filterConfig.baseFilterSettings();
            desktopCorpFilterSettings = filterConfig.desktopCorpFilterSettings();
            desktopB2CFilterSettings = filterConfig.desktopB2CFilterSettings();
            pwaCorpFilterSettings = filterConfig.pwaCorpFilterSettings();
            pwaB2CFilterSettings = filterConfig.pwaB2CFilterSettings();
            androidB2CFilterSettings = filterConfig.androidB2CFilterSettings();
            androidCorpFilterSettings = filterConfig.androidCorpFilterSettings();
            iosB2CFilterSettings = filterConfig.iosB2CFilterSettings();
            iosCorpFilterSettings = filterConfig.iosCorpFilterSettings();
            compositeFilterMapping = filterConfig.compositeFilterConfig();

            // myPartner property filter settings
            pwaMyPartnerFilterSettings = filterConfig.pwaMyPartnerFilterSettings();
            desktopMyPartnerFilterSettings = filterConfig.desktopMyPartnerFilterSettings();

            //GCC filter setting
            desktopGCCFilterSetting = filterConfig.desktopGCCFilterSetting();
            androidGCCFilterSetting = filterConfig.androidGCCFilterSetting();
            iosGCCFilterSetting = filterConfig.iosGCCFilterSetting();
            pwaGCCFilterSetting = filterConfig.pwaGCCFilterSetting();
            //Seo filter settings
            seoIntlFilterSetting = filterConfig.seoIntlFilterSetting();
            seoDomFilterSetting = filterConfig.seoDomFilterSetting();
            //Meta filter setting
            metaIntlFilterSetting = filterConfig.metaIntlFilterSetting();
            metaDomFilterSetting = filterConfig.metaDomFilterSetting();
            //SEM filter setting
            semIntlFilterSetting = filterConfig.semIntlFilterSetting();
            semDomFilterSetting = filterConfig.semDomFilterSetting();
            //PhonePe filter Setting
            phonePeFilterSetting = filterConfig.phonePeFilterSetting();
            // hotels filter setting
            desktopIntlHotelsFilterSetting = filterConfig.desktopIntlHotelsFilterSetting();
            appsIntlHotelsFilterSetting = filterConfig.appsIntlHotelsFilterSetting();
            pwaIntlHotelsFilterSetting = filterConfig.pwaIntlHotelsFilterSetting();
            desktopDomHotelsFilterSetting = filterConfig.desktopDomHotelsFilterSetting();
            appsDomHotelsFilterSetting = filterConfig.appsDomHotelsFilterSetting();
            pwaDomHotelsFilterSetting = filterConfig.pwaDomHotelsFilterSetting();
            // homestay filter setting
            desktopIntlHomestayFilterSetting = filterConfig.desktopIntlHomestayFilterSetting();
            appsIntlHomestayFilterSetting = filterConfig.appsIntlHomestayFilterSetting();
            pwaIntlHomestayFilterSetting = filterConfig.pwaIntlHomestayFilterSetting();
            desktopDomHomestayFilterSetting = filterConfig.desktopDomHomestayFilterSetting();
            appsDomHomestayFilterSetting = filterConfig.appsDomHomestayFilterSetting();
            pwaDomHomestayFilterSetting = filterConfig.pwaDomHomestayFilterSetting();
            //dayUse filter setting
            dayuseFilterSetting = filterConfig.dayuseFilterSetting();
            // Corp filter setting
            desktopCorpIntlFilterSetting = filterConfig.desktopCorpIntlFilterSetting();
            appsCorpIntlFilterSetting = filterConfig.appsCorpIntlFilterSetting();
            pwaCorpIntlFilterSetting = filterConfig.pwaCorpIntlFilterSetting();
            desktopCorpDomFilterSetting = filterConfig.desktopCorpDomFilterSetting();
            appsCorpDomFilterSetting = filterConfig.appsCorpDomFilterSetting();
            pwaCorpDomFilterSetting = filterConfig.pwaCorpDomFilterSetting();

            filterConfig.addPropertyChangeListener("baseFilterSettings", event -> {
                baseFilterSettings = filterConfig.baseFilterSettings();
            });
            filterConfig.addPropertyChangeListener("desktopCorpFilterSettings", event -> {
                desktopCorpFilterSettings = filterConfig.desktopCorpFilterSettings();
            });
            filterConfig.addPropertyChangeListener("desktopB2CFilterSettings", event -> {
                desktopB2CFilterSettings = filterConfig.desktopB2CFilterSettings();
            });
            filterConfig.addPropertyChangeListener("pwaCorpFilterSettings", event -> {
                pwaCorpFilterSettings = filterConfig.pwaCorpFilterSettings();
            });
            filterConfig.addPropertyChangeListener("pwaB2CFilterSettings", event -> {
                pwaB2CFilterSettings = filterConfig.pwaB2CFilterSettings();
            });
            filterConfig.addPropertyChangeListener("androidB2CFilterSettings", event -> {
                androidB2CFilterSettings = filterConfig.androidB2CFilterSettings();
            });
            filterConfig.addPropertyChangeListener("androidCorpFilterSettings", event -> {
                androidCorpFilterSettings = filterConfig.androidCorpFilterSettings();
            });
            filterConfig.addPropertyChangeListener("iosB2CFilterSettings", event -> {
                iosB2CFilterSettings = filterConfig.iosB2CFilterSettings();
            });
            filterConfig.addPropertyChangeListener("iosCorpFilterSettings", event -> {
                iosCorpFilterSettings = filterConfig.iosCorpFilterSettings();
            });

            // myPartner property change listeners
            filterConfig.addPropertyChangeListener("pwaMyPartnerFilterSettings", event -> {
                pwaMyPartnerFilterSettings = filterConfig.pwaMyPartnerFilterSettings();
            });
            filterConfig.addPropertyChangeListener("desktopMyPartnerFilterSettings", event -> {
                desktopMyPartnerFilterSettings = filterConfig.desktopMyPartnerFilterSettings();
            });

            filterConfig.addPropertyChangeListener("compositeFilterMapping", event -> {
                compositeFilterMapping = filterConfig.compositeFilterConfig();
            });

            filterConfig.addPropertyChangeListener("desktopGCCFilterSetting", event -> {
                desktopGCCFilterSetting = filterConfig.desktopGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("androidGCCFilterSetting", event -> {
                androidGCCFilterSetting = filterConfig.androidGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("iosGCCFilterSetting", event -> {
                iosGCCFilterSetting = filterConfig.iosGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaGCCFilterSetting", event -> {
                pwaGCCFilterSetting = filterConfig.pwaGCCFilterSetting();
            });

            filterConfig.addPropertyChangeListener("seoIntlFilterSetting", event -> {
                seoIntlFilterSetting = filterConfig.seoIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("seoDomFilterSetting", event -> {
                seoDomFilterSetting = filterConfig.seoDomFilterSetting();
            });
            filterConfig.addPropertyChangeListener("metaIntlFilterSetting", event -> {
                metaIntlFilterSetting = filterConfig.pwaGCCFilterSetting();
            });
            filterConfig.addPropertyChangeListener("metaDomFilterSetting", event -> {
                metaDomFilterSetting = filterConfig.metaDomFilterSetting();
            });

            filterConfig.addPropertyChangeListener("semIntlFilterSetting", event -> {
                semIntlFilterSetting = filterConfig.semIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("semDomFilterSetting", event -> {
                semDomFilterSetting = filterConfig.semDomFilterSetting();
            });

            filterConfig.addPropertyChangeListener("phonePeFilterSetting", event -> {
                phonePeFilterSetting = filterConfig.phonePeFilterSetting();
            });

            filterConfig.addPropertyChangeListener("desktopIntlHotelsFilterSetting", event -> {
                desktopIntlHotelsFilterSetting = filterConfig.desktopIntlHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsIntlHotelsFilterSetting", event -> {
                appsIntlHotelsFilterSetting = filterConfig.appsIntlHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaIntlHotelsFilterSetting", event -> {
                pwaIntlHotelsFilterSetting = filterConfig.pwaIntlHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("desktopDomHotelsFilterSetting", event -> {
                desktopDomHotelsFilterSetting = filterConfig.desktopDomHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsDomHotelsFilterSetting", event -> {
                appsDomHotelsFilterSetting = filterConfig.appsDomHotelsFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaDomHotelsFilterSetting", event -> {
                pwaDomHotelsFilterSetting = filterConfig.pwaDomHotelsFilterSetting();
            });

            filterConfig.addPropertyChangeListener("desktopIntlHomestayFilterSetting", event -> {
                desktopIntlHomestayFilterSetting = filterConfig.desktopIntlHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsIntlHomestayFilterSetting", event -> {
                appsIntlHomestayFilterSetting = filterConfig.appsIntlHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaIntlHomestayFilterSetting", event -> {
                pwaIntlHomestayFilterSetting = filterConfig.pwaIntlHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("desktopDomHomestayFilterSetting", event -> {
                desktopDomHomestayFilterSetting = filterConfig.desktopDomHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsDomHomestayFilterSetting", event -> {
                appsDomHomestayFilterSetting = filterConfig.appsDomHomestayFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaDomHomestayFilterSetting", event -> {
                pwaDomHomestayFilterSetting = filterConfig.pwaDomHomestayFilterSetting();
            });

            filterConfig.addPropertyChangeListener("dayuseFilterSetting", event -> {
                dayuseFilterSetting = filterConfig.dayuseFilterSetting();
            });

            filterConfig.addPropertyChangeListener("desktopCorpIntlFilterSetting", event -> {
                desktopCorpIntlFilterSetting = filterConfig.desktopCorpIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsCorpIntlFilterSetting", event -> {
                appsCorpIntlFilterSetting = filterConfig.appsCorpIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaCorpIntlFilterSetting", event -> {
                pwaCorpIntlFilterSetting = filterConfig.pwaCorpIntlFilterSetting();
            });
            filterConfig.addPropertyChangeListener("desktopCorpDomFilterSetting", event -> {
                desktopCorpDomFilterSetting = filterConfig.desktopCorpDomFilterSetting();
            });
            filterConfig.addPropertyChangeListener("appsCorpDomFilterSetting", event -> {
                appsCorpDomFilterSetting = filterConfig.appsCorpDomFilterSetting();
            });
            filterConfig.addPropertyChangeListener("pwaCorpDomFilterSetting", event -> {
                pwaCorpDomFilterSetting = filterConfig.pwaCorpDomFilterSetting();
            });
        }


    }

    public FilterRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return filterRequestTransformer;
        switch (client.toUpperCase()) {
            case "PWA":
                return filterRequestTransformer;
            case "DESKTOP":
                return filterRequestTransformer;
            case "ANDROID":
                return filterRequestTransformer;
            case "IOS":
                return filterRequestTransformer;
        }
        return filterRequestTransformer;
    }

    public FilterResponseTransformer getResponseService(String client) {
    	if (StringUtils.isEmpty(client))
			return filterResponseTransformer;
        switch(client.toUpperCase()){
            case "PWA": return filterResponseTransformer;
            case "DESKTOP": return  filterResponseTransformer;
            case "ANDROID": return  filterResponseTransformer;
            case "IOS": return  filterResponseTransformer;
        }
        return filterResponseTransformer;
    }

    /*
    * myPartner change log :
    *   commonModifierResponse which contains profileType and affiliateId[subProfileType] is sent as another request parameter
    *   The values will be used to read myPartner filter configs
    *   We are modifying the current getFilterConfiguration,
    *   since the change affects only the test cases [test cases are modified as well to mock this object] and getFilterConfiguration is
    *   not used in any other service or  any functional part of the code base
    * */
    public FilterConfiguration getFilterConfiguration(String client, String idContext, String funnelSource, CommonModifierResponse commonModifierResponse, String locationName, FilterCountRequest filterRequest, String filterSettingsFromRuleEngine){

        FilterConfiguration fConfig = null;
        fConfig = gson.fromJson(baseFilterSettings, new TypeToken<FilterConfiguration>() {
        }.getType());

        String trafficSource = null;
        if(filterRequest!=null && filterRequest.getRequestDetails()!=null && filterRequest.getRequestDetails().getTrafficSource()!=null){
            trafficSource = filterRequest.getRequestDetails().getTrafficSource().getSource();
        }
        String siteDomain = null;
        if(filterRequest!=null && filterRequest.getRequestDetails()!=null && StringUtils.isNotEmpty(filterRequest.getRequestDetails().getSiteDomain())){
            siteDomain = filterRequest.getRequestDetails().getSiteDomain();
        }
        String countryCode = null;
        if(filterRequest!=null && filterRequest.getSearchCriteria()!=null && StringUtils.isNotEmpty(filterRequest.getSearchCriteria().getCountryCode())){
            countryCode = filterRequest.getSearchCriteria().getCountryCode();
        }

        if(fConfig!=null && MapUtils.isNotEmpty(fConfig.getFilters())) {
            LinkedHashMap<String, FilterConfigCategory> filtersConfigMap = fConfig.getFilters();
            if (MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && !Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.FILTER_PILL_EXP))) {
//            null check
                if (filtersConfigMap.containsKey(Constants.TOGGLE_FILTERS_CATEGORY)) {
                    fConfig.getFilters().remove(Constants.TOGGLE_FILTERS_CATEGORY);
                }
                if (filtersConfigMap.containsKey(Constants.MEAL_PREFERENCE_CATEGORY)) {
                    fConfig.getFilters().remove(Constants.MEAL_PREFERENCE_CATEGORY);
                }
            } else if(Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource) && filtersConfigMap.get(Constants.TOGGLE_FILTERS_CATEGORY)!=null && MapUtils.isNotEmpty(filtersConfigMap.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups())) {
                for(Map.Entry<String, LinkedHashMap<String, FilterConfigDetail>> groupEntry: fConfig.getFilters().get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().entrySet()) {
                    for (Map.Entry<String, FilterConfigDetail> entry : groupEntry.getValue().entrySet()) {
                        entry.getValue().setDescription(null);
                    }
                }
            }
            if (MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && !Constants.TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.EXP_CITY_COLLECTION_ENABLE))) {
                if (filtersConfigMap.containsKey(Constants.DPT_COLLECTIONS)) {
                    fConfig.getFilters().remove(Constants.DPT_COLLECTIONS);
                }
            }
        }

        FilterConfiguration baseFilterSettingsModified = polyglotHelper.translateFilterConfig(fConfig,funnelSource);

        // Append location name for the title of DRIVING_DURATION_HR
        if ( baseFilterSettingsModified != null && MapUtils.isNotEmpty(baseFilterSettingsModified.getFilters()) &&
                baseFilterSettingsModified.getFilters().containsKey(Constants.DRIVING_DURATION_HR)){
            String title = baseFilterSettingsModified.getFilters().get(Constants.DRIVING_DURATION_HR).getTitle();
            title = StringUtils.isEmpty(title) ? Constants.EMPTY_STRING : title + Constants.SPACE + locationName;
            baseFilterSettingsModified.getFilters().get(Constants.DRIVING_DURATION_HR).setTitle(title);
        }

        String baseFilterModified = gson.toJson(baseFilterSettingsModified);

        /*
         * myPartner change log :
         *   Since myPartner checks work completely on different request parameters [profile/subProfileType] instead of idContext
         *   we will not be using the switch case and these cases will entirely be moved to a block where the b2c flow is true
         * */
        FilterConfiguration filterConfiguration = null;
        if (StringUtils.isNotEmpty(filterSettingsFromRuleEngine)) {
            filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(filterSettingsFromRuleEngine, funnelSource), idContext);
        } else if (Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId())) {
            /*
             * myPartner change log :
             *   We won't be switching on idContext, and just on the client under the myPartner isolated context
             * */
            switch (client){
                case "PWA":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaMyPartnerFilterSettings,funnelSource), idContext);
                    break;
                case "DESKTOP":
                    filterConfiguration =  filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(desktopMyPartnerFilterSettings,funnelSource), idContext);
                    break;
                default :
                    filterConfiguration =  filterHelper.getFilterConfig(baseFilterModified, idContext);
            }
        } else if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            filterConfiguration = getCorpFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_SEO.equalsIgnoreCase(trafficSource)) {
            // for GCC
            filterConfiguration = getSeoFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_META.equalsIgnoreCase(trafficSource)) {
            filterConfiguration = getMetaFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_SEM.equalsIgnoreCase(trafficSource)) {
            // for GCC
            filterConfiguration = getSemFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.TRAFFIC_SOURCE_PHONEPE.equalsIgnoreCase(trafficSource)) {
            return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(phonePeFilterSetting, funnelSource), idContext);
        } else if (Utility.isRegionGccOrKsa(siteDomain)) {
            return getGccFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(funnelSource)) {
            filterConfiguration = getHotelFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
            filterConfiguration = getHomestayFilterConfiguration(client, countryCode, baseFilterModified, funnelSource, idContext);
        } else if (Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnelSource)) {
            filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(dayuseFilterSetting, funnelSource), idContext);
        } else {
            /*
             * myPartner change log :
             *   myPartner flow is retained as is
             * */
            String key = client+"_"+(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)?"CORP":"B2C");
            switch (key){
                case "PWA_CORP":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaCorpFilterSettings,funnelSource), idContext);
                    break;
                case "PWA_B2C":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(pwaB2CFilterSettings,funnelSource), idContext);
                    break;
                case "ANDROID_CORP":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(androidCorpFilterSettings,funnelSource), idContext);
                    break;
                case "ANDROID_B2C":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(androidB2CFilterSettings,funnelSource), idContext);
                    break;
                case "DESKTOP_B2C":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(desktopB2CFilterSettings,funnelSource), idContext);
                    break;
                case "DESKTOP_CORP":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopCorpFilterSettings,funnelSource), idContext);
                    break;
                case "IOS_B2C":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(iosB2CFilterSettings,funnelSource), idContext);
                    break;
                case "IOS_CORP":
                    filterConfiguration = filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(iosCorpFilterSettings,funnelSource), idContext);
                    break;
                default:
                    filterConfiguration =  filterHelper.getFilterConfig(baseFilterModified, idContext);
            }
        }
        modifyFilterConfigFromExperiments(filterConfiguration,commonModifierResponse.getExpDataMap());
        return filterConfiguration;
    }

    public void modifyFilterConfigFromExperiments(FilterConfiguration filterConfiguration, LinkedHashMap<String, String> expDataMap) {
        //if filterConfiguration is non-null and expDataMap is non-null and does not contain the key ExperimentKeys.<experiment_name>.getKey() or its value is false
        // remove the filter group from the filterConfiguration
        if (filterConfiguration != null && MapUtils.isNotEmpty(filterConfiguration.getFilters()) && MapUtils.isNotEmpty(expDataMap)) {
            LinkedHashMap<String, FilterConfigCategory> filters = filterConfiguration.getFilters();
            if(!Constants.TRUE.equalsIgnoreCase(expDataMap.getOrDefault(ExperimentKeys.MMT_SPOTLIGHT.getKey(),Constants.FALSE))) {
                if (filters.getOrDefault(Constants.TOGGLE_FILTERS_CATEGORY, null) != null && filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().getOrDefault(FilterGroup.SPOTLIGHT.name(), null) != null) {
                    filters.get(Constants.TOGGLE_FILTERS_CATEGORY).getGroups().remove(FilterGroup.SPOTLIGHT.name());
                }
            }
            if(!Constants.TRUE.equalsIgnoreCase(expDataMap.getOrDefault(ExperimentKeys.BUSINESS_REVIEW.getKey(),Constants.FALSE))) {
                if (filters.getOrDefault(FilterCategory.USER_RATING_MMT_BRAND, null) != null) {
                    filters.get(FilterCategory.USER_RATING_MMT_BRAND).getGroups().remove(FilterGroup.UGC_BUSINESS_RATING.name());
                }
            }
        }
    }

    public FilterConfigurationV2 getFilterConfigurationV2(CommonModifierResponse commonModifierResponse, FilterCountRequest filterRequest, UserCohort userCohort) {
        FilterConfigurationV2 baseFilterConfig = null;

        String client = filterRequest.getClient();
        String idContext = filterRequest.getRequestDetails().getIdContext();
        String funnelSource = filterRequest.getRequestDetails().getFunnelSource();

        try {
            baseFilterConfig = gson.fromJson(baseFilterSettingsV2, new TypeToken<FilterConfigurationV2>() {
            }.getType());
        } catch (Exception e) {
            logger.error("Error in parsing baseFilterSettingsV2", e);
        }

        String siteDomain = null;
        if(filterRequest!=null && filterRequest.getRequestDetails()!=null && StringUtils.isNotEmpty(filterRequest.getRequestDetails().getSiteDomain())){
            siteDomain = filterRequest.getRequestDetails().getSiteDomain();
        }
        String countryCode = null;
        if(filterRequest!=null && filterRequest.getSearchCriteria()!=null && StringUtils.isNotEmpty(filterRequest.getSearchCriteria().getCountryCode())){
            countryCode = filterRequest.getSearchCriteria().getCountryCode();
        }

        FilterConfigurationV2 modifiedBaseFilterConfig = polyglotHelper.translateFilterPageData(baseFilterConfig,funnelSource);
        String cohort = getUserCohortTag(userCohort);

        if (Objects.nonNull(commonModifierResponse) && Objects.nonNull(commonModifierResponse.getExtendedUser()) &&
                Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId())) {
            // Use Case : My Partner (We won't be switching on idContext, and just on the client under the myPartner isolated context)
            return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, null, idContext, cohort);
        } else if (filterRequest != null && filterRequest.getRequestDetails() != null && filterRequest.getRequestDetails().isPremium()) {
            // Use Case : Premium
            return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(premiumFunnelFilterSettingsV2, funnelSource), idContext, cohort);
        } else if (Constants.AE.equalsIgnoreCase(siteDomain) || Constants.KSA.equalsIgnoreCase(siteDomain)) {
            // Use Case : GCC / KSA
            return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(gccFilterSettingsV2,funnelSource), idContext, cohort);
        }else if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            // Use Case : Corp
            return getCorpFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        }  else if (Constants.FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(funnelSource)) {
            // Use Case : Hotels
            return getHotelFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        } else if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
            // Use Case : Homestay
            return getHomestayFilterConfigurationV2(client, countryCode, modifiedBaseFilterConfig, funnelSource, idContext, cohort);
        } else if (Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnelSource)) {
            // Use Case : Dayuse
            return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(dayUseFilterSettingsV2,funnelSource), idContext, cohort);
        }
        else {
            String key = client+"_"+(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)?"CORP":"B2C");
            switch (key){
                case "ANDROID_CORP":
                case "IOS_CORP":
                    return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(appsCorpDomFilterSettingsV2,funnelSource), idContext, cohort);
                case "ANDROID_B2C":
                case "IOS_B2C":
                    return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, modifiedFilterConfigWithPolyglotDataV2(appsDomFilterSettingsV2,funnelSource), idContext, cohort);
                default:
                    return filterHelper.getFilterConfigV2(modifiedBaseFilterConfig, null, idContext, cohort);
            }
        }
    }

    /**
     * userCohort tag = {searchContext}_{userSegment}_{cityGroup}
     * */
    private String getUserCohortTag(UserCohort userCohort) {
        if (userCohort != null) {
            String searchContext = userCohort.getSearchContext();
            String userSegment = userCohort.getUserSegment();
            String cityGroup = userCohort.getCityGroup();

            StringBuilder sb = new StringBuilder();
            if (StringUtils.isNotEmpty(searchContext)) {
                sb.append(searchContext.toLowerCase());
            }
            if (StringUtils.isNotEmpty(userSegment)) {
                if (sb.length() > 0) sb.append("_");
                sb.append(userSegment.toLowerCase());
            }
            if (StringUtils.isNotEmpty(cityGroup)) {
                if (sb.length() > 0) sb.append("_");
                sb.append(cityGroup.toLowerCase());
            }
            if (sb.length() > 0) {
                return sb.toString();
            }
        }
        return "default";
    }

    private FilterConfiguration getCorpFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext){
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.CORP_ID_CONTEXT+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.DESKTOP_CORP_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopCorpIntlFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_CORP_INTL:
            case Constants.IOS_CORP_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsCorpIntlFilterSetting,funnelSource), idContext);
            case Constants.PWA_CORP_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaCorpIntlFilterSetting,funnelSource), idContext);
            case Constants.DESKTOP_CORP_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopCorpDomFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_CORP_DOM:
            case Constants.IOS_CORP_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsCorpDomFilterSetting,funnelSource), idContext);
            case Constants.PWA_CORP_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaCorpDomFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfigurationV2 getCorpFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort){
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.CORP_ID_CONTEXT+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.ANDROID_CORP_INTL:
            case Constants.IOS_CORP_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsCorpIntlFilterSettingsV2,funnelSource), idContext, cohort);
            case Constants.ANDROID_CORP_DOM:
            case Constants.IOS_CORP_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsCorpDomFilterSettingsV2,funnelSource), idContext, cohort);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort);
        }
    }

    private FilterConfiguration getSeoFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = Constants.TRAFFIC_SOURCE_SEO.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.SEO_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(seoIntlFilterSetting,funnelSource), idContext);
            case Constants.SEO_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(seoDomFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getMetaFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = Constants.TRAFFIC_SOURCE_META.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.META_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(metaIntlFilterSetting,funnelSource), idContext);
            case Constants.META_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(metaDomFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getSemFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = Constants.TRAFFIC_SOURCE_SEM.toUpperCase()+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.SEM_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(semIntlFilterSetting,funnelSource), idContext);
            case Constants.SEM_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(semDomFilterSetting,funnelSource), idContext);
            default:
                return filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getGccFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+"GCC";
        switch (key) {
            case Constants.DESKTOP_GCC:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopGCCFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_GCC:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(androidGCCFilterSetting,funnelSource), idContext);
            case Constants.IOS_GCC:
                return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(iosGCCFilterSetting,funnelSource), idContext);
            case Constants.PWA_GCC:
                return filterHelper.getFilterConfig(baseFilterModified,modifiedFilterWithPolyglotData(pwaGCCFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfiguration getHotelFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOTELS+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.DESKTOP_HOTELS_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopIntlHotelsFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOTELS_INTL:
            case Constants.IOS_HOTELS_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsIntlHotelsFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOTELS_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaIntlHotelsFilterSetting,funnelSource), idContext);
            case Constants.DESKTOP_HOTELS_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopDomHotelsFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOTELS_DOM:
            case Constants.IOS_HOTELS_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsDomHotelsFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOTELS_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaDomHotelsFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    private FilterConfigurationV2 getHotelFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOTELS+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.ANDROID_HOTELS_INTL:
            case Constants.IOS_HOTELS_INTL:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsIntlFilterSettingsV2,funnelSource), idContext, cohort);
            case Constants.ANDROID_HOTELS_DOM:
            case Constants.IOS_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsDomFilterSettingsV2,funnelSource), idContext, cohort);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort);
        }
    }

    private FilterConfigurationV2 getHomestayFilterConfigurationV2(String client, String countryCode, FilterConfigurationV2 baseFilterModified, String funnelSource, String idContext, String cohort) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOTELS+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.ANDROID_HOTELS_INTL:
            case Constants.IOS_HOTELS_INTL:
            case Constants.ANDROID_HOTELS_DOM:
            case Constants.IOS_HOTELS_DOM:
                return filterHelper.getFilterConfigV2(baseFilterModified, modifiedFilterConfigWithPolyglotDataV2(appsHomestayFilterSettingsV2,funnelSource), idContext, cohort);
            default:
                return  filterHelper.getFilterConfigV2(baseFilterModified, null, idContext, cohort);
        }
    }

    private FilterConfiguration getHomestayFilterConfiguration(String client, String countryCode, String baseFilterModified, String funnelSource, String idContext) {
        String key = client.toUpperCase()+Constants.UNDERSCORE+Constants.FUNNEL_SOURCE_HOMESTAY+Constants.UNDERSCORE+((Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode))?"DOM":"INTL");
        switch (key) {
            case Constants.DESKTOP_HOMESTAY_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopIntlHomestayFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOMESTAY_INTL:
            case Constants.IOS_HOMESTAY_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsIntlHomestayFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOMESTAY_INTL:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaIntlHomestayFilterSetting,funnelSource), idContext);
            case Constants.DESKTOP_HOMESTAY_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(desktopDomHomestayFilterSetting,funnelSource), idContext);
            case Constants.ANDROID_HOMESTAY_DOM:
            case Constants.IOS_HOMESTAY_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(appsDomHomestayFilterSetting,funnelSource), idContext);
            case Constants.PWA_HOMESTAY_DOM:
                return filterHelper.getFilterConfig(baseFilterModified, modifiedFilterWithPolyglotData(pwaDomHomestayFilterSetting,funnelSource), idContext);
            default:
                return  filterHelper.getFilterConfig(baseFilterModified, idContext);
        }
    }

    /**
     * this method adds meal_preferences and toggle_filters category to base filter settings HTL-38235
     **/
    private void modifyFilterConfigForPillsExperiment(String funnelSource, FilterConfiguration fConfig) {
        if (StringUtils.isNotBlank(mealPrefConfig) && StringUtils.isNotBlank(toggleFilterConfigHomestay) && StringUtils.isNotBlank(toggleFilterConfig)) {
            mealPrefConfigCategory = gson.fromJson(mealPrefConfig, new TypeToken<FilterConfigCategory>() {
            }.getType());

            if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
                toggleConfigCategory = gson.fromJson(toggleFilterConfigHomestay, new TypeToken<FilterConfigCategory>() {
                }.getType());
            } else {
                toggleConfigCategory = gson.fromJson(toggleFilterConfig, new TypeToken<FilterConfigCategory>() {
                }.getType());
            }
            if (fConfig != null && fConfig.getFilters() != null) {
                fConfig.getFilters().put(Constants.MEAL_PREFERENCE_CATEGORY, mealPrefConfigCategory);
                fConfig.getFilters().put(Constants.TOGGLE_FILTERS_CATEGORY, toggleConfigCategory);
            }
        }
    }

    public String modifiedFilterWithPolyglotData(String filter,String funnelSource){
        FilterConfiguration fConfig = null;
        fConfig = gson.fromJson(filter, new TypeToken<FilterConfiguration>() {
        }.getType());

        polyglotHelper.translateFilterConfig(fConfig,funnelSource);
        String modifiedFilter = gson.toJson(fConfig);
        return modifiedFilter;
    }

    public FilterConfigurationV2 modifiedFilterConfigWithPolyglotDataV2(String filter,String funnelSource){
        FilterConfigurationV2 fConfig = null;

        try {
            fConfig = gson.fromJson(filter, new TypeToken<FilterConfigurationV2>() {
            }.getType());
        } catch (Exception e) {
            logger.error("Error in parsing filter", e);
        }
        polyglotHelper.translateFilterPageData(fConfig,funnelSource);
        return fConfig;
    }

    public Map<String, List<Filter>> getCompositeFilterConfig() {
        return compositeFilterMapping;
    }

}
