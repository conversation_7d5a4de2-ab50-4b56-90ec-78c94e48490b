package com.mmt.hotels.clientgateway.transformer.response.desktop;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.Hover;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

@Component
public class SearchRoomsResponseTransformerDesktop extends SearchRoomsResponseTransformer{
    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsResponseTransformerDesktop.class);

    @Override
    protected PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop) {
        PersuasionObject persuasionObject = new PersuasionObject();
        persuasionObject.setData(new ArrayList<>());
        persuasionObject.setPlaceholder(Constants.PC_SELECT_RIGHT_1);
        persuasionObject.setTemplate("IMAGE_TEXT_H");
        PersuasionData persuasionData = new PersuasionData();
        PersuasionStyle style = new PersuasionStyle();
        if(isNewDetailsPageDesktop) {
            style.setStyleClasses(Arrays.asList("rmType__topratedNew"));
        }
        else{
            style.setStyleClasses(Arrays.asList("rmType__toprated"));
        }
        persuasionData.setStyle(style);
        persuasionData.setPersuasionType("PEITHO");
        persuasionData.setText(polyglotService.getTranslatedData(ConstantsTranslation.TOP_RATED));
        persuasionObject.setData(Arrays.asList(persuasionData));
        return persuasionObject;
    }

    @Override
    public LoginPersuasion buildLoginPersuasion(){
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        LoginPersuasion loginPersuasion = new LoginPersuasion();
        if (Utility.isRegionGccOrKsa(region)) {
            loginPersuasion.setLoginPersuasionText(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT_GCC));
            loginPersuasion.setLoginPersuasionSubText(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT_GCC));
        }else {
            loginPersuasion.setLoginPersuasionText(polyglotService.getTranslatedData(LOGIN_PERSUASION_TEXT));
            loginPersuasion.setLoginPersuasionSubText(polyglotService.getTranslatedData(LOGIN_PERSUASION_SUBTEXT));
        }
        return loginPersuasion;
    }

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
    }

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap, int myPartnerCashback, HeroTierDetails heroTierDetails) {
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon,persuasionMap, myPartnerCashback, heroTierDetails);
    }

    /**
     * HTL-40907: Build delayed confirmation persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias             Organisation alias name.
     * @param isMyBizNewDetailsPage
     * @return delayed confirmation persuasion.
     */
    @Override
    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        PersuasionResponse delayedConfirmationPersuasion = new PersuasionResponse();
        delayedConfirmationPersuasion.setId(DELAYED_CONFIRMATION);
        String title = isMyBizNewDetailsPage ? polyglotService.getTranslatedData(DELAYED_CONFIRMATION_PERSUASION_MYB_NEW_DETAILS_TITLE) : polyglotService.getTranslatedData(DELAYED_CONFIRMATION_PERSUASION_TITLE);
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        title = StringUtils.replace(title, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        delayedConfirmationPersuasion.setTitle(title);
        Style style = new Style();
        if (isMyBizNewDetailsPage) {
            style.setBgColor(Constants.DELAYED_CONFIRMATION_MYB_NEW_DETAILS_BG_COLOR);
        } else {
            style.setBgColor(Constants.DELAYED_CONFIRMATION_BG_COLOR);
        }
        delayedConfirmationPersuasion.setStyle(style);
        return delayedConfirmationPersuasion;
    }

    public String getHtml(){
        return  DT_INCLUSION_HTML;
    }

    /**
     * HTL-40907: Build special fare tag persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias Organization alias name.
     * @return special fare persuasion.
     */
    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        PersuasionResponse specialFarePersuasion = new PersuasionResponse();
        Style style = new Style();
        style.setStyleClass(SPECIAL_FARE_TAG_SMALL_STYLE);
        specialFarePersuasion.setStyle(style);
        String title = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG);
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        specialFarePersuasion.setTitle(title);
        specialFarePersuasion.setId(SPECIAL_FARE_TAG_SMALL);
        return specialFarePersuasion;
    }

    /**
     * HTL-40907: Build special fare tag with info icon persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias Organization alias name.
     * @return special fare persuasion.
     */
    @Override
    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias, boolean isNewSelectRoomPage) {
        PersuasionResponse specialFarePersuasion = new PersuasionResponse();
        Style style = new Style();
        style.setStyleClass(SPECIAL_FARE_TAG_LARGE_STYLE);
        specialFarePersuasion.setStyle(style);
        String title = polyglotService.getTranslatedData(ConstantsTranslation.SPECIAL_FARE_TAG);
        corpAlias = corpAlias != null ? corpAlias : StringUtils.EMPTY;
        title = StringUtils.replace(title, "{CORP_ALIAS}", corpAlias);
        specialFarePersuasion.setTitle(title);
        specialFarePersuasion.setId(SPECIAL_FARE_TAG_LARGE);
        specialFarePersuasion.setIconType("infoIconLarge");
        Hover hover = new Hover();
        String titleText = polyglotService.getTranslatedData(SPECIAL_FARE_TITLE_TEXT);
        titleText = StringUtils.replace(titleText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        hover.setTitleText(titleText);
        specialFarePersuasion.setHover(hover);
        return specialFarePersuasion;
    }

    /**
     * HTL-40907: Build delayed booking confirmation persuasion for negotiated rate hotels.
     * Negotiated rates are the one-on-one rates that are directly negotiated between the corporate/organization and the hotel.
     *
     * @param corpAlias Organization alias name.
     * @return confirmation text persuasion.
     */
    @Override
    public PersuasionResponse buildConfirmationTextPersuasion(String corpAlias,boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage) {
        PersuasionResponse confirmationTextPersuasion = new PersuasionResponse();
        confirmationTextPersuasion.setId(BOOKING_CONFIRMATION_TEXT);
        String persuasionText = isMyBizNewDetailsPage ? polyglotService.getTranslatedData(ConstantsTranslation.BOOKING_CONFIRMATION_TEXT_NEW_DETAILS_DESKTOP) : polyglotService.getTranslatedData(ConstantsTranslation.BOOKING_CONFIRMATION_TEXT_DESKTOP);
        persuasionText = StringUtils.replace(persuasionText, NO_OF_HOURS_PLACEHOLDER, String.valueOf(noOfHoursForConfirmation));
        confirmationTextPersuasion.setPersuasionText(persuasionText);
        return confirmationTextPersuasion;
    }
}
