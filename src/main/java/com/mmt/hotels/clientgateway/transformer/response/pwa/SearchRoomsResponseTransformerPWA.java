package com.mmt.hotels.clientgateway.transformer.response.pwa;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import com.mmt.hotels.model.response.pricing.HeroTierUpgradeDetails;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;

import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class SearchRoomsResponseTransformerPWA extends SearchRoomsResponseTransformer{

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchRoomsResponseTransformerPWA.class);

    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon,persuasionMap);
    }

    @Override
    public void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap, int myPartnerCashback, HeroTierDetails heroTierDetails) {
        persuasionUtil.buildLoyaltyCashbackPersuasions(coupon,persuasionMap, myPartnerCashback, heroTierDetails);
    }

    @Override
    public PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop) {
        return createTopRatedPersuasionForMoblie();
    }

    @Override
    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias,boolean isNewSelectRoomPage) {
        return null;
    }

    @Override
    public PersuasionResponse buildConfirmationTextPersuasion(String corpAlias,boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage) {
        return null;
    }

    public String getHtml(){
        return Constants.DT_INCLUSION_HTML;
    }
}
