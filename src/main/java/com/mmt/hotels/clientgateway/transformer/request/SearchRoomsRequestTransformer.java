package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.SearchRoomsCriteria;
import com.mmt.hotels.clientgateway.request.SearchRoomsRequest;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.filter.FilterRange;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.GuestRecommendationEnabledReqBody;
import com.mmt.hotels.model.request.PriceByHotelsRequestBody;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.DETAIL_SEARCH_ROOMS;


@Component
public abstract class SearchRoomsRequestTransformer extends BaseRoomRequestTransformer{
	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private Utility utility;

	public PriceByHotelsRequestBody convertSearchRoomsRequest(SearchRoomsRequest searchRoomsRequest, CommonModifierResponse commonModifierResponse) {
		PriceByHotelsRequestBody priceByHotelsRequestBody = new PriceByHotelsRequestBody();
		long startTime = System.currentTimeMillis();
		try {
			if(searchRoomsRequest != null && CollectionUtils.isNotEmpty(searchRoomsRequest.getFilterCriteria()) && searchRoomsRequest.getRequestDetails() != null && Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(searchRoomsRequest.getRequestDetails().getFunnelSource())){
				Optional<com.mmt.hotels.clientgateway.request.Filter> bedRoomCountfilter = searchRoomsRequest.getFilterCriteria().stream().filter(f -> com.mmt.hotels.clientgateway.response.filter.FilterGroup.BEDROOM_COUNT.name().equalsIgnoreCase(f.getFilterGroup().name())).findFirst();
				if(bedRoomCountfilter.isPresent()){
					utility.modifyRoomStayCandidateRequestForHomestayFunnelDetail(bedRoomCountfilter.get(),searchRoomsRequest.getSearchCriteria());
				}
			}
			super.buildDeviceDetails(priceByHotelsRequestBody, searchRoomsRequest.getDeviceDetails());
			buildSearchCriteria(priceByHotelsRequestBody, searchRoomsRequest.getSearchCriteria(), commonModifierResponse, searchRoomsRequest.getExpDataMap());
            buildRequestDetails(priceByHotelsRequestBody, searchRoomsRequest.getRequestDetails(), commonModifierResponse);
			priceByHotelsRequestBody.setReqContext(searchRoomsRequest.getRequestDetails().isPremium() ? Constants.PREMIUM : Constants.DEFAULT);
			priceByHotelsRequestBody.setResponseFilterFlags(super.buildResponseFilterFlags(priceByHotelsRequestBody, searchRoomsRequest, commonModifierResponse));
			priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
			priceByHotelsRequestBody.setMobile(commonModifierResponse.getMobile());
			priceByHotelsRequestBody.setExperimentData(searchRoomsRequest.getExpData());
			priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
			priceByHotelsRequestBody.setMtKey(searchRoomsRequest.getSearchCriteria().getMtKey());
			priceByHotelsRequestBody.setAppliedFilterMap(buildAppliedFilterMap(searchRoomsRequest.getFilterCriteria()));
			setOtherDetails(priceByHotelsRequestBody);
			priceByHotelsRequestBody.setChannel(searchRoomsRequest.getRequestDetails().getChannel());
			priceByHotelsRequestBody.setCdfContextId(commonModifierResponse.getCdfContextId());
			priceByHotelsRequestBody.setAffiliateId(commonModifierResponse.getAffiliateId());
			priceByHotelsRequestBody.setCorrelationKey(searchRoomsRequest.getCorrelationKey());
			priceByHotelsRequestBody.setValidExpList(searchRoomsRequest.getValidExpList());
			priceByHotelsRequestBody.setVariantKeys(searchRoomsRequest.getExpVariantKeys());
			priceByHotelsRequestBody.setRequestIdentifier(utility.buildRequestIdentifier(searchRoomsRequest.getRequestDetails()));
			if (searchRoomsRequest.getSearchCriteria() != null ) {
				priceByHotelsRequestBody.setHotelType(searchRoomsRequest.getSearchCriteria().getHotelType());
				priceByHotelsRequestBody.setGuestHouseAvailable(searchRoomsRequest.getSearchCriteria().getGuestHouseAvailable());
			}
			if (commonModifierResponse.getExtendedUser() != null) {
				priceByHotelsRequestBody.setUuid(commonModifierResponse.getExtendedUser().getUuid());
				priceByHotelsRequestBody.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
				priceByHotelsRequestBody.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
				priceByHotelsRequestBody.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
				priceByHotelsRequestBody.setAgencyUUID(Utility.fetchAgencyUUIDFromCorp(commonModifierResponse.getExtendedUser().getCorporateData()));
			}
			if (CollectionUtils.isNotEmpty(searchRoomsRequest.getSearchCriteria().getTravellerEmailID())) {
				List<TravelerDetail> travelerDetailsList = new ArrayList<>();
				for (String email : searchRoomsRequest.getSearchCriteria().getTravellerEmailID()) {
					TravelerDetail travelerDetail = new TravelerDetail();
					try {
						ScramblerClient sc = ScramblerClient.getInstance();
						String commEmail = sc.encode(email, HashType.F);
						travelerDetail.setEmailCommId(commEmail);
						travelerDetailsList.add(travelerDetail);
					} catch (ScramblerClientException e) {
						//Do nothing
					}
				}
				priceByHotelsRequestBody.setTravelerDetailsList(travelerDetailsList);
			}
			priceByHotelsRequestBody.setSiteDomain(searchRoomsRequest.getRequestDetails().getSiteDomain());
			priceByHotelsRequestBody.setZcpHash(searchRoomsRequest.getRequestDetails().getZcp());
			priceByHotelsRequestBody.setLoggedIn(searchRoomsRequest.getRequestDetails().isLoggedIn());
			priceByHotelsRequestBody.setOldWorkflowId(searchRoomsRequest.getRequestDetails().getOldWorkflowId());
			priceByHotelsRequestBody.setForwardBookingFlow(searchRoomsRequest.getRequestDetails().isForwardBookingFlow());
			if (commonModifierResponse.getHydraResponse() != null) {
				priceByHotelsRequestBody.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
				priceByHotelsRequestBody.setFlightBooker(commonModifierResponse.getHydraResponse().isFlightBooker());
			}
			priceByHotelsRequestBody.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
			priceByHotelsRequestBody.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
			priceByHotelsRequestBody.setUserLocation(commonModifierResponse.getUserLocation());

			priceByHotelsRequestBody.setRoomPreferenceEnabled(utility.checkIfFilterValueExistsInAppliedFilterMap(searchRoomsRequest.getFilterCriteria()));
			priceByHotelsRequestBody.setExperimentData(utility.updateExpDataForBedRoomCountFilter(searchRoomsRequest.getFilterCriteria(), priceByHotelsRequestBody.getExperimentData()));
			if (commonModifierResponse.getExtendedUser() != null) {
				priceByHotelsRequestBody.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
			}
			if (searchRoomsRequest.getSearchCriteria() != null && searchRoomsRequest.getSearchCriteria().getMultiCurrencyInfo() != null) {
				priceByHotelsRequestBody.setMultiCurrencyInfo(utility.buildMultiCurrencyInfoRequest(searchRoomsRequest.getSearchCriteria().getMultiCurrencyInfo()));
			}
			if (searchRoomsRequest.getSearchCriteria().getUserGlobalInfo() != null) {
				priceByHotelsRequestBody.setUserGlobalInfo(utility.buildUserGlobalInfoHES(searchRoomsRequest.getSearchCriteria().getUserGlobalInfo()));
			}
		}finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_REQUEST_PROCESS, DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
		}

		return priceByHotelsRequestBody;
	}


	private void setOtherDetails(PriceByHotelsRequestBody priceByHotelsRequestBody) {
		priceByHotelsRequestBody.setChannel("B2Cweb");
		priceByHotelsRequestBody.setPageContext("DETAIL");
		priceByHotelsRequestBody.setRequestType("B2CAgent");
		priceByHotelsRequestBody.setDomain("B2C");

	}

    private void buildRequestDetails(PriceByHotelsRequestBody priceByHotelsRequestBody, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {
        priceByHotelsRequestBody.setFunnelSource(requestDetails.getFunnelSource());
        priceByHotelsRequestBody.setIdContext(requestDetails.getIdContext());
        priceByHotelsRequestBody.setVisitorId(requestDetails.getVisitorId());
        priceByHotelsRequestBody.setVisitNumber(requestDetails.getVisitNumber() != null ?
                String.valueOf(requestDetails.getVisitNumber()) : "");

        priceByHotelsRequestBody.setPaymentChannel(requestDetails.getChannel());
        priceByHotelsRequestBody.setFirstTimeUserState(requestDetails.getFirstTimeUserState());
        if (null != requestDetails.getTrafficSource()) {
            priceByHotelsRequestBody.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource()));
        }
        priceByHotelsRequestBody.setCouponCount(requestDetails.getCouponCount() != null ? requestDetails.getCouponCount() : 0);
        if (commonModifierResponse.getUserLocation() != null) {
            priceByHotelsRequestBody.setSrCty(commonModifierResponse.getUserLocation().getCity());
            priceByHotelsRequestBody.setSrCon(commonModifierResponse.getUserLocation().getCountry());
            priceByHotelsRequestBody.setSrcState(commonModifierResponse.getUserLocation().getState());
        }
        priceByHotelsRequestBody.setCouponCount(requestDetails.getCouponCount() != null ? requestDetails.getCouponCount() : 0);
        priceByHotelsRequestBody.setSeoCorp(requestDetails.isSeoCorp());
        priceByHotelsRequestBody.setExtendedPackageCall(requestDetails.isExtendedPackageCall());
        priceByHotelsRequestBody.setRequisitionID(requestDetails.getRequisitionID());
        priceByHotelsRequestBody.setMyBizFlowIdentifier(requestDetails.getMyBizFlowIdentifier());
		priceByHotelsRequestBody.setRequestIdentifier(utility.buildRequestIdentifier(requestDetails));
		priceByHotelsRequestBody.setReqContext(requestDetails.isPremium() ? Constants.PREMIUM : "");

    }

    private com.mmt.hotels.model.request.TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {

        com.mmt.hotels.model.request.TrafficSource trafficSourceCB = new com.mmt.hotels.model.request.TrafficSource();
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        // Pass aud field to downstream APIs
        if (trafficSource.getAud() != null) {
            trafficSourceCB.setAud(trafficSource.getAud());
        }
        return trafficSourceCB;

    }

	private void buildSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody,
			SearchRoomsCriteria searchCriteria, CommonModifierResponse commonModifierResponse, Map<String, String> expData) {

		List<String> hotelIds = new ArrayList<String>();
		hotelIds.add(searchCriteria.getHotelId());
		super.populateSearchCriteria(priceByHotelsRequestBody,searchCriteria,hotelIds, commonModifierResponse);
		priceByHotelsRequestBody.setRoomStayCandidates(buildRoomStayCandidates(searchCriteria.getRoomStayCandidates()));
		if (searchCriteria != null && utility.isDistributeRoomStayCandidates(searchCriteria.getRoomStayCandidates(), expData)) {
			priceByHotelsRequestBody.setAppendRscInDeeplink(true);
			priceByHotelsRequestBody.setRscValueForDeepLink(utility.buildRscValue(searchCriteria.getRoomStayCandidates()));
			priceByHotelsRequestBody.setRoomStayCandidates(utility.buildRoomStayDistribution(searchCriteria.getRoomStayCandidates(), expData));
		}
	}


	private GuestRecommendationEnabledReqBody buildGuestRecommendationEnabled() {
		GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
		guestRecommendationEnabledReqBody.setMaxRecommendations("1");
		guestRecommendationEnabledReqBody.setText("true");
		return guestRecommendationEnabledReqBody;
	}

	private List<RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {

        if(roomStayCandidates==null)
            return null;

        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates){
        	RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;
    }



    private List<GuestCount> buildGuestCounts(
			com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG) {
		List<GuestCount> guestCounts = new ArrayList<>();
		GuestCount guestCount = new GuestCount();
		guestCount.setAgeQualifyingCode("10");
		guestCount.setAges(roomStayCandidateCG.getChildAges());
		guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
		guestCounts.add(guestCount);
		return guestCounts;
	}
}
