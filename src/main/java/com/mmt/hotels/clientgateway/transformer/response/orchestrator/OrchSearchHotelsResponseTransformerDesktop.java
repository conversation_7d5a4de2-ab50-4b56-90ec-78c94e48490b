package com.mmt.hotels.clientgateway.transformer.response.orchestrator;


import com.fasterxml.jackson.core.type.TypeReference;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.content.CampaignPojo;
import com.gommt.hotels.orchestrator.model.response.content.IndiannessPersuasion;
import com.gommt.hotels.orchestrator.model.response.da.CancellationTimeline;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PriceDetail;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.PropertyTextConfigConsul;
import com.mmt.hotels.clientgateway.enums.ActionType;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.PolyglotHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.PropertyTextConfig;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.response.Hover;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.searchHotels.MyBizStaticCard;
import com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionStyle;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.PersuasionUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.listpersonalization.IndianessToolTip;
import com.mmt.hotels.model.response.listpersonalization.LuxeToolTip;
import com.mmt.hotels.model.response.listpersonalization.MyBizAssuredToolTip;
import com.mmt.hotels.model.response.listpersonalization.MySafetyTooltip;
import com.mmt.hotels.model.response.listpersonalization.ValueStaysTooltip;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.orchestrator.models.mypartner.MyPartnerFareHoldEligibilityResponse;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INDIANESS_HOVER_SUBTITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.INDIANESS_HOVER_TITLE;

@Component
@SuppressWarnings("unchecked")
public class OrchSearchHotelsResponseTransformerDesktop extends OrchSearchHotelsResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformerDesktop.class);

    private MySafetyTooltip mySafetyDataTooltips;
    List<String> amenetiesWithUrl;

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Autowired
    PropertyTextConfigConsul propertyTextConfigConsul;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Autowired
    PropertyManager propManager;
    @Autowired
    private PolyglotHelper polyglotHelper;

    @Autowired
    private Utility utility;
    private ValueStaysTooltip valueStaysTooltipDom;
    private ValueStaysTooltip valueStaysTooltipIntl;
    private Map<Integer, Set<String>> budgetHotelCityConfig;
    private LuxeToolTip luxeToolTipConfig;
    private MySafetyTooltip mysafetytooltip;
    private MyBizAssuredToolTip myBizAssuredTooltipDom;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    DateUtil dateUtil;

    @Value("${desktop.tool.tip.persuasions}")
    private String toolTipPersuasions;

    @Value("${mybiz.assured.url}")
    private String myBizAssuredUrl;

    @Value("${high.rated.url}")
    private String highRatedUrl;

    @Value("${gst.invoice.url}")
    private String gstInvoiceUrl;

    @Value("${bpg.url}")
    private String bpgUrl;

    @Value("${active.languages}")
    private String activeLanguages;

    private Map<String, Object> desktopToolTipPersuasionsMap;

    private Map<String, MySafetyTooltip> mySafetyToolTipTranslated;

    private MyBizStaticCard myBizStaticCard;

    @Autowired
    PersuasionUtil persuasionUtil;

    private static final String DEVICE_TYPE = "Desktop";


    @PostConstruct
    public void init() {
        super.init();
        if (consulFlag) {
            mySafetyDataTooltips = propertyTextConfigConsul.getMySafetyTooltipKeys();
            mySafetyToolTipTranslated = createMySafetyTooltipTranslated();
            amenetiesWithUrl = propertyTextConfigConsul.getAmenetiesWithUrl();
            LOGGER.debug("Fetching values from propertyTextConfig consul");
        } else {
            PropertyTextConfig propertyTextConfig = propManager.getProperty("propertyTextConfig", PropertyTextConfig.class);
            mySafetyDataTooltips = propertyTextConfig.mySafetyTooltipKeys();
            propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> mySafetyDataTooltips = propertyTextConfig.mySafetyTooltipKeys());
            mySafetyToolTipTranslated = createMySafetyTooltipTranslated();
            amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl();
            propertyTextConfig.addPropertyChangeListener("propertyTextConfig", event -> amenetiesWithUrl = propertyTextConfig.amenetiesWithUrl());
        }

        if (consulFlag) {
            valueStaysTooltipDom = commonConfigConsul.getMmtValueStaysTooltipDom();
            valueStaysTooltipIntl = commonConfigConsul.getMmtValueStaysTooltipIntl();
            luxeToolTipConfig = commonConfigConsul.getLuxeToolTip();
            myBizAssuredTooltipDom = commonConfigConsul.getMyBizAssuredTooltipDom();
            myBizStaticCard = commonConfigConsul.getMyBizStaticCard();
            missingSlotDetails = commonConfigConsul.getMissingSlotDetails();
            thresholdForSlashedAndDefaultHourPrice = commonConfigConsul.getThresholdForSlashedAndDefaultHourPrice();
            LOGGER.warn("missingSlotDetails : {}", missingSlotDetails);
            LOGGER.debug("Fetching values from commonConfig consul");
        } else {
            CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
            valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom();
            valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl();
            luxeToolTipConfig = commonConfig.luxeToolTip();
            myBizAssuredTooltipDom = commonConfig.myBizAssuredTooltipDom();
            myBizStaticCard = commonConfig.myBizStaticCard();
            missingSlotDetails = commonConfig.missingSlotDetails();
            thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
            commonConfig.addPropertyChangeListener("missingSlotDetails", event -> missingSlotDetails = commonConfig.missingSlotDetails());
            commonConfig.addPropertyChangeListener("myBizStaticCard", event -> myBizStaticCard = commonConfig.myBizStaticCard());
            commonConfig.addPropertyChangeListener("mmtValueStaysTooltipDom", evt -> valueStaysTooltipDom = commonConfig.mmtValueStaysTooltipDom());
            commonConfig.addPropertyChangeListener("mmtValueStaysTooltipIntl", evt -> valueStaysTooltipIntl = commonConfig.mmtValueStaysTooltipIntl());
            commonConfig.addPropertyChangeListener("luxeToolTip", evt -> luxeToolTipConfig = commonConfig.luxeToolTip());
            commonConfig.addPropertyChangeListener("myBizAssuredTooltipDom", evt -> myBizAssuredTooltipDom = commonConfig.myBizAssuredTooltipDom());
            LOGGER.warn("missingSlotDetails : {}", missingSlotDetails);
        }

        try {
            desktopToolTipPersuasionsMap = objectMapperUtil.getObjectFromJsonWithType(toolTipPersuasions, new TypeReference<Map<String, Object>>() {
                    },
                    DependencyLayer.CLIENTGATEWAY);
        } catch (JsonParseException e) {
            LOGGER.error("error in creating desktopToolTipPersuasionsMap from string {}, ", toolTipPersuasions, e);
        }
        specialFarePersuasionConfigMap = new Gson().fromJson(specialFarePersuasionConfig, new TypeToken<Map<String, Map<String, PersuasionData>>>() {
        }.getType());
    }

    private Map<String, MySafetyTooltip> createMySafetyTooltipTranslated() {
        Map<String, MySafetyTooltip> mySafetyTooltipTranslated = new HashMap<>();
        String[] activeLanguagesList = activeLanguages.split(",");
        MySafetyTooltip mySafetyTooltipConfig = SerializationUtils.clone(mySafetyDataTooltips);

        for (String lang : activeLanguagesList) {
            MySafetyTooltip mySafetyTooltip;
            mySafetyTooltip = polyglotHelper.translateMySafetyToolTip(mySafetyTooltipConfig, lang);
            mySafetyTooltipTranslated.put(lang, mySafetyTooltip);
        }
        return mySafetyTooltipTranslated;
    }

    @Override
    protected BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse) {
        BottomSheet bottomSheet = new BottomSheet();
        bottomSheet.setHeading(perResponse.getHeading());
        bottomSheet.setSubHeading(perResponse.getHeading());
        bottomSheet.setImgUrl(myBizAssuredUrl);
        bottomSheet.setCta(polyglotService.getTranslatedData(ConstantsTranslation.MYBIZ_ASSURED_FILTER_CARD_CTA));
        bottomSheet.setCtaAction("");
        List<SectionFeature> sectionFeatureList = new ArrayList<>();
        sectionFeatureList.add(new SectionFeature(highRatedUrl, polyglotService.getTranslatedData(ConstantsTranslation.RATED_HIGH_BT), "grayDot", null, null));
        sectionFeatureList.add(new SectionFeature(gstInvoiceUrl, polyglotService.getTranslatedData(ConstantsTranslation.GST_INVOICE_ASSURANCE_TEXT), "grayDot", null, null));
        sectionFeatureList.add(new SectionFeature(bpgUrl, polyglotService.getTranslatedData(ConstantsTranslation.BPG_TEXT), "grayDot", null, null));
        bottomSheet.setSectionFeatures(sectionFeatureList);
        return bottomSheet;
    }

    @Override
    public void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity, CancellationTimeline cancellationTimeline, PriceDetail displayFare, ListingSearchRequest listingSearchRequest) {
        try {
            if (null != hotel.getHotelPersuasions()) {
                JSONObject hotelPersuasions = new JSONObject(objectMapperUtil.getJsonFromObject(hotel.getHotelPersuasions(), DependencyLayer.CLIENTGATEWAY));
                Iterator<String> keys = hotelPersuasions.keys();
                for (String placeHolder : hotelPersuasions.keySet()) {
                    JSONObject persuasion = hotelPersuasions.has(placeHolder) ? hotelPersuasions.getJSONObject(placeHolder) : null;
                    if (null != persuasion && persuasion.has("data")) {
                        JSONArray persuasionDataList = persuasion.getJSONArray("data");
                        for (int i = 0; i < persuasionDataList.length(); i++) {
                            JSONObject persuasionData = persuasionDataList.getJSONObject(i);
                            if (persuasionData.has("hover")) {
                                if (persuasionData.getJSONObject("hover").has("tooltipType")) {
                                    addToolTip(persuasionData.getJSONObject("hover"), hotelEntity, cancellationTimeline, listingSearchRequest);
                                    if (persuasionData.getJSONObject("hover") != null && StringUtils.isNotEmpty(persuasionData.getJSONObject("hover").getString("tooltipType")) && TOOL_TIP_INDIANNESS.equalsIgnoreCase(persuasionData.getJSONObject("hover").getString("tooltipType"))) {
                                        hotel.setLovedByIndians(true);
                                    }
                                }
                                //TODO - myPartner/myBiz/GCC case
                                /*if (isWalletSurgePersuasionEnabled(persuasionData)) {
                                    persuasionData.getJSONObject("hover").put(PERSUASION_HEADING_KEY, hotelEntity.getWalletEntity().getWpmRule().getOutputDetails().getPersuasionText());
                                    persuasionData.getJSONObject(TIMER).put(EXPIRY, hotelEntity.getWalletEntity().getWpmRule().getExpireAt());
                                }*/
                            }
                        }

                    }
                    //TODO - myPartner/myBiz case
                    if(null != persuasion && persuasion.has("hover")) {

                        JSONObject topLevelHoverData = persuasion.getJSONObject("hover");
                        updateTopLevelHover(topLevelHoverData, hotelEntity.getMpFareHoldStatus());
                    }
                }

                hotel.setHotelPersuasions(hotelPersuasions.toMap());
            }
        } catch (Exception e) {
            LOGGER.error("Error while updating hover data for desktop", e);
        }

    }

    protected void updateTopLevelHover(JSONObject topLevelHoverData, com.gommt.hotels.orchestrator.model.response.listing.MpFareHoldStatus mpFareHoldStatus) {
        if (topLevelHoverData == null || mpFareHoldStatus == null) {
            return;
        }
        String tooltipType = topLevelHoverData.optString(TOOL_TIP_TYPE);
        if (MP_FARE_HOLD.equalsIgnoreCase(tooltipType)) {
            Hover hover = new Hover();
            Long expiry = mpFareHoldStatus.getExpiry();
            if (expiry != null) {
                hover.setTitleText(MessageFormat.format(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_TITLE),
                        dateUtil.convertEpochToDateTime(expiry, dateUtil.DD_MMM_hh_mm_a)));
            } else {
                hover.setTitleText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_GENERIC_TITLE));
            }
            hover.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.BOOK_NOW_PERSUASION_HOVER_SUB_TITLE));

            topLevelHoverData.put(DATA, hover);
            topLevelHoverData.put(LOG_HOVER_KEY, LOG_HOVER_VALUE);
            topLevelHoverData.put(TOOL_TIP_TYPE, TITLE_SUBTITLE_TOOLTIP);
        }
    }

    private static Map<String, Object> convertJSONObjectToMap(JSONObject jsonObject) {
        Map<String, Object> map = new HashMap<>();
        Iterator<String> keys = jsonObject.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            map.put(key, jsonObject.get(key));
        }
        return map;
    }


    private void addToolTip(JSONObject hoverData, HotelDetails hotelEntity, CancellationTimeline cancellationTimeline, ListingSearchRequest listingSearchRequest) {
        switch (hoverData.getString("tooltipType").toUpperCase()) {
            case Constants.TOOL_TIP_BNPL_AVAIL:
                hoverData.put("data", cancellationTimeline);
                break;
            case Constants.TOOL_TIP_FCZPN:
                hoverData.put("data", cancellationTimeline);
                break;
            case Constants.TOOL_TIP_FC:
                hoverData.put("data", createFreeCancellationTooltip(cancellationTimeline));
                break;
            case Constants.TOOL_TIP_SAFETY:
                hoverData.put("data", mySafetyToolTipTranslated.get(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue())));

                break;
            case Constants.TOOL_TIP_VILLA_STAY:
                if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
                        .containsKey(Constants.TOOL_TIP_VILLA_STAY)) {
                    hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_VILLA_STAY));
                    hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
                }
                break;
            case Constants.TOOL_TIP_HOSTEL_STAY:
                if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
                        .containsKey(Constants.TOOL_TIP_HOSTEL_STAY)) {
                    hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_HOSTEL_STAY));
                    hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
                }
                break;
            case Constants.TOOL_TIP_APARTMENT_STAY:
                if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
                        .containsKey(Constants.TOOL_TIP_APARTMENT_STAY)) {
                    hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_APARTMENT_STAY));
                    hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
                }
                break;
            case Constants.TOOL_TIP_COTTAGE_STAY:
                if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
                        .containsKey(Constants.TOOL_TIP_COTTAGE_STAY)) {
                    hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_COTTAGE_STAY));
                    hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
                }
                break;
            case Constants.TOOL_TIP_HOMESTAY_STAY:
                if (MapUtils.isNotEmpty(desktopToolTipPersuasionsMap) && desktopToolTipPersuasionsMap
                        .containsKey(Constants.TOOL_TIP_HOMESTAY_STAY)) {
                    hoverData.put("data", fetchToolTipPersuasionData(Constants.TOOL_TIP_HOMESTAY_STAY));
                    hoverData.put("tooltipType", Constants.TOOL_TIP_TYPE_PROPERTY_BENEFITS);
                }
                break;
            case Constants.TOOL_TIP_MMT_VALUE_STAY:
                if (hotelEntity.isBudgetHotel()) {
                    hoverData.put("data", createValueStayToolTip(listingSearchRequest.getSearchCriteria().getCountryCode()));
                }
                break;
            case Constants.TOOL_TIP_MYBIZ_ASSURED:
                hoverData.put("data", createMyBizAssuredToolTip());
                break;
            case Constants.TOOL_TIP_LUXE:
                hoverData.put("data", createLuxeToolTip());
                break;
            case TOOL_TIP_INDIANNESS:
                hoverData.put("data", createIndianessPersuasion(hotelEntity.getIndianessPersuasion()));
                break;

        }
    }

    public IndianessToolTip createIndianessPersuasion(IndiannessPersuasion indianessPersuasion) {
        IndianessToolTip indianessToolTip = new IndianessToolTip();
        if (indianessPersuasion == null || CollectionUtils.isEmpty(indianessPersuasion.getShortSummary()))
            return indianessToolTip;
        List<IndianessToolTip> data = new ArrayList<>();
        for (CampaignPojo campaignPojo : indianessPersuasion.getShortSummary()) {
            IndianessToolTip indianessToolTip1 = new IndianessToolTip();
            indianessToolTip1.setTitleText(campaignPojo.getHeading());
            indianessToolTip1.setIconUrl(campaignPojo.getIconUrl());
            data.add(indianessToolTip1);
        }
        indianessToolTip.setData(data);
        indianessToolTip.setTitleText(polyglotService.getTranslatedData(INDIANESS_HOVER_TITLE));
        indianessToolTip.setSubText(polyglotService.getTranslatedData(INDIANESS_HOVER_SUBTITLE));
        return indianessToolTip;
    }

    private LuxeToolTip createLuxeToolTip() {
        LuxeToolTip luxeToolTip;
        luxeToolTip = SerializationUtils.clone(luxeToolTipConfig);
        polyglotHelper.translateLuxeToolTip(luxeToolTip);
        return luxeToolTip;
    }

    public MyBizAssuredToolTip createMyBizAssuredToolTip() {
        MyBizAssuredToolTip myBizAssuredToolTip;
        myBizAssuredToolTip = SerializationUtils.clone(myBizAssuredTooltipDom);
        polyglotHelper.translateMyBizAssuredTooltip(myBizAssuredToolTip);
        return myBizAssuredToolTip;
    }

    public ValueStaysTooltip createValueStayToolTip(String countryCode){
        ValueStaysTooltip valueStaysTooltip;
        if (StringUtils.isBlank(countryCode) || "IN".equalsIgnoreCase(countryCode)) {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipDom);
        } else {
            valueStaysTooltip = SerializationUtils.clone(valueStaysTooltipIntl);
        }
        polyglotHelper.translateValueStaysTooltip(valueStaysTooltip);
        return valueStaysTooltip;
    }

    public CancellationTimeline createFreeCancellationTooltip(CancellationTimeline cancellationTimeline) {
        CancellationTimeline cancellationToolTip = new CancellationTimeline();
        if (cancellationTimeline != null) {
            cancellationToolTip.setCheckInDate(cancellationTimeline.getCheckInDate());
            cancellationToolTip.setCancellationDate(cancellationTimeline.getCancellationDate());
            cancellationToolTip.setSubTitle(cancellationTimeline.getSubTitle());
            cancellationToolTip.setFreeCancellationBenefits(cancellationTimeline.getFreeCancellationBenefits());
            cancellationToolTip.setFreeCancellationText(cancellationTimeline.getFreeCancellationText());
            cancellationToolTip.setTitle(cancellationTimeline.getTitle());
            cancellationToolTip.setBookingDate(cancellationTimeline.getBookingDate());
            cancellationToolTip.setFcTextForPersuasion(cancellationTimeline.getFcTextForPersuasion());
            cancellationToolTip.setCancellationPolicyTimelineList(cancellationTimeline.getCancellationPolicyTimelineList());
        }
        return cancellationToolTip;
    }

    private String fetchToolTipPersuasionData(String stayType) {
        Map<String, Object> toolTipData = new HashMap<>();
        Map<String, Object> toolTipConfig = (Map<String, Object>) desktopToolTipPersuasionsMap.get(stayType);
        toolTipData.put("imageUrl", toolTipConfig.get("imageUrl"));
        toolTipData.put("toolTipHeading", polyglotService.getTranslatedData((String) toolTipConfig.get("toolTipHeading")));
        toolTipData.put("toolTipData", new ArrayList<String>());
        List<String> toolTipList = (List<String>) toolTipConfig.get("data");
        for (String toolTipKey : toolTipList) {
            ((List<String>) toolTipData.get("toolTipData")).add(polyglotService.getTranslatedData(toolTipKey));
        }
        try {
            return objectMapperUtil.getJsonFromObject(toolTipData, DependencyLayer.CLIENTGATEWAY);
        } catch (JsonParseException e) {
            //
        }
        return "";
    }

    @Override
    public void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocationDetails locusData, boolean homestayV2Flow) {

        boolean isGccListingPage = Utility.isGccOrKsa() && Utility.isListingPage(searchHotelsRequest);
        boolean isHNOrientation = searchHotelsRequest != null && utility.isExperimentOn(searchHotelsRequest.getExpDataMap(), EXP_MYPARTNER_LISTING_HN);
        if (CollectionUtils.isNotEmpty(locationPersuasion)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject locPers = new PersuasionObject();
            PersuasionObject locPersGCC = new PersuasionObject();
            Set<String> alreadyAddedPersuasions = new HashSet<>();
            locPers.setData(new ArrayList<>());
            locPers.setTemplate("MULTI_PERSUASION_H");
            locPers.setPlaceholder("MULTI");

            int index = 1;
            PersuasionData locPersuasionData = new PersuasionData();
            locPersuasionData.setStyle(new PersuasionStyle());
            List<String> styleClasses = new ArrayList<>();
            styleClasses.add("pc__location");
            boolean isPerNewEnabled = searchHotelsRequest != null ? utility.isExperimentOn(searchHotelsRequest.getExpDataMap(), EXP_PERNEW) : false;
            if (isHNOrientation) {
                styleClasses.remove("pc__location");
                styleClasses.add("pc__location pc__location_ellipsis_2 darkGreyText");
            } else if (isPerNewEnabled) {
                styleClasses.remove("pc__location");
                styleClasses.add("pc__locationPerNew");
            }
            locPersuasionData.getStyle().setStyleClasses(styleClasses);
            locPersuasionData.setHtml(true);
            locPersuasionData.setId("LOC_PERSUASION_" + index++);
            locPersuasionData.setPersuasionType("LOCATION");
            locPersuasionData.setHasAction(true);
            locPersuasionData.setActionType("DETAIL_PAGE_MAP");
            if (isGccListingPage) {
                styleClasses.add(STYLE_BLUE_TEXT);
                locPersuasionData.setActionType(ActionType.LISTING_MAP_POPUP.name());
            }

            locPers.getData().add(locPersuasionData);
            if (searchHotelsRequest != null && (searchHotelsRequest.getFeatureFlags() != null && searchHotelsRequest.getFeatureFlags().isOriginListingMap())) {
                if (!locationPersuasion.isEmpty()) {
                    locPersuasionData.setText("<span class='blueText" + (isHNOrientation ? " latoBold" : "") + "'>" + locationPersuasion.get(0) + "</span>");
                    alreadyAddedPersuasions.add(locationPersuasion.get(0));
                }
            } else if (isGccListingPage && !locationPersuasion.isEmpty()) {
                if (isPerNewEnabled) {
                    setTextForLocationPersuasion(locationPersuasion, locPersuasionData, isPerNewEnabled, alreadyAddedPersuasions, isHNOrientation);
                } else {
                    locPersuasionData.setText(locationPersuasion.get(0));
                    alreadyAddedPersuasions.add(locationPersuasion.get(0));
                }
            } else {
                setTextForLocationPersuasion(locationPersuasion, locPersuasionData, isPerNewEnabled, alreadyAddedPersuasions, isHNOrientation);

                // we need to block second element of data for sectionMyBizSimilarToDirectHtl
                if (locationPersuasion.size() > 2 && !sectionMyBizSimilarToDirectHtl && StringUtils.isNotBlank(locPersuasionData.getText())) {
                    if (!alreadyAddedPersuasions.contains(locationPersuasion.get(2))) {
                        String text = isPerNewEnabled ? locPersuasionData.getText() + "&nbsp;| " + "<span class='latoRegular'>" + locationPersuasion.get(2) + "</span>" : locPersuasionData.getText() + "&nbsp;| " + locationPersuasion.get(2);
                        locPersuasionData.setText(text);
                        alreadyAddedPersuasions.add(locationPersuasion.get(2));
                    }
                }

                //For Secondary Location Persuasion, if it is present, add it in the Location Persuasion
                if (locationPersuasion.size() > 3 && StringUtils.isNotBlank(locPersuasionData.getText())) {
                    if (!alreadyAddedPersuasions.contains(locationPersuasion.get(3))) {
                        String text = isPerNewEnabled ? locPersuasionData.getText() + "&nbsp;| " + "<span class='latoRegular'>" + locationPersuasion.get(3) + "</span>" : locPersuasionData.getText() + "&nbsp;| " + locationPersuasion.get(3);
                        locPersuasionData.setText(text);
                        alreadyAddedPersuasions.add(locationPersuasion.get(3));
                    }
                }
            }

            boolean isGroundTransportToBeAdded = !(locationPersuasion.size() > 2 && (locationPersuasion.get(2).contains(METRO) || locationPersuasion.get(2).contains(BUS))) && nearestGroundTransportPoi != null && CollectionUtils.isNotEmpty(nearestGroundTransportPoi.getPoiTags());
            if (isGccListingPage && locationPersuasion.size() > 1) {
                //For GCC Listing page location POIs will be in new placeholder with template MULTI_PERSUASION_H
                locPersGCC.setData(new ArrayList<>());
                locPersGCC.setTemplate("MULTI_PERSUASION_H");
                locPersGCC.setPlaceholder("MULTI");
                for (String persuasion : locationPersuasion.subList(1, locationPersuasion.size())) {
                    if (alreadyAddedPersuasions.contains(persuasion)) {
                        continue;
                    }
                    PersuasionData locPersuasionDataGCC = new PersuasionData();
                    locPersuasionDataGCC.setHasAction(false);
                    locPersuasionDataGCC.setHtml(true);
                    locPersuasionDataGCC.setId(LOCATION_PERSUASION_ID + index++);
                    locPersuasionDataGCC.setPersuasionType(LOCATION);
                    locPersuasionDataGCC.setText(persuasion);
                    if (index <= locationPersuasion.size() || isGroundTransportToBeAdded) {
                        locPersuasionDataGCC.setText(persuasion + COMMA); //Comma to be appended after every persuasion
                    }
                    locPersuasionDataGCC.setStyle(new PersuasionStyle());
                    locPersuasionDataGCC.getStyle().setStyleClasses(new ArrayList<>(Arrays.asList(STYLE_LOCATION, STYLE_DARK_TEXT)));
                    locPersGCC.getData().add(locPersuasionDataGCC);
                    alreadyAddedPersuasions.add(persuasion);
                }
            }

            // Adding Ground transport Persuasion
            if (ObjectUtils.isNotEmpty(locPersGCC) && locPersGCC.getData() != null && !locPersGCC.getData().isEmpty() && isGroundTransportToBeAdded) {
                PersuasionData locPersuasionData3 = getGroundTransportPersuasionData(nearestGroundTransportPoi, index);
                locPersGCC.getData().add(locPersuasionData3);
            }


            try {
                final String locationPersuasionKey = isHNOrientation ? Constants.LOCATION_PERSUAION_HN_PLACEHOLDER_ID : Constants.LOCATION_PERSUAION_PLACEHOLDER_ID;
                ((Map<Object, Object>) hotel.getHotelPersuasions()).put(locationPersuasionKey, locPers);
                if (isGccListingPage) {
                    ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.LOCATION_PERSUAION_2_PLACEHOLDER_ID, locPersGCC);
                }
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }
        }

        if (CollectionUtils.isNotEmpty(facilities) && enableAmenities) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate("MULTI_PERSUASION_H");
            amenityPers.setPlaceholder("MULTI");

            int index = 1;
            Iterator<String> iter = facilities.iterator();
            while (iter.hasNext() && index <= 3) {
                PersuasionData amenPersuasionData = new PersuasionData();
                amenPersuasionData.setHasAction(false);
                amenPersuasionData.setHtml(false);
                amenPersuasionData.setId("AMENITIES_" + index++);
                amenPersuasionData.setPersuasionType("AMENITIES");
                amenPersuasionData.setText(iter.next());
                String text = Utility.removeSpecialChar(amenPersuasionData.getText());
                if (StringUtils.isNotBlank(text) && amenetiesWithUrl.contains(text.toLowerCase())) {
                    amenPersuasionData.setIconurl("https://promos.makemytrip.com/images/highlighted/" + text.toLowerCase() + ".png");
                } else {
                    amenPersuasionData.setIcontype("singleGreyTickIcon");
                }
                amenPersuasionData.setStyle(new PersuasionStyle());
                List<String> styleClasses = new ArrayList<>();
                if (isHNOrientation) {
                    amenPersuasionData.setIcontype(null);
                    amenPersuasionData.setIconurl(null);
                    styleClasses.add("pc__hotelCategoryPerNew_hn");
                } else {
                    styleClasses.add("pc__amenity");
                }
                amenPersuasionData.getStyle().setStyleClasses(styleClasses);
                amenityPers.getData().add(amenPersuasionData);
            }

            try {
                String amenityPersuasionKey = AMENITIES_PLACEHOLDER_ID;
                if (isHNOrientation) {
                    amenityPersuasionKey = AMENITIES_PLACEHOLDER_ID_HN;
                    utility.constraintLengthForAmenityPersuasions(amenityPers);
                }
                if (!((Map<Object, Object>) hotel.getHotelPersuasions()).containsKey(amenityPersuasionKey)) {
                    ((Map<Object, Object>) hotel.getHotelPersuasions()).put(amenityPersuasionKey, amenityPers);
                }
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }
        }

        if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && FUNNEL_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource()) && StringUtils.isNotEmpty(dayUsePersuasionsText)) {
            if (hotel.getHotelPersuasions() == null)
                hotel.setHotelPersuasions(new HashMap<String, Object>());
            PersuasionObject amenityPers = new PersuasionObject();
            amenityPers.setData(new ArrayList<>());
            amenityPers.setTemplate(IMAGE_TEXT_H);
            amenityPers.setPlaceholder("SINGLE");


            PersuasionData amenPersuasionData = new PersuasionData();
            amenPersuasionData.setHasAction(false);
            amenPersuasionData.setHtml(false);
            amenPersuasionData.setId(DAYUSE_LOCAL_ID);
            amenPersuasionData.setPersuasionType(DAYUSE_LOCAL_ID);
            amenPersuasionData.setStyle(new PersuasionStyle());
            amenPersuasionData.getStyle().setTextColor("#000000");
            amenPersuasionData.setText(dayUsePersuasionsText);
            amenityPers.getData().add(amenPersuasionData);
            try {
                ((Map<Object, Object>) hotel.getHotelPersuasions()).put(Constants.AMENITIES_PLACEHOLDER_ID_APP, amenityPers);
            } catch (Exception e) {
                LOGGER.error("Location Persuasion could not be added due to : {} ", e.getMessage());
            }
        }


    }

    private static PersuasionData getGroundTransportPersuasionData(TransportPoi nearestGroundTransportPoi, Integer index) {
        PersuasionData locPersuasionData3 = new PersuasionData();
        locPersuasionData3.setHasAction(false);
        locPersuasionData3.setHtml(true);
        locPersuasionData3.setId(LOCATION_PERSUASION_ID + index++);
        locPersuasionData3.setPersuasionType(LOCATION);
        locPersuasionData3.setStyle(new PersuasionStyle());
        if (nearestGroundTransportPoi.getPoiTags() != null && nearestGroundTransportPoi.getPoiTags().contains(METRO_TAG)) {
            locPersuasionData3.setText("<b>&nbsp;" + METRO_TEXT + "</b>");
        } else if (nearestGroundTransportPoi.getPoiTags() != null && (nearestGroundTransportPoi.getPoiTags().contains(BUS_TAG) || nearestGroundTransportPoi.getPoiTags().contains(BUS_TERMINAL_TAG))) {
            locPersuasionData3.setText("<b>&nbsp;" + BUS_TEXT + "</b>");
        }
        locPersuasionData3.getStyle().setStyleClasses(new ArrayList<>(Arrays.asList(STYLE_LOCATION, STYLE_DARK_TEXT, STYLE_INLINE_BLOCK)));
        com.mmt.hotels.clientgateway.thirdparty.response.Hover hover = new com.mmt.hotels.clientgateway.thirdparty.response.Hover();
        hover.setData(nearestGroundTransportPoi);
        hover.setTooltipType(TOOL_TIP_GT);
        locPersuasionData3.setHover(hover);
        return locPersuasionData3;
    }

    private void setTextForLocationPersuasion(List<String> locationPersuasion, PersuasionData locPersuasionData, boolean isPerNewEnabled, Set<String> alreadyAddedPersuasions, boolean isHNOrientation) {
        if (alreadyAddedPersuasions == null) {
            alreadyAddedPersuasions = new HashSet<>();
        }

        if (locationPersuasion.size() == 1) {
            locPersuasionData.setText("<span class='blueText" + (isHNOrientation ? " latoBold" : "") + "'>" + locationPersuasion.get(0) + "</span>");
            alreadyAddedPersuasions.add(locationPersuasion.get(0));
        } else if (locationPersuasion.size() >= 2) {
            locPersuasionData.setText("<span class='blueText" + (isHNOrientation ? " latoBold" : "") + "'>" + locationPersuasion.get(0) + "</span>");
            alreadyAddedPersuasions.add(locationPersuasion.get(0));
            if (StringUtils.isNotBlank(locPersuasionData.getText())) {
                if (!alreadyAddedPersuasions.contains(locationPersuasion.get(1))) {
                    if (isPerNewEnabled) {
                        locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + "<span class='latoRegular'>" + locationPersuasion.get(1) + "</span>");
                    } else {
                        locPersuasionData.setText(locPersuasionData.getText() + "&nbsp;| " + locationPersuasion.get(1));
                    }
                    alreadyAddedPersuasions.add(locationPersuasion.get(1));
                }
            }
        }
    }

    @Override
    protected String buildBGColor(String section, String orientation, String cardType) {
        return null;
    }

    @Override
    protected void addBookingConfirmationPersuasion(HotelDetails hotelEntity) {

        //TODO - Not being used for B2C

    }

    @Override
    public MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels) {
        //TODO - Not being used for B2C
        return null;
    }
}
