package com.mmt.hotels.clientgateway.transformer.response.android;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.rooms.LoginPersuasion;
import com.mmt.hotels.clientgateway.response.rooms.SearchRoomsResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.model.response.pricing.BestCoupon;
import com.mmt.hotels.model.response.pricing.HeroTierDetails;
import com.mmt.hotels.model.response.pricing.RoomDetailsResponse;
import com.mmt.hotels.model.response.staticdata.HotelImage;
import com.mmt.model.HotelsRoomInfoResponseEntity;
import org.springframework.stereotype.Component;

import com.mmt.hotels.clientgateway.transformer.response.SearchRoomsResponseTransformer;

import java.util.List;
import java.util.Map;

import static com.mmt.hotels.clientgateway.constants.Constants.DT_INCLUSION_HTML;

@Component
public class SearchRoomsResponseTransformerAndroid extends SearchRoomsResponseTransformer{

    @Override
    public SearchRoomsResponse convertSearchRoomsResponse(RoomDetailsResponse roomDetailsResponse, HotelsRoomInfoResponseEntity hotelsRoomInfoResponseEntity, HotelImage hotelImage, String expData, List<RoomStayCandidate> roomStayCandidates, SearchCriteria searchRoomsCriteria, List<Filter> filterCriteria, String expVariantKeys, RequestDetails requestDetails) {
        SearchRoomsResponse searchRoomsResponse = super.convertSearchRoomsResponse(roomDetailsResponse,hotelsRoomInfoResponseEntity,hotelImage,expData,roomStayCandidates,searchRoomsCriteria,filterCriteria,expVariantKeys,requestDetails);
        removeImpInfo(searchRoomsResponse);
        //addPriceToolTip(searchRoomsResponse);
        return searchRoomsResponse;
    }

    public String getHtml(){
        return Constants.APPS_INCLUSION_HTML;
    }

    private void removeImpInfo(SearchRoomsResponse searchRoomsResponse) {
        /* HTL-30428 Remove ImpInfo as per Product Teams request - only for Android for now */
        if (searchRoomsResponse!=null && searchRoomsResponse.getImpInfo()!=null)
            searchRoomsResponse.setImpInfo(null);
    }

    @Override
    protected PersuasionObject createTopRatedPersuasion(boolean isNewDetailsPageDesktop) {
        return createTopRatedPersuasionForMoblie();
    }
    @Override
    protected LoginPersuasion buildLoginPersuasion() {
        return null;
    }

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap) {

    }

    @Override
    protected void buildLoyaltyCashbackPersuasions(BestCoupon coupon, Map<String, PersuasionResponse> persuasionMap, int myPartnerCashback, HeroTierDetails heroTierDetails) {

    }

    @Override
    public PersuasionResponse buildDelayedConfirmationPersuasion(String corpAlias, boolean isMyBizNewDetailsPage) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagPersuasion(String corpAlias) {
        return null;
    }

    @Override
    public PersuasionResponse buildSpecialFareTagWithInfoPersuasion(String corpAlias,boolean isNewSelectRoomPage) {
        return buildSpecialFarePersuasionForMobile(corpAlias,isNewSelectRoomPage);
    }

    @Override
    public PersuasionResponse buildConfirmationTextPersuasion(String corpAlias,boolean isNewSelectRoomPage, boolean isMyBizNewDetailsPage) {
        return buildBookingConfirmationPersuasionForMobile(corpAlias,isNewSelectRoomPage);
    }
}
