package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.RoomInfoRequest;
import com.mmt.hotels.clientgateway.response.CancellationTimeline;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.FlexiDetailBottomSheet;
import com.mmt.hotels.clientgateway.response.rooms.CancellationPolicyTimeline;
import com.mmt.hotels.clientgateway.response.rooms.RoomTariff;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.response.pricing.CancelPenalty;
import com.mmt.hotels.model.response.pricing.Inclusion;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.staticdata.CategoryInfo;
import com.mmt.hotels.model.response.staticdata.HouseRules;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.model.response.staticdata.RuleTableInfo;
import com.mmt.hotels.model.response.staticdata.VideoInfo;
import com.mmt.hotels.model.response.txn.*;
import com.mmt.model.BathroomArrangement;
import com.mmt.model.Facility;
import com.mmt.model.FacilityGroup;
import com.mmt.model.ReviewRoomInfo;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

@Component
public class RoomInfoResponseTransformer {


    @Value("${consul.enable}")
    private boolean consulFlag;

    @Value("${view.360.icon.url}")
    private String view360IconUrl;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    private Map<String,String> mealPlanMapPolyglot;
    private List<String> translateEnabledSupplierCodes = new ArrayList<>();

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;

    @Autowired
    private Utility utility;

    @Autowired
    PropertyManager propManager;

    @Autowired
    DateUtil dateUtil;

    @Autowired
    private PolyglotService polyglotService;

    private static final Logger logger = LoggerFactory.getLogger(RoomInfoResponseTransformer.class);


    @PostConstruct
    public  void init() {
        if(consulFlag){
            mealPlanMapPolyglot = commonConfigConsul.getMealPlanMapPolyglot();
            translateEnabledSupplierCodes = commonConfigConsul.getTranslateEnabledSupplierCodes();
            logger.debug("Fetching values from commonConfig consul");
        }
        else{
            CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
            mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot();
            commonConfig.addPropertyChangeListener("mealPlanMapPolyglot", evt -> mealPlanMapPolyglot = commonConfig.mealPlanMapPolyglot());
        }
    }

    public RoomInfoResponse transformRoomInfoResponses(RoomInfoRequest roomInfoRequest, PersistanceMultiRoomResponseEntity txnDataEntity) {
        /* Maps to NEW cg/room-infos API */
        RoomInfoResponse roomInfoResponse = new RoomInfoResponse();

        PersistedMultiRoomData txnData = txnDataEntity.getPersistedData();
        if (null != txnData) {

            roomInfoResponse.setBlackInfo(commonResponseTransformer.buildBlackInfo(txnData.getBlackInfo()));
            roomInfoResponse.setExtraBedPolicy(getExtraBedPolicy(txnData.getHotelList().get(0).getHotelInfo().getHouseRules()));
            BNPLDetails bnplDetails = getBnplDetails(txnData.getTotalDisplayFare());
            boolean bnplApplicable = bnplDetails != null && bnplDetails.isBnplApplicable();

            String checkIn = (txnData.getAvailReqBody()!=null) ? txnData.getAvailReqBody().getCheckin():StringUtils.EMPTY;
            Integer ap = (StringUtils.isNotBlank(checkIn)) ? dateUtil.getDaysDiff(LocalDate.now(),LocalDate.parse(checkIn)) : null;

            for (PersistedHotel hotel : txnData.getHotelList()) {

                RatePolicy ratePolicy = getConfirmationPolicy(hotel.getTariffInfoList());
                String confirmationPolicyType = ratePolicy != null ? ratePolicy.getValue() : null;

                AtomicInteger totalNumRooms = getTotalNumOfRooms(hotel);

                for (PersistedTariffInfo tariff: hotel.getTariffInfoList()) {
                    /* Respond only for the matching RoomCode+RatePlanCode */
                    if (roomInfoRequest.getRoomCode().equalsIgnoreCase(tariff.getRoomCode())
                            && roomInfoRequest.getRatePlanCode().equalsIgnoreCase(tariff.getRatePlanCode())) {

                        if (roomInfoResponse.getBlackInfo() != null) {
                            /* Update blackInfo node inclusions for the matching RatePlan */
                            roomInfoResponse.getBlackInfo().setInclusionsList(getBlackInclusions(tariff.getInclusions()));
                        }
                        if(utility.isExperimentOn(txnData.getExpData(), Constants.ALL_INCLUSIVE_PLAN_EXPERIMENT) && tariff.isAllInclusiveRate()){
                            roomInfoResponse.setAllInclusiveInclusions(null!=hotel.getHotelInfo()?hotel.getHotelInfo().getAllInclusiveInclusions():null);
                        }
                        RoomDetails roomDetail = new RoomDetails();
                        if (null != tariff.getRoomDetails()) {
                        	roomDetail.setBeds(tariff.getRoomDetails().getBeds());
                            roomDetail.setRoomSize(tariff.getRoomDetails().getRoomSize());
                            roomDetail.setRoomName(tariff.getRoomDetails().getRoomName());
                            roomDetail.setRoomViewName(tariff.getRoomDetails().getRoomViewName());
                            roomDetail.setImages(getImages(tariff.getRoomDetails().getImages()));
                            roomDetail.setMedia(buildMedia(roomDetail.getImages() , tariff.getRoomDetails().getRoomLevelVideos()));
                            roomDetail.setView360(buildView360Images(tariff.getRoomDetails().getImage360List()));
                            roomDetail.setMaxGuest(tariff.getRoomDetails().getMaxGuestCount());
                            roomDetail.setMaxAdult(tariff.getRoomDetails().getMaxAdultCount());
                            roomDetail.setMaxChild(tariff.getRoomDetails().getMaxChildCount());
                            /* Below three nodes are duplicate & need to be removed after next client release */
                            /* Only Temp addition to fix live bug */
                            roomDetail.setMaxGuestCount(tariff.getRoomDetails().getMaxGuestCount());
                            roomDetail.setMaxAdultCount(tariff.getRoomDetails().getMaxAdultCount());
                            roomDetail.setMaxChildCount(tariff.getRoomDetails().getMaxChildCount());
                            /* Above three nodes are duplicate & need to be removed after next client release */
                            roomDetail.setHighlightedAmenities(getHighlightedAmenities(tariff));


                            roomDetail.setRoomHighlights(getRoomHighlights(tariff.getRoomDetails(),utility.isExperimentOn(txnData.getExpData(), ExperimentKeys.PILGR_IMAGE_BED_INFO.getKey()) ));
                            roomDetail.setRatePlans(getRatePlans(tariff, bnplApplicable, confirmationPolicyType, ap, txnData, (hotel.getHotelInfo() != null && hotel.getHotelInfo().isAltAcco())));

                            boolean isAmenitiesV2Enabled = utility.isAmenitiesV2Enabled(txnData.getExpData());
                            roomDetail.setAmenities(getFacilities(commonResponseTransformer.getAmenities(tariff.getRoomDetails().getFacilityWithGrp(), isAmenitiesV2Enabled), tariff.getRoomDetails().getStarFacilities()));
                            roomDetail.setRoomSummary(tariff.getRoomDetails().getRoomSummary());
                            roomDetail.setUsp(tariff.getRoomDetails().getRoomUsp());
                            roomDetail.setDescription(tariff.getRoomDetails().getDescription());
                            if (MapUtils.isNotEmpty(txnData.getPrivateSpaceMap())) {
                                roomDetail.setPrivateSpaces(convertSpaceData(txnData.getPrivateSpaceMap().get(tariff.getRoomCode())));
                            }
                            roomDetail.setExtraGuestInfo(getExtraGuestInfo(tariff.getRoomDetails().getReviewPageDetailExtraBedText(), (hotel.getHotelInfo() != null && hotel.getHotelInfo().isAltAcco())));
                            if(txnData.getAvailReqBody()!= null && StringUtils.equalsIgnoreCase(txnData.getAvailReqBody().getFunnelSource(), FUNNEL_SOURCE_GROUP_BOOKING) &&
                                    tariff.isBaseRoom()) {
                                roomDetail.setDisplayName(totalNumRooms + SPACE_X_SPACE + tariff.getRoomTypeName());
                            }
                            roomDetail.setRoomCategoryText(hotel.getHotelInfo() != null && LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotel.getHotelInfo().getListingType())
                                    ? hotel.getHotelInfo().getPropertyType()!=null?hotel.getHotelInfo().getPropertyType():SELLABLE_ROOM_TYPE
                                    : (tariff.getSellableType()!=null?tariff.getSellableType():SELLABLE_ROOM_TYPE));
                            roomDetail.setExtraBedAvailable(tariff.getRoomDetails().isAllowExtraBed() && tariff.getRoomDetails().isAllowExtraCrib());
                            roomInfoResponse.setRoomInfo(roomDetail);
                        }
                    }
                }
            }
            roomInfoResponse.setSharedSpaces(convertSpaceData(txnData.getSharedSpace()));
            if (txnData.getFlexiCancelAddOnInfo() != null && txnData.getFlexiCancelAddOnInfo().getFlexiDetailBottomSheet() != null) {
                roomInfoResponse.setFlexiDetailBottomSheet(buildFlexiDetailBottomSheet(txnData.getFlexiCancelAddOnInfo().getFlexiDetailBottomSheet()));
            }
        }
        return roomInfoResponse;
    }

    private FlexiDetailBottomSheet buildFlexiDetailBottomSheet(com.mmt.hotels.model.response.pricing.FlexiDetailBottomSheet flexiDetailBottomSheet) {
        FlexiDetailBottomSheet flexiDetailBottomSheetResponse = new FlexiDetailBottomSheet();
        flexiDetailBottomSheetResponse.setTitleText(flexiDetailBottomSheet.getTitleText());
        flexiDetailBottomSheetResponse.setSelected(utility.getSelected(flexiDetailBottomSheet.getSelected()));
        flexiDetailBottomSheetResponse.setUnselected(utility.getSelected(flexiDetailBottomSheet.getUnselected()));
        return flexiDetailBottomSheetResponse;
    }


    private AtomicInteger getTotalNumOfRooms(PersistedHotel hotel) {
        AtomicInteger totalNumRooms = new AtomicInteger();
        if(CollectionUtils.isNotEmpty(hotel.getTariffInfoList())) {
            List<PersistedTariffInfo> tariffInfoList = hotel.getTariffInfoList().stream().filter(persistedTariffInfo -> persistedTariffInfo.getAvailDetails() != null)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tariffInfoList)) {
                tariffInfoList.forEach(persistedTariffInfo -> totalNumRooms.addAndGet(persistedTariffInfo.getAvailDetails().getNumOfRooms()));
            }
        }
        return totalNumRooms;
    }

    private SpaceData convertSpaceData(com.mmt.hotels.model.response.staticdata.SpaceData sharedSpace) {
        if(sharedSpace == null){
            return null;
        }
        SpaceData spaceData = new SpaceData();
        spaceData.setDescriptive(sharedSpace.getDescriptive());
        return spaceData;
    }

    private View360Image buildView360Images(List<Image360> image360List){
        if(CollectionUtils.isNotEmpty(image360List)){
            View360Image view360Image = new View360Image();
            view360Image.setImages(image360List);
            view360Image.setCtaText(polyglotService.getTranslatedData(ConstantsTranslation.CTA_360_TEXT));
            view360Image.setCtaIcon(view360IconUrl);
            return view360Image;
        }
        return null;
    }

    private List<MediaData> buildMedia(List<String> images, List<VideoInfo> roomLevelVideos) {


        List<MediaData> mediaList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(roomLevelVideos)) {

            for (VideoInfo videoInfo : roomLevelVideos) {

                MediaData mediaData = new MediaData();
                mediaData.setMediaType(Constants.VIDEO_TYPE);
                mediaData.setUrl(videoInfo.getUrl());
                mediaData.setThumbnailUrl(videoInfo.getThumbnailUrl());
                mediaData.setText(videoInfo.getText());
                mediaList.add(mediaData);
            }


        }

        if (CollectionUtils.isNotEmpty(images)) {

            for (String url : images) {
                MediaData mediaData = new MediaData();
                mediaData.setMediaType(Constants.IMAGE_TYPE);
                mediaData.setUrl(url);
                mediaList.add(mediaData);
            }
        }
        return mediaList;


    }

    private List<RoomHighlight> getRoomHighlights(ReviewRoomInfo roomInfo, boolean pilgrimageBedInfoEnable) {
        List<RoomHighlight> roomHighlights = new ArrayList<>();

        if (roomInfo == null)
            return null;

        if(StringUtils.isNotBlank(roomInfo.getRoomSize())) {
            RoomHighlight roomHighlight = new RoomHighlight();
            roomHighlight.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/size.png");
            roomHighlight.setText(roomInfo.getRoomSize());
            roomHighlight.setDescription(roomHighlight.getText());
            roomHighlights.add(roomHighlight);
        }

        if(StringUtils.isNotBlank(roomInfo.getRoomViewName())) {
            RoomHighlight roomHighlight = new RoomHighlight();
            roomHighlight.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/view.png");
            roomHighlight.setText(roomInfo.getRoomViewName());
            roomHighlight.setDescription(roomInfo.getRoomViewName());
            roomHighlights.add(roomHighlight);
        }
        if (CollectionUtils.isNotEmpty(roomInfo.getBathrooms())) {
            RoomHighlight roomHighlight = new RoomHighlight();
            roomHighlight.setIconUrl(Constants.IMAGE_URL_BATHROOM_TYPE);
            BathroomArrangement bathroomArrangement= roomInfo.getBathrooms().get(0);
            String bedTypeText;
            if (bathroomArrangement.getCount() > 1) {
                bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_BATHROOMS_TEXT);
            } else {
                bedTypeText = bathroomArrangement.getCount() + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_BATHROOM_TEXT);
            }
            roomHighlight.setText(bedTypeText);
            roomHighlight.setDescription(bedTypeText);
            roomHighlights.add(roomHighlight);
        }
        if(pilgrimageBedInfoEnable) {
            if (CollectionUtils.isNotEmpty(roomInfo.getBeds()) || CollectionUtils.isNotEmpty(roomInfo.getAlternateBeds())) {
                RoomHighlight roomHighlight = new RoomHighlight();
                roomHighlight.setIconUrl(Constants.IMAGE_URL_ROOM_TYPE);
                List<String> bedTypeList = new ArrayList<>();
                List<String> alternateBedTypeList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(roomInfo.getBeds())) {
                    roomInfo.getBeds().forEach(bedType -> {
                        String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
                        bedTypeList.add(bedTypeText);
                    });
                }
                if (CollectionUtils.isNotEmpty(roomInfo.getAlternateBeds())) {
                    roomInfo.getAlternateBeds().forEach(bedType -> {
                        String bedTypeText = bedType.getCount() + SPACE + bedType.getType();
                        alternateBedTypeList.add(bedTypeText);
                    });
                }

                String bedTypeListString = "";
                if (!bedTypeList.isEmpty()) {
                    bedTypeListString = String.join(COMMA_SPACE, bedTypeList);
                    if (!alternateBedTypeList.isEmpty()) {
                        bedTypeListString = bedTypeListString + SPACE + polyglotService.getTranslatedData(ROOM_DETAILS_ALTERNATE_BED_TYPE_OR_TEXT) + SPACE + String.join(COMMA_SPACE, alternateBedTypeList);
                    }
                } else {
                    bedTypeListString = String.join(COMMA_SPACE, alternateBedTypeList);
                }
                roomHighlight.setText(bedTypeListString);
                roomHighlight.setDescription(bedTypeListString);
                if (StringUtils.isNotEmpty(roomInfo.getReviewPageDetailExtraBedText())) {
                    String[] tokens = roomInfo.getReviewPageDetailExtraBedText().split(Constants.HASH_SEPARATOR);
                    roomHighlight.setSubText(tokens[0]);
                }
                roomHighlights.add(roomHighlight);
            }
        }else {
            if(CollectionUtils.isNotEmpty(roomInfo.getBeds()) && StringUtils.isNotBlank(roomInfo.getBeds().get(0).getType())) {
                RoomHighlight roomHighlight = new RoomHighlight();
                roomHighlight.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/bed.png");
                roomHighlight.setText(roomInfo.getBeds().get(0).getType());
                roomHighlight.setDescription(roomInfo.getBeds().get(0).getType());
                if(StringUtils.isNotEmpty(roomInfo.getReviewPageDetailExtraBedText())){
                    String[] tokens=roomInfo.getReviewPageDetailExtraBedText().split(Constants.HASH_SEPARATOR);
                    roomHighlight.setSubText(tokens[0]);
                }
                roomHighlights.add(roomHighlight);
            }
        }
        return roomHighlights;
    }

    private ExtraGuestInfo getExtraGuestInfo(String reviewPageDetailText, boolean isAltAcco) {
        ExtraGuestInfo extraGuestInfo = null;
        if (!isAltAcco && StringUtils.isNotEmpty(reviewPageDetailText)) {
            String[] tokens = reviewPageDetailText.split(Constants.HASH_SEPARATOR);
            if (tokens.length > 1 && !NULL_STRING.equalsIgnoreCase(tokens[1])) {
                extraGuestInfo = new ExtraGuestInfo();
                extraGuestInfo.setExtraGuestInfoHeading(polyglotService.getTranslatedData(ConstantsTranslation.ROOM_DETAILS_EXTRA_GUEST_INFO_HEADING));
                extraGuestInfo.setExtraGuestInfoDscr(tokens[1]);
            }
        }
        return extraGuestInfo;
    }

    private List<String> getImages(LinkedList<String> images) {
		if (CollectionUtils.isEmpty(images))
			return null;
		LinkedList<String> imageList = new LinkedList<>();
		for (String image: images) {
			imageList.add("https:"+image);
		}
		return imageList;
	}

	private List<Inclusion> getBlackInclusions(List<Inclusion> inclusionList) {
    	if (CollectionUtils.isEmpty(inclusionList))
    		return null;
    	List<Inclusion> blackInclusions = new ArrayList<>();
    	for (Inclusion inclusion : inclusionList) {
    		if (Constants.BLACK_INCLUSION_IDENTIFIER.equalsIgnoreCase(inclusion.getSegmentIdentifier()))
    			blackInclusions.add(inclusion);
    	}
    	if (CollectionUtils.isEmpty(blackInclusions))
    		return null;
    	return blackInclusions;
	}

	private List<com.mmt.hotels.clientgateway.response.FacilityGroup> getFacilities(List<SelectRoomAmenities> amenities , List<FacilityGroup> starFacilities) {
		if (CollectionUtils.isEmpty(amenities))
			return null;

        Set<String> starAmenitySet = new HashSet<>();

        if (CollectionUtils.isNotEmpty(starFacilities)) {
            for (com.mmt.model.FacilityGroup facilityGroup : starFacilities) {
                if (CollectionUtils.isNotEmpty(facilityGroup.getFacilities())){
                    for (Facility facility : facilityGroup.getFacilities()){
                        starAmenitySet.add(facility.getName());
                    }
                }

            }
        }
        List<com.mmt.hotels.clientgateway.response.Facility> starFacilityCGs = new ArrayList<>();


        List<com.mmt.hotels.clientgateway.response.FacilityGroup> facilityGroupList = new ArrayList<>();
		for (SelectRoomAmenities amenity: amenities) {
			com.mmt.hotels.clientgateway.response.FacilityGroup facilityGroup = new com.mmt.hotels.clientgateway.response.FacilityGroup();
			facilityGroup.setName(amenity.getName());
			if (CollectionUtils.isNotEmpty(amenity.getFacilities())) {
				List<com.mmt.hotels.clientgateway.response.Facility> facilitiesList = new ArrayList<>();
				for (SelectRoomFacility selectRoomFacility: amenity.getFacilities()) {
					com.mmt.hotels.clientgateway.response.Facility facility = new com.mmt.hotels.clientgateway.response.Facility();
					facility.setName(selectRoomFacility.getName());
					facility.setSubText(selectRoomFacility.getSubText());

                    if (starAmenitySet.contains(facility.getName())){
                        starAmenitySet.remove(facility.getName());
                        starFacilityCGs.add(facility);
                    }else{
                        facilitiesList.add(facility);
                    }

//					facilitiesList.add(facility);
				}
				facilityGroup.setFacilities(facilitiesList);
			}
			facilityGroupList.add(facilityGroup);
		}

        if (CollectionUtils.isNotEmpty(starFacilities) && !starAmenitySet.isEmpty()){
            for (com.mmt.model.FacilityGroup facilityGroup : starFacilities) {
                for (Facility facilityHes : facilityGroup.getFacilities()) {

                    if (!starAmenitySet.contains(facilityHes.getName())){
                        continue;
                    }
                    com.mmt.hotels.clientgateway.response.Facility facilityCG = new com.mmt.hotels.clientgateway.response.Facility();

                    facilityCG.setName(facilityHes.getName());
                    starFacilityCGs.add(facilityCG);
                }
            }

        }

        if (CollectionUtils.isNotEmpty(starFacilityCGs)){

            com.mmt.hotels.clientgateway.response.FacilityGroup facility = new com.mmt.hotels.clientgateway.response.FacilityGroup();
            facility.setName(polyglotService.getTranslatedData(ConstantsTranslation.STAR_FACILITIES));
            facility.setType(Constants.BOLD_TYPE);
            facility.setFacilities(starFacilityCGs);
            facilityGroupList.add(0, facility);
        }




		return facilityGroupList;
	}

    private List<SelectRoomRatePlan> getRatePlans(PersistedTariffInfo tariff, boolean bnplApplicable,
                                                  String confirmationPolicyType, Integer ap, PersistedMultiRoomData txnData,
                                                  boolean isAltAcco) {
        boolean enableThemification = utility.isExperimentTrue(txnData.getExpData(), THEMIFICATION_ENABLED);
        List<SelectRoomRatePlan> selectRoomRatePlanList = new ArrayList<>();
        SelectRoomRatePlan selectRoomRatePlan = new SelectRoomRatePlan();
        selectRoomRatePlan.setCancellationTimeline(getCancellationTimeline(tariff.getCancellationTimeline(), bnplApplicable));
        selectRoomRatePlan.setCancellationPolicyTimeline(getCancellationPolicyTimeline(tariff.getCancellationTimeline(), bnplApplicable, enableThemification));
        selectRoomRatePlan.setAllInclusiveRate(tariff.isAllInclusiveRate());
        // HTL-42803:  TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
        boolean isBnplOneVariant = false;
        if (MapUtils.isNotEmpty(txnData.getExpData())) {
            isBnplOneVariant = txnData.getExpData().containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(txnData.getExpData().get(EXP_BNPL_NEW_VARIANT));
        }
        BNPLVariant bnplVariant = txnData.getTotalDisplayFare() != null ? txnData.getTotalDisplayFare().getBnplVariant() : null;
        String partialRefundText = utility.buildPartialRefundDateText(tariff.getCancellationTimeline());
        selectRoomRatePlan.setCancellationPolicy(utility.transformCancellationPolicy(tariff.getCancelPenaltyList(), bnplApplicable, isBnplOneVariant, bnplVariant, confirmationPolicyType, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap, Optional.empty(), partialRefundText, false));
        updateCancelPolicyDescription(selectRoomRatePlan.getCancellationPolicy(), tariff.getCancelPenaltyList());
        List<Integer> childAges = null;
        if (null != tariff.getAvailDetails() && null != tariff.getAvailDetails().getOccupancyDetails()) {
            childAges = tariff.getAvailDetails().getOccupancyDetails().getChildAges();
        }
        selectRoomRatePlan.setInclusionsList(utility.transformInclusions(
                tariff.getMealPlans(), tariff.getInclusions(), mealPlanMapPolyglot,
                tariff.getSupplierDetails() != null ? tariff.getSupplierDetails().getSupplierCode() : "",
                ap, null, txnData.getExpData(), isAltAcco,
                tariff.getFreeChildCount() > 0 ? tariff.getFreeChildText() : null,
                txnData.getHotelList().get(0).getHotelInfo().getChainCode(),
                txnData.getHotelList().get(0).getHotelInfo().getHtlAttributes(), childAges,
                txnData.getHotelList().get(0).getHotelInfo().getCountryCode(),
                tariff.isMealAvailableAtProperty(), IconType.DEFAULT, true, Boolean.TRUE.equals(txnData.getIsExtraAdultChild())
        ));
        selectRoomRatePlan.setTariffs(getTariffs(tariff.getRoomTariff(), tariff.getAvailDetails()));
        selectRoomRatePlan.setSellableType(StringUtils.isNotBlank(tariff.getSellableType()) ? tariff.getSellableType() : Constants.SELLABLE_ROOM_TYPE);
        if(tariff != null && tariff.getSupplierDetails() != null){
            String supplierCode = tariff.getSupplierDetails().getSupplierCode();
            Utility.setCanTranslateFlag(selectRoomRatePlan, translateEnabledSupplierCodes,supplierCode);
        }
        selectRoomRatePlanList.add(selectRoomRatePlan);
        return selectRoomRatePlanList;
    }

    private ExtraBedPolicy getExtraBedPolicy(HouseRules houseRules) {
        ExtraBedPolicy extraBedPolicy = null;

        if (houseRules != null && CollectionUtils.isNotEmpty(houseRules.getCategoryInfoList())) {
            for (CategoryInfo categoryInfo : houseRules.getCategoryInfoList()) {
                if(categoryInfo.getId().equalsIgnoreCase("EXTRA_BED_POLICY")) {
                    RuleTableInfo ruleTableInfo = Utility.getParsedRuleTableInfo(categoryInfo.getRuleTableInfo());
                    if (ruleTableInfo == null) {
                        return null;
                    }
                    extraBedPolicy =  new ExtraBedPolicy();
                    extraBedPolicy.setDesc(categoryInfo.getCategoryDesc());
                    extraBedPolicy.setTitle(categoryInfo.getCategoryHeading());
                    extraBedPolicy.setRules(categoryInfo.getRuleDesc());
                    extraBedPolicy.setRuleTableInfo(ruleTableInfo);
                }
            }
        }
        return extraBedPolicy;
    }

    private CancellationPolicyTimeline getCancellationPolicyTimeline(com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline, boolean bnplApplicable, boolean enableThemification) {
        if (cancellationTimeline == null)
            return null;
        CancellationPolicyTimeline cancellationPolicyTimeline = commonResponseTransformer.buildCancellationPolicyTimeline(cancellationTimeline, enableThemification, null);
        if (!bnplApplicable && cancellationPolicyTimeline != null) {
            cancellationPolicyTimeline.setCardChargeDate(null);
            cancellationPolicyTimeline.setCardChargeDateTime(null);
            cancellationPolicyTimeline.setCardChargeText(null);
            cancellationPolicyTimeline.setBookingAmountText(null);
        }
        return cancellationPolicyTimeline;
    }

    private List<Tariff> getTariffs(List<com.mmt.hotels.model.response.pricing.RoomTariff> list, AvailDetails availDetails) {
        if (CollectionUtils.isEmpty(list))
            return null;
        List<Tariff> tariffList = new ArrayList<>();
        List<RoomTariff> roomTariffs = new ArrayList<>();
        for (com.mmt.hotels.model.response.pricing.RoomTariff roomTariff : list) {
            RoomTariff tariff = new RoomTariff();
            tariff.setNumberOfAdults(roomTariff.getNumberOfAdults());
            tariff.setNumberOfChildren(roomTariff.getNumberOfChildren());
            roomTariffs.add(tariff);
        }
        tariffList.add(new Tariff());
        tariffList.get(0).setRoomTariffs(roomTariffs);
        if(availDetails != null && availDetails.getOccupancyDetails() != null) {
            RoomTariff roomTariff = new RoomTariff();
            roomTariff.setRoomCount(availDetails.getOccupancyDetails().getNumOfRooms());
            roomTariff.setNumberOfAdults(availDetails.getOccupancyDetails().getAdult());
            roomTariff.setNumberOfChildren(availDetails.getOccupancyDetails().getChild());
            tariffList.get(0).setOccupancydetails(roomTariff);
        }
        return tariffList;
    }

	private CancellationTimeline getCancellationTimeline(
			com.mmt.hotels.model.response.pricing.CancellationTimeline cancellationTimeline, boolean bnplApplicable) {
		if (cancellationTimeline == null)
			return null;
		CancellationTimeline cancellationTimelineCG = new CancellationTimeline();
        cancellationTimelineCG.setBookingDate(cancellationTimeline.getBookingDate());
        cancellationTimelineCG.setCancellationDate(cancellationTimeline.getCancellationDate());
        cancellationTimelineCG.setCancellationDateTime(cancellationTimeline.getCancellationDateTime());
        if (bnplApplicable) {
            cancellationTimelineCG.setCardChargeDate(cancellationTimeline.getCardChargeDate());
            cancellationTimelineCG.setCardChargeDateTime(cancellationTimeline.getCardChargeDateTime());
            cancellationTimelineCG.setCardChargeText(cancellationTimeline.getCardChargeText());
            cancellationTimelineCG.setBookingAmountText(cancellationTimeline.getBookingAmountText());
        }
        cancellationTimelineCG.setCheckInDate(cancellationTimeline.getCheckInDate());
        cancellationTimelineCG.setCheckInDateTime(cancellationTimeline.getCheckInDateTime());
        cancellationTimelineCG.setDateFormat(cancellationTimeline.getDateFormat());
        cancellationTimelineCG.setFreeCancellationText(cancellationTimeline.getFreeCancellationText());
        cancellationTimelineCG.setFreeCancellationBenefits(commonResponseTransformer.buildFreeCancellationBenefits(cancellationTimeline.getFreeCancellationBenefits(), null));
        cancellationTimelineCG.setSubTitle(cancellationTimeline.getSubTitle());
        cancellationTimelineCG.setTitle(cancellationTimeline.getTitle());
        return cancellationTimelineCG;
    }

	public RoomInfoResponse transformRoomInfoResponse(RoomInfoRequest roomInfoRequest, PersistanceMultiRoomResponseEntity txnDataEntity){
        /* Maps to OLD cg/room-info API */
        RoomInfoResponse roomInfoResponse = new RoomInfoResponse();
        PersistedMultiRoomData txnData = txnDataEntity.getPersistedData();
        if(null != txnData){
            String checkIn = (txnData.getAvailReqBody()!=null) ? txnData.getAvailReqBody().getCheckin():StringUtils.EMPTY;
            Integer ap = (StringUtils.isNotBlank(checkIn)) ? dateUtil.getDaysDiff(LocalDate.now(),LocalDate.parse(checkIn)) : null;
            roomInfoResponse.setCancellationTimeline(txnData.getCancellationTimeline());
            BNPLDetails bnplDetails = getBnplDetails(txnData.getTotalDisplayFare());
            boolean bnplApplicable = (bnplDetails!=null) ? bnplDetails.isBnplApplicable() : false;
            for(PersistedHotel hotel : txnData.getHotelList()){
                String confirmationPolicyType = null;
                RatePolicy ratePolicy = getConfirmationPolicy(hotel.getTariffInfoList());
                if(null != ratePolicy)
                    confirmationPolicyType = ratePolicy.getValue();
                for(PersistedTariffInfo tariff: hotel.getTariffInfoList()){
                    if(roomInfoRequest.getRoomCode().equalsIgnoreCase(tariff.getRoomCode())){
                        if(null != tariff.getRoomDetails()) {
                            roomInfoResponse.setBeds(tariff.getRoomDetails().getBeds());
                            roomInfoResponse.setRoomSize(tariff.getRoomDetails().getRoomSize());
                            roomInfoResponse.setRoomViewName(tariff.getRoomDetails().getRoomViewName());
                            roomInfoResponse.setImages(tariff.getRoomDetails().getImages());
                            roomInfoResponse.setMaxGuestCount(tariff.getRoomDetails().getMaxGuestCount());
                            roomInfoResponse.setMaxAdultCount(tariff.getRoomDetails().getMaxAdultCount());
                            roomInfoResponse.setMaxChildCount(tariff.getRoomDetails().getMaxChildCount());
                            //get highlightedAmenities
                            List<String> highlightedAmenities = getHighlightedAmenities(tariff);
                            roomInfoResponse.setHighlightedAmenities(highlightedAmenities);

                            boolean isAmenitiesV2Enabled = utility.isAmenitiesV2Enabled(txnData.getExpData());
                            roomInfoResponse.setAmenities(commonResponseTransformer.getAmenities(tariff.getRoomDetails().getFacilityWithGrp(), isAmenitiesV2Enabled));

                        }
                        // HTL-42803:  TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
                        boolean isBnplOneVariant = false;
                        if (MapUtils.isNotEmpty(txnData.getExpData())) {
                            isBnplOneVariant = txnData.getExpData().containsKey(EXP_BNPL_NEW_VARIANT) && Boolean.parseBoolean(txnData.getExpData().get(EXP_BNPL_NEW_VARIANT));
                        }

                        BNPLVariant bnplVariant = txnData.getTotalDisplayFare() != null ? txnData.getTotalDisplayFare().getBnplVariant() : null;
                        String partialRefundText = utility.buildPartialRefundDateText(tariff.getCancellationTimeline());
                        roomInfoResponse.setCancellationPolicy(utility.transformCancellationPolicy(tariff.getCancelPenaltyList(), bnplApplicable, isBnplOneVariant, bnplVariant, confirmationPolicyType, polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT), ap, Optional.empty(), partialRefundText, false));
                        updateCancelPolicyDescription(roomInfoResponse.getCancellationPolicy(), tariff.getCancelPenaltyList());
                        roomInfoResponse.setInclusions(utility.transformInclusions(
                                tariff.getMealPlans(), tariff.getInclusions(), mealPlanMapPolyglot,
                                tariff.getSupplierDetails() != null ? tariff.getSupplierDetails().getSupplierCode() : "",
                                ap, null, txnData.getExpData(), hotel.getHotelInfo().isAltAcco(),
                                tariff.getFreeChildCount() > 0 ? tariff.getFreeChildText() : null,
                                null, null, null, null, tariff.isMealAvailableAtProperty(), IconType.DEFAULT, true,
                                Boolean.TRUE.equals(txnData.getIsExtraAdultChild())));
                        if (tariff.getRoomDetails().getRoomSummary() != null) {
                            roomInfoResponse.setRoomInfo(new RoomDetails());
                            roomInfoResponse.getRoomInfo().setRoomSummary(tariff.getRoomDetails().getRoomSummary());
                        }
                    }
                }
            }
        }
        return roomInfoResponse;
    }

    private List<String> getHighlightedAmenities(PersistedTariffInfo tariff) {
        List<String> highlightedAmenities = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tariff.getRoomDetails().getHighlightedFacilities())) {
            for (FacilityGroup fg : tariff.getRoomDetails().getHighlightedFacilities()) {
                if(CollectionUtils.isNotEmpty(fg.getFacilities())){
                    for(Facility facility : fg.getFacilities()){
                        highlightedAmenities.add(facility.getHighlightedName());
                    }
                }
            }
        }
        return highlightedAmenities;
    }

    private void updateCancelPolicyDescription(BookedCancellationPolicy bookedCancellationPolicy, List<CancelPenalty> cancelPenalty) {
        if (CollectionUtils.isNotEmpty(cancelPenalty) && cancelPenalty.get(0).getPenaltyDescription() != null
                && cancelPenalty.get(0).getPenaltyDescription().getDescription() != null) {
            bookedCancellationPolicy.setDescription(cancelPenalty.get(0).getPenaltyDescription().getDescription());
        }
    }

    private RatePolicy getConfirmationPolicy(List<PersistedTariffInfo> tariffs) {
        RatePolicy confirmationPolicy = null;
            for (PersistedTariffInfo tariff : tariffs) {
                RatePolicy ratePolicy = tariff.getConfirmationPolicy();
                if (confirmationPolicy == null && ratePolicy !=null) {
                    confirmationPolicy = ratePolicy;
                    if(Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                        return confirmationPolicy;
                    }
                } else if (confirmationPolicy !=null && Constants.MOST_RESTRICTED_POLICY.equalsIgnoreCase(ratePolicy.getMostRestrictive())) {
                    confirmationPolicy = ratePolicy;
                    return confirmationPolicy;
                }
            }
        return confirmationPolicy;
    }

    private BNPLDetails getBnplDetails(DisplayFare displayFare) {
        if (displayFare !=null) {
            return commonResponseTransformer.buildBNPLDetails(displayFare.getIsBNPLApplicable(), displayFare.getBnplPersuasionMsg(), displayFare.getBnplPolicyText(), null, null, displayFare.isOriginalBNPL(), false, displayFare.getBnplVariant(), 0.0);
        }
        return null;
    }
}
