package com.mmt.hotels.clientgateway.transformer.response;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.MyPartnerConfigConsul;
import com.mmt.hotels.clientgateway.enums.ExperimentKeys;
import com.mmt.hotels.clientgateway.enums.ReviewCategoryConstants;
import com.mmt.hotels.clientgateway.helpers.PricingEngineHelper;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.Address;
import com.mmt.hotels.clientgateway.response.ManualPersuasion;
import com.mmt.hotels.clientgateway.response.ReviewSummary;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.flyfish.AltAccoPer;
import com.mmt.hotels.clientgateway.response.moblanding.CardAction;
import com.mmt.hotels.clientgateway.response.moblanding.CardActionData;
import com.mmt.hotels.clientgateway.response.moblanding.Section;
import com.mmt.hotels.clientgateway.response.moblanding.Item;
import com.mmt.hotels.clientgateway.response.moblanding.SupportDetails;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.*;
import com.mmt.hotels.clientgateway.response.staticdetail.ChildExtraBedPolicy;
import com.mmt.hotels.clientgateway.response.staticdetail.CommonRules;
import com.mmt.hotels.clientgateway.response.staticdetail.ExtraBedRules;
import com.mmt.hotels.clientgateway.response.staticdetail.FoodDiningV2;
import com.mmt.hotels.clientgateway.response.staticdetail.GovtPolicies;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRules;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRulesV2;
import com.mmt.hotels.clientgateway.response.staticdetail.Image360.View360Image;
import com.mmt.hotels.clientgateway.response.staticdetail.MediaInfo;
import com.mmt.hotels.clientgateway.response.staticdetail.PolicyRules;
import com.mmt.hotels.clientgateway.response.staticdetail.Rule;
import com.mmt.hotels.clientgateway.response.staticdetail.StreetViewInfo;
import com.mmt.hotels.clientgateway.response.wishlist.WishListedHotelsDetailResponse;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.persuasion.response.PersuasionData;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.upsell.UpsellHotelDetailResponse;
import com.mmt.hotels.model.response.flyfish.*;
import com.mmt.hotels.model.response.flyfish.CountryWiseReviewData;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.Image360.Image360;
import com.mmt.hotels.pojo.FoodAndDining.FoodAndDiningEnums;
import com.mmt.hotels.pojo.request.detail.mob.CBFlyFishSummaryResponse;
import com.mmt.hotels.pojo.request.detail.mob.CBPlatformSummaryResponse;
import com.mmt.hotels.pojo.response.detail.HotelDetailWrapperResponse;
import com.mmt.hotels.pojo.response.detail.PlacesResponse;
import com.mmt.hotels.pojo.response.detail.placesapi.Category;
import com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum;
import com.mmt.hotels.pojo.response.wishlist.FlyfishReviewWrapperResponse;
import com.mmt.hotels.pojo.response.wishlist.HotStoreHotelsWrapperResponse;
import com.mmt.model.RoomInfo;
import com.mmt.model.UGCRatingData;
import lombok.NonNull;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.BEACHFRONT_CATEGORY_USP_DETAILS_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXTRA_BED_POLICY;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_EXTRA_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_HINT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_ITEM_COUNT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FAQ_TITLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FOOD_AND_DINING;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.RESTRICTIONS;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.SPOKEN_LANGUAGE_HEADER;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STATIC_ROOM_TAG;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STREET_VIEW_SUBTITLE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.STREET_VIEW_TITLE_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.util.Utility.isMyPartnerRequest;
import static java.lang.Math.min;

public abstract class StaticDetailResponseTransformer {

	@Autowired
	ObjectMapperUtil objectMapperUtil;

	@Autowired
	protected CommonResponseTransformer commonResponseTransformer;

	@Value("${value.stay.icon}")
	private String iconUrl;

	@Value("${value.stay.icon.gcc}")
	private String iconUrlGcc;

	@Value("${mypartner.gst.assured.icon.url}")
	private String iconUrlGSTAssured;

	@Value("${food.dining.min.count.config}")
	private int foodDiningMinCountConfig;

	@Value("${food.menu.position.config}")
	private int foodMenuPosition;

	@Value("${luxe.icon.new.detail.page.dt}")
	private String luxeIconNewDetailPageDt;

	@Value("${view.360.icon.url}")
	private String view360IconUrl;

	@Value("${sponsored.hotel.icon.url}")
	private String sponsoredHotelIconUrl;

	@Value("${view.360.persuasionIcon.url}")
	private String view360PersuasionIconUrl;

	@Value("${detail.grid.image.limit}")
	private int detailGridImageLimit;

	@Value("${listing.media.limit.exp}")
	private int listingMediaLimitExp;

	@Autowired
	private PolyglotService polyglotService;

	@Autowired
	private Utility utility;

	@Autowired
	private DateUtil dateUtil;

	private static final Logger LOGGER = LoggerFactory.getLogger(StaticDetailResponseTransformer.class);


	@Autowired
	private SearchHotelsFactory searchHotelsFactory;

	@Autowired
	private MetricAspect metricAspect;

	@Value("#{'${food.dining.merge.sections}'.split(',')}")
	private List<String> foodDiningMergeSections;

	@Value("#{'${suppressed.houseRules.list}'.split(',')}")
	private List<Integer> supressedHouseRulesList;
	@Autowired
	private MyPartnerConfigConsul myPartnerConfigConsul;

	@Autowired
	PricingEngineHelper pricingEngineHelper;

	@Autowired
	CommonConfigConsul commonConfigConsul;

	private static final Logger logger = LoggerFactory.getLogger(StaticDetailResponseTransformer.class);

	@Value("${business.rating.icon.url}")
	private String businessRatingIconUrl;

	public StaticDetailResponse convertStaticDetailResponse(HotelDetailWrapperResponse hotelDetailWrapperResponse,
															String client, StaticDetailRequest staticDetailRequest,
															CommonModifierResponse commonModifierResponse) {
		StaticDetailResponse staticDetailResponse = new StaticDetailResponse();
		Map<String, String> expDataMap = utility.getExpDataMap(staticDetailRequest.getExpData());
		boolean myPartnerReq = false;
		staticDetailResponse.setCurrency(hotelDetailWrapperResponse.getCurrency());
		if(commonModifierResponse!=null && commonModifierResponse.getExtendedUser()!=null) {
			myPartnerReq = isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
		}

		boolean isLiteResponse = staticDetailRequest != null && staticDetailRequest.getFeatureFlags() != null && staticDetailRequest.getFeatureFlags().isLiteResponse();

        staticDetailResponse.setHotelDetails(getHotelResult(staticDetailRequest,hotelDetailWrapperResponse.getHotelResult(), hotelDetailWrapperResponse.getContext(), expDataMap, staticDetailRequest.getSearchCriteria(), staticDetailRequest.getDeviceDetails(), isCorp(staticDetailRequest), utility.isGroupBookingFunnel(staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : null), staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : null, myPartnerReq));
        if(Objects.nonNull(myPartnerConfigConsul)){
            staticDetailResponse.setMyPartnerMarkupConfig(pricingEngineHelper.buildMarkUpConfig(hotelDetailWrapperResponse.getMarkUpDetails(),myPartnerConfigConsul.getMarkUpConfig()));
        }
		boolean isLuxe = hotelDetailWrapperResponse.getHotelResult() != null && hotelDetailWrapperResponse.getHotelResult().getLuxe() != null ? hotelDetailWrapperResponse.getHotelResult().getLuxe() : false;
		boolean isImageExpEnable = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(IMAGES_EXP_ENABLE) && TRUE.equalsIgnoreCase(expDataMap.get(IMAGES_EXP_ENABLE));
		boolean isChatBotEnable = hotelDetailWrapperResponse.getHotelResult() != null &&
				hotelDetailWrapperResponse.getHotelResult().isChatbotEnabled() &&
				MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(CHATBOT_HOOKS_EXP) && TRUE.equalsIgnoreCase(expDataMap.get(CHATBOT_HOOKS_EXP));;
		if(MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(EXP_GALLERY_V2) && EXP_TRUE_VALUE.equalsIgnoreCase(expDataMap.get(EXP_GALLERY_V2))){
			staticDetailResponse.setMediaV2(buildMediaV2(hotelDetailWrapperResponse.getHotelImage(),
					hotelDetailWrapperResponse.getHotelResult() != null ? hotelDetailWrapperResponse.getHotelResult().getListingType() : null,
					hotelDetailWrapperResponse.getHotelResult() != null ? hotelDetailWrapperResponse.getHotelResult().getProfessionalImageTagsOrder() : null,
					isLuxe,isImageExpEnable,client,isChatBotEnable));
		}

		else{
			staticDetailResponse.setMedia(buildMedia(hotelDetailWrapperResponse.getHotelImage(), hotelDetailWrapperResponse.getHotelResult(), hotelDetailWrapperResponse.getHotelResult() != null ? hotelDetailWrapperResponse.getHotelResult().getListingType() : null));
		}

		if(isLiteResponse && staticDetailRequest != null && CollectionUtils.isEmpty(staticDetailRequest.getUuids())){
			if(staticDetailResponse != null && staticDetailResponse.getMediaV2() != null){
				//set grid
				if(staticDetailResponse.getMediaV2().getGrid() != null && CollectionUtils.isNotEmpty(staticDetailResponse.getMediaV2().getGrid().getImages())) {
					staticDetailResponse.setHotelGridImages(staticDetailResponse.getMediaV2().getGrid().getImages());
				}

				//set hotelImageListCount
				if(staticDetailResponse.getMediaV2().getHotel() != null && CollectionUtils.isNotEmpty(staticDetailResponse.getMediaV2().getHotel().getTags())){
					staticDetailResponse.setHotelImageListCount(getMediaV2HotelMediaListCount(staticDetailResponse.getMediaV2().getHotel().getTags()));
				}

				//set hotelImageByTravellerCount and hotelImageByTravellerL1
				if(staticDetailResponse.getMediaV2().getTraveller() != null && CollectionUtils.isNotEmpty(staticDetailResponse.getMediaV2().getTraveller().getTags())) {
					List<LiteResponseTravellerImage> liteResponseTravellerImages = getMediaV2TravellerMediaList(staticDetailResponse.getMediaV2().getTraveller().getTags());
					if(CollectionUtils.isNotEmpty(liteResponseTravellerImages)){
						staticDetailResponse.setHotelImageByTravellerCount(liteResponseTravellerImages.size());
						staticDetailResponse.setHotelImageByTravellerL1(liteResponseTravellerImages.subList(0, min(liteResponseTravellerImages.size(), 4)));
					}
				}

			}
			staticDetailResponse.setMediaV2(null);
		}
		String countryCode = "";
		if (staticDetailRequest != null && staticDetailRequest.getSearchCriteria() != null && staticDetailRequest.getSearchCriteria().getCountryCode() != null) {
			countryCode = staticDetailRequest.getSearchCriteria().getCountryCode();
		}
		//On Old apps combined OTAs like MMT_BKG not supported, hence changing such OTA to MMT for old apps using exp COMBINED_OTA
		boolean combineOTASupported = utility.isExperimentOn(expDataMap, COMBINED_OTA);
		staticDetailResponse.setReviewSummary(getReviewSummary(hotelDetailWrapperResponse.getFlyfishSummaryResponse(), client, countryCode));
		staticDetailResponse.setUgcSummary(getUgcSummary(hotelDetailWrapperResponse.getPlatformSummaryResponse(), countryCode, combineOTASupported));
		if (null != hotelDetailWrapperResponse.getHotelCompareResponse()) {
			staticDetailResponse.setHotelCompareResponse(buildComparatorResponse(hotelDetailWrapperResponse.getHotelCompareResponse(), null != staticDetailRequest.getRequiredApis() && staticDetailRequest.getRequiredApis().isComparatorV2Required(), client, expDataMap, isLiteResponse));
		}
		if(utility.isExperimentOn(expDataMap, "CRI") ){
			if(null != hotelDetailWrapperResponse.getChainCompareResponse())
				staticDetailResponse.setChainCompareResponse(buildComparatorResponse(hotelDetailWrapperResponse.getChainCompareResponse(), null != staticDetailRequest.getRequiredApis() && staticDetailRequest.getRequiredApis().isComparatorV2Required(), client, expDataMap, isLiteResponse));
			staticDetailResponse.setHostSummary(hotelDetailWrapperResponse.getHostSummaryResponse());
		}
		if (null != hotelDetailWrapperResponse.getCollectionCompareResponse()) {
			ComparatorResponse  comparatorResponse = buildComparatorResponse(hotelDetailWrapperResponse.getCollectionCompareResponse(), null != staticDetailRequest.getRequiredApis() && staticDetailRequest.getRequiredApis().isComparatorV2Required(), client, expDataMap, isLiteResponse);
			 String title =  grtCollectionListingTitle(staticDetailRequest.getFilterCriteria());
			comparatorResponse.setTitle(title);
			staticDetailResponse.setCollectionListingResponse(comparatorResponse);
		}
		if(hotelDetailWrapperResponse!=null && hotelDetailWrapperResponse.getHotelImage()!=null && hotelDetailWrapperResponse.getHotelImage().getTreelGalleryData()!=null) {
			staticDetailResponse.setTreelGalleryData(hotelDetailWrapperResponse.getHotelImage().getTreelGalleryData());
		}
		if (null != hotelDetailWrapperResponse.getChatBotPersuasion()) {
			staticDetailResponse.setChatBotPersuasions(hotelDetailWrapperResponse.getChatBotPersuasion());
		}
		LinkedHashMap<String, String> linkedHashMapExpData = (null != commonModifierResponse && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap())) ? commonModifierResponse.getExpDataMap() : new LinkedHashMap<>();

		staticDetailResponse.setBhfPersuasions(commonResponseTransformer.getBhfPersuasions(hotelDetailWrapperResponse.getBhfPersuasions()));
		staticDetailResponse.setPlacesResponse(modifyPlacesResponse(hotelDetailWrapperResponse.getPlacesResponse()));
		staticDetailResponse.setPersuasionDetail(hotelDetailWrapperResponse.getPersuasionDetail());
		staticDetailResponse.setComparatorResponse(hotelDetailWrapperResponse.getComparatorResponse());
		staticDetailResponse.setWeaverResponse(hotelDetailWrapperResponse.getWeaverResponse());
		staticDetailResponse.setMyPartnerHero(hotelDetailWrapperResponse.getMyPartnerHero());
		staticDetailResponse.setDetailPersuasionCards(commonResponseTransformer.buildListPersonalizationResponse(hotelDetailWrapperResponse.getDetailPersuasionCards(), client, linkedHashMapExpData));
		staticDetailResponse.setRoomInfoMap(buildRoomInfoMap(hotelDetailWrapperResponse));
		staticDetailResponse.setExpVariantKeys(StringUtils.isNotBlank(staticDetailRequest.getExpVariantKeys()) ? staticDetailRequest.getExpVariantKeys() : null);
		staticDetailResponse.setUuids(hotelDetailWrapperResponse.getUuids());
		staticDetailResponse.setCompletedRequests(hotelDetailWrapperResponse.getCompletedRequests());
		if(MapUtils.isNotEmpty(hotelDetailWrapperResponse.getSocialMediaResponseMap()) && staticDetailRequest.getSearchCriteria()!=null && StringUtils.isNotEmpty(staticDetailRequest.getSearchCriteria().getHotelId()) && hotelDetailWrapperResponse.getSocialMediaResponseMap().containsKey(staticDetailRequest.getSearchCriteria().getHotelId())){
			staticDetailResponse.setSocialMediaResponse(hotelDetailWrapperResponse.getSocialMediaResponseMap().get(staticDetailRequest.getSearchCriteria().getHotelId()));
		}
		return staticDetailResponse;
	}


	public String grtCollectionListingTitle(List<Filter> filterCriteria){
		if (filterCriteria != null && !filterCriteria.isEmpty()) {
			return filterCriteria.stream()
					.filter(filter -> filter.getFilterGroup().equals(FilterGroup.DPT_COLLECTIONS))
					.map( filter ->polyglotService.getTranslatedData(MORE_COLLECTION_PROPERTIES).replace("{collection}",filter.getTitle()))
					.findFirst()
					.orElse(polyglotService.getTranslatedData(COLLECTION_LISTING_COMPARE_CARD_TITLE)); // Return a default value if not found
		}
		return 	polyglotService.getTranslatedData(COLLECTION_LISTING_COMPARE_CARD_TITLE);
	}

	public PlacesResponse modifyPlacesResponse(PlacesResponse placesResponse) {
		if (placesResponse != null && CollectionUtils.isNotEmpty(placesResponse.getCategories())) {
			for (Category category: placesResponse.getCategories()) {
				if (category != null && CollectionUtils.isNotEmpty(category.getCategoryData())) {
					for (CategoryDatum categoryDatum: category.getCategoryData()) {
						if (StringUtils.isNotEmpty(categoryDatum.getCategory())) {
							categoryDatum.setTagLine(categoryDatum.getCategory());
						}
					}
				}
			}
		}
		return placesResponse;
	}

	public CountryWiseReviewData buildCountryWiseData(UGCPlatformReviewSummaryDTO summary) {
		if(summary != null && summary.getCountryWiseReviewCount() != null && summary.getCountryWiseReviewCount().getCountry() != null){
			int reviewCount = summary.getCountryWiseReviewCount().getReviewCount();
			String nationality = NATIONALITY_MAP.get(summary.getCountryWiseReviewCount().getCountry());

			if(reviewCount > MIN_COUNTRY_WISE_REVIEW_COUNT && StringUtils.isNotEmpty(nationality)){
				String reviewText = polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_TEXT);
				String reviewCtaText = polyglotService.getTranslatedData(COUNTRY_WISE_REVIEW_CTA_TEXT);

				if(StringUtils.isNotEmpty(reviewText)){
					reviewText = reviewText.replace(REVIEW_COUNT, Integer.toString(reviewCount)).replace(NATIONALITY, nationality);
				}
				else {
					return null;
				}
				if(StringUtils.isNotEmpty(reviewCtaText)){
					reviewCtaText = reviewCtaText.replace(REVIEW_COUNT, Integer.toString(reviewCount)).replace(NATIONALITY, nationality);
				}
				CountryWiseReviewData countryWiseReviewData = new CountryWiseReviewData();
				countryWiseReviewData.setText(reviewText);
				countryWiseReviewData.setColor(COUNTRY_WISE_REVIEW_TEXT_COLOR);
				countryWiseReviewData.setCtaText((reviewCtaText));
				return countryWiseReviewData;
			}
		}
		return null;
	}

	private UGCSummary getUgcSummary(CBPlatformSummaryResponse data, String countryCode, boolean combineOTASupported) {
		UGCSummary ugcSummary = null;
		if (data != null && data.getSummary() != null && !data.getSummary().isEmpty()) {
			ugcSummary = new UGCSummary();
			UGCPlatformReviewSummaryDTO summary = objectMapperUtil.getObjectFromJsonNode(data.getSummary().get("data"), new TypeReference<UGCPlatformReviewSummaryDTO>() {
			});
			if(summary != null){
				if (!Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
					summary.setRatingBreakup(null);
				}
				if (!combineOTASupported && summary.getSource() != null &&
						(OTA.BKG.name().equalsIgnoreCase(summary.getSource().name()) || OTA.MMT_BKG.name().equalsIgnoreCase(summary.getSource().name()) || OTA.MMT_EXP.name().equalsIgnoreCase(summary.getSource().name()))) {
					summary.setSource(OTA.MMT);
				}
				if(summary.getSelectedCategory() != null && summary.getSelectedCategory().equals(ReviewCategoryConstants.BUSINESS.name())){
					summary.setSelectedCategoryIcon(businessRatingIconUrl);
				}
			}
			CountryWiseReviewData countryWiseReviewData = buildCountryWiseData(summary);
			if(countryWiseReviewData != null) {
				summary.setCountryWiseReviewData(countryWiseReviewData);
			}
			ugcSummary.setData(summary);
			ugcSummary.setCardTitle(REVIEW_RATING_TITLE);
		}
		return ugcSummary;
	}

	public List<Hotel> liteHotelLists(List<Hotel> hotelList, boolean isSponsoredHotels, boolean isChainInfoRequired){
		List<Hotel> finalHotelList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(hotelList)) {
			for (Hotel hotel : hotelList) {
				Hotel hotelLite = new Hotel();
				hotelLite.setId(hotel.getId());
				hotelLite.setName(hotel.getName());
				hotelLite.setStarRating(hotel.getStarRating());
				hotelLite.setStarRatingType(hotel.getStarRatingType());
				hotelLite.setHighSellingAltAcco(hotel.getHighSellingAltAcco());
				hotelLite.setSoldOut(hotel.isSoldOut());
				hotelLite.setIsAltAcco(hotel.getIsAltAcco());
				hotelLite.setPriceDetail(hotel.getPriceDetail());
				hotelLite.setLocationDetail(hotel.getLocationDetail());
				hotelLite.setDetailDeeplinkUrl(hotel.getDetailDeeplinkUrl());
				hotelLite.setLocationPersuasion(hotel.getLocationPersuasion());
				hotelLite.setRatePersuasionText(hotel.getRatePersuasionText());
				hotelLite.setHotelPersuasions(hotel.getHotelPersuasions());
				if (CollectionUtils.isNotEmpty(hotel.getMedia())) {
					for (MediaInfo liteMediaInfo : hotel.getMedia()) {
						if ("IMAGE".equalsIgnoreCase(liteMediaInfo.getMediaType())) {
							hotelLite.setMedia(Collections.singletonList(liteMediaInfo));
							break;
						}
					}
				}
				if (hotel.getReviewSummary() != null) {
					ReviewSummary liteReviewSummary = new ReviewSummary();
					liteReviewSummary.setSource(hotel.getReviewSummary().getSource());
					liteReviewSummary.setCumulativeRating(hotel.getReviewSummary().getCumulativeRating());
					liteReviewSummary.setTotalRatingCount(hotel.getReviewSummary().getTotalRatingCount());
					liteReviewSummary.setRatingText(hotel.getReviewSummary().getRatingText());
					liteReviewSummary.setPreferredOTA(hotel.getReviewSummary().isPreferredOTA());
					hotelLite.setReviewSummary(liteReviewSummary);
				}
				if (isSponsoredHotels || isChainInfoRequired) {
					List<String> requiredPlaceHolders = Arrays.asList("PLACEHOLDER_CARD_M4", "PLACEHOLDER_IMAGE_LEFT_TOP");
					if(isChainInfoRequired) {
						requiredPlaceHolders = Arrays.asList("PLACEHOLDER_CARD_M2");
					}
					Map<String, Object> hotelPersuasions = (Map<String, Object>) hotel.getHotelPersuasions();
					Map<String, Object> liteHotelPersuasions = new LinkedHashMap<>();
					for (String key : requiredPlaceHolders) {
						if (hotelPersuasions.containsKey(key)) {
							liteHotelPersuasions.put(key, hotelPersuasions.get(key));
						}
					}
					if(MapUtils.isNotEmpty(liteHotelPersuasions)){
						hotelLite.setHotelPersuasions(liteHotelPersuasions);
					}
				}
				finalHotelList.add(hotelLite);
			}
		}
		return finalHotelList;
	}


	private  ComparatorResponse buildComparatorResponse(UpsellHotelDetailResponse hotelCompareResponse, boolean isComparatorV2Required, String client, Map<String, String> expDataMap, boolean isLiteResponse){
		ComparatorResponse comparatorResponse = new ComparatorResponse();
		comparatorResponse.setComparisonHeadings(hotelCompareResponse.getComparisonHeadings());
		comparatorResponse.setCtaMap(hotelCompareResponse.getCtaMap());
		comparatorResponse.setDeeplink(hotelCompareResponse.getDeeplink());
		comparatorResponse.setHotelDisplayMap(hotelCompareResponse.getHotelDisplayMap());
		comparatorResponse.setResponseErrors(hotelCompareResponse.getResponseErrors());
		comparatorResponse.setTitle(hotelCompareResponse.getTitle());
		comparatorResponse.setRepositionIndex(hotelCompareResponse.getRepositionIndex());
		comparatorResponse.setShowCTA(hotelCompareResponse.isShowCTA());
		comparatorResponse.setBannerInfo(hotelCompareResponse.getBannerInfo());
		if (isComparatorV2Required && null != hotelCompareResponse.getHotelSearchResponse()) {
			if (CollectionUtils.isNotEmpty(hotelCompareResponse.getHotelSearchResponse().getHotelList())) {
				comparatorResponse.setHotelList(searchHotelsFactory.getResponseService(client).buildPersonalizedHotels(hotelCompareResponse.getHotelSearchResponse().getHotelList(),
						expDataMap, null, null, null, null, null, null));
				if(isLiteResponse && CollectionUtils.isNotEmpty(comparatorResponse.getHotelList())){
					List<Hotel> finalHotelList = liteHotelLists(comparatorResponse.getHotelList(),false, utility.isExperimentOn(expDataMap, "CRI"));
					if(CollectionUtils.isNotEmpty(finalHotelList)){
						comparatorResponse.setHotelList(finalHotelList);
					}
				}
			}
			if (CollectionUtils.isNotEmpty(hotelCompareResponse.getHotelSearchResponse().getSponsoredHotelList())) {
				comparatorResponse.setSponsoredIconUrl(sponsoredHotelIconUrl);
				comparatorResponse.setSponsoredHotelList(searchHotelsFactory.getResponseService(client).buildPersonalizedHotels(hotelCompareResponse.getHotelSearchResponse().getSponsoredHotelList(),
						expDataMap, null, null, null, null, null, null));
				if(isLiteResponse && CollectionUtils.isNotEmpty(comparatorResponse.getSponsoredHotelList())){
					List<Hotel> finalSponsoredHotelList = liteHotelLists(comparatorResponse.getSponsoredHotelList(),true, utility.isExperimentOn(expDataMap, "CRI"));
					if(CollectionUtils.isNotEmpty(finalSponsoredHotelList)){
						comparatorResponse.setSponsoredHotelList(finalSponsoredHotelList);
					}
				}
			}
		} else {
			if (MapUtils.isNotEmpty(expDataMap) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.UNIFIED_USER_RATING))) {
				transformUserRatingFromReviewSummary(hotelCompareResponse.getHotelSearchResponse());
			}
			comparatorResponse.setHotelSearchResponse(hotelCompareResponse.getHotelSearchResponse());
		}
		return comparatorResponse;
	}

	@Deprecated
	private void transformUserRatingFromReviewSummary(SearchWrapperResponseBO<SearchWrapperHotelEntity> hotelSearchResponse) {
		if (hotelSearchResponse != null && CollectionUtils.isNotEmpty(hotelSearchResponse.getHotelList())) {
			for (SearchWrapperHotelEntity hotelEntity : hotelSearchResponse.getHotelList()) {
				if (hotelEntity.getReviewSummary() != null) {
					Map<OTA, JsonNode> flyFishReviewSummary = new HashMap<>();
					Map<String, Object> summary = new HashMap<>();
					summary.put("cumulativeRating", hotelEntity.getReviewSummary().getHotelRating());
					summary.put("ratingText", hotelEntity.getReviewSummary().getRatingText());
					summary.put("preferredOTA", true);
					try {
						if (StringUtils.isNotBlank(hotelEntity.getReviewSummary().getOta())) {
							ObjectMapper objectMapper = new ObjectMapper();
							flyFishReviewSummary.put(OTA.valueOf(hotelEntity.getReviewSummary().getOta().toUpperCase()), objectMapper.convertValue(summary, JsonNode.class));
							hotelEntity.setFlyfishReviewSummary(flyFishReviewSummary);
						}
					} catch (Exception ex) {
						logger.error("Exception while parsing review summary node for comparator hotels - hotel Id : {} exception message: {} ", hotelEntity.getId(), ex.getMessage());
					}
				}
			}
		}
	}

	//Making space Name to Image Details Map to convert the format to Hotstore one
	private Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap(List<TravellerImageEntity> travelerList){
		Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap = new HashMap<>();

		if (CollectionUtils.isNotEmpty(travelerList)) {
			for (TravellerImageEntity travellerImageEntity : travelerList) {
				if (spaceNameToImageEntityMap.containsKey(travellerImageEntity.getImageFilterInfo())) {
					spaceNameToImageEntityMap.get(travellerImageEntity.getImageFilterInfo()).add(travellerImageEntity);
				} else {
					spaceNameToImageEntityMap.put(travellerImageEntity.getImageFilterInfo(), new ArrayList<TravellerImageEntity>() {{
						add(travellerImageEntity);
					}});
				}
			}
		}
		return spaceNameToImageEntityMap;
	}

	// Converting the Image Details Map to Client format of Traveller Images.
	private List<Tag> createTagsFromTravellerImages(Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap, boolean isChatBotEnable){
		List<Tag> tags = new ArrayList<>();
		for (Map.Entry<String, List<TravellerImageEntity>> entry : spaceNameToImageEntityMap.entrySet()){
			List<TravellerImageEntity> travelerList = entry.getValue();
			Tag tag = new Tag();
			tag.setName(entry.getKey());
			if (isChatBotEnable) {
				tag.setChatBotHook(utility.buildChatbotHook(tag.getName()));
			}
			Subtag subtag = new Subtag();
			subtag.setName(entry.getKey());
			List<ImageData> data = new ArrayList<>();
			for (TravellerImageEntity travellerImageEntity : travelerList) {
				ImageData imageData = new ImageData();
				imageData.setDate(travellerImageEntity.getDate());
				imageData.setTravelerName(travellerImageEntity.getTravellerName());
				imageData.setMediaType("IMAGE");
				imageData.setUrl(travellerImageEntity.getUrl());
				data.add(imageData);
			}
			subtag.setData(data);
			tag.setSubtags(new ArrayList<Subtag>(){{add(subtag);}});
			tags.add(tag);
		}
		return tags;
	}

	public int getMediaV2HotelMediaListCount(List<Tag> tags){
		int hotelImageLength = 0;
		if(CollectionUtils.isNotEmpty(tags)) {
			for(Tag tag : tags){
				if(CollectionUtils.isNotEmpty(tag.getSubtags())){
					for(Subtag subtag : tag.getSubtags()){
						if(subtag.getData() != null) {
							for(ImageData item : subtag.getData()){
								if(StringUtils.isNotEmpty(item.getMediaType()) && !item.getMediaType().equalsIgnoreCase("VIDEO")){
									hotelImageLength++;
								}
							}
						}
					}
				}
			}
		}
		return hotelImageLength;
	}

	public List<LiteResponseTravellerImage> getMediaV2TravellerMediaList(List<Tag> tags){
		List<LiteResponseTravellerImage> travellerImageList = new ArrayList<>();
		if(CollectionUtils.isNotEmpty(tags)) {
			for(Tag tag : tags){
				if(CollectionUtils.isNotEmpty(tag.getSubtags())){
					for(Subtag subtag : tag.getSubtags()){
						if(subtag.getData() != null) {
							for(ImageData item : subtag.getData()){
								if(StringUtils.isNotEmpty(item.getMediaType()) && "IMAGE".equalsIgnoreCase(item.getMediaType())){
									LiteResponseTravellerImage liteResponseTravellerImage = new LiteResponseTravellerImage();
									liteResponseTravellerImage.setUrl(item.getUrl());
									liteResponseTravellerImage.setMediaType(item.getMediaType());
									liteResponseTravellerImage.setTitle(item.getTitle());
									liteResponseTravellerImage.setFilterInfo(subtag.getName());
									liteResponseTravellerImage.setSuperTag(tag.getName());
									liteResponseTravellerImage.setAccess(subtag.getAccess());
									liteResponseTravellerImage.setAccessType(subtag.getAccessType());
									liteResponseTravellerImage.setThumbnailURL(item.getThumbnailURL());
									liteResponseTravellerImage.setDate(item.getDate());
									liteResponseTravellerImage.setTravelerName(item.getTravelerName());
									liteResponseTravellerImage.setDescription(item.getDescription());
									liteResponseTravellerImage.setText(subtag.getText());
									travellerImageList.add(liteResponseTravellerImage);
								}
							}
						}
					}
				}
			}
		}
		return travellerImageList;
	}


	private MediaV2 buildMediaV2(HotelImage hotelImage, String listingType, LinkedHashSet<String> imageTagsOrder, boolean isLuxe, boolean isImageExpEnable, String client, boolean isChatBotEnable) {
		MediaV2 mediaV2 = null;

		if (hotelImage != null && hotelImage.getImageDetails() != null ) {

			/* Traveller images building START */
			if (MapUtils.isNotEmpty(hotelImage.getImageDetails().getTraveller())) {

				Map<String, List<TravellerImageEntity>> travelerImageMapV2 = hotelImage.getImageDetails().getTraveller();
				List<TravellerImageEntity> travelerList = travelerImageMapV2.get("H");

				// Getting the space Name to Image Entity Map
				Map<String, List<TravellerImageEntity>> spaceNameToImageEntityMap = spaceNameToImageEntityMap(travelerList);

				// Converting the Image Details Map to Client format
				List<Tag> tags = createTagsFromTravellerImages(spaceNameToImageEntityMap, isChatBotEnable);

				//sorting traveller tags based upon ImageTags order from hotstore
				if(CollectionUtils.isNotEmpty(imageTagsOrder) && CollectionUtils.isNotEmpty(tags)){
					sortTravellerTagsBasedImageTagsOrder(tags, new ArrayList<>(imageTagsOrder));
				}

				TravellerImages travellerImages = new TravellerImages();
				travellerImages.setTags(tags);
				mediaV2 = new MediaV2();
				mediaV2.setTraveller(travellerImages);

			}
			/* Traveller images building END */

			/* Professional images V2 building START */
			if(MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessionalV2())){
				Map<String, List<Tag>> professionalImageMapV2 = hotelImage.getImageDetails().getProfessionalV2();
				if(professionalImageMapV2.containsKey("H")){
					HotelImages hotelImages = new HotelImages();
					hotelImages.setTags(professionalImageMapV2.get("H"));
					if(mediaV2 == null){
						mediaV2 = new MediaV2();
					}
					mediaV2.setHotel(hotelImages);
				}
			}
			/* Professional images V2 building END */
			if (mediaV2 != null && mediaV2.getHotel() != null && CollectionUtils.isNotEmpty(mediaV2.getHotel().getTags())) {
				for (Tag tag : mediaV2.getHotel().getTags()) {
					// Set the ChatBotHook for each tag
					if (isChatBotEnable) {
						tag.setChatBotHook(utility.buildChatbotHook(tag.getName()));
					}

					// Check if the tag is the Street View tag and set the icon URL
					if (Constants.Street_View.equalsIgnoreCase(tag.getName())) {
						tag.setTagIconUrl(view360PersuasionIconUrl);
					}
				}
			}

			/* 360 images building START */
			if(MapUtils.isNotEmpty(hotelImage.getImageDetails().getImages360()) && hotelImage.getImageDetails().getImages360().containsKey("H")){
				List<Image360> images = hotelImage.getImageDetails().getImages360().get("H");
				if (CollectionUtils.isNotEmpty(images)) {
					View360Image view360Image = new View360Image();
					view360Image.setImages(images);
					view360Image.setCtaIcon(view360IconUrl);
					view360Image.setPersuasionIcon(view360PersuasionIconUrl);
					view360Image.setCtaText(polyglotService != null ? (isLuxe && !CLIENT_DESKTOP.equalsIgnoreCase(client)? polyglotService.getTranslatedData(ConstantsTranslation.VIEW_360_IMAGE) : polyglotService.getTranslatedData(ConstantsTranslation.VIEW_IMAGE)) : null);
					mediaV2.setView360(view360Image);
				}
			}
			/* 360 images building END */

			/* Grid images building START */
			int mediaLimit = isImageExpEnable && Utility.isAppRequest() ? listingMediaLimitExp : detailGridImageLimit;
			if(MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())) {
				List<MediaInfo> professionalMediaInfos = buildProfessionalImagesFromContentResponse(hotelImage, listingType);

				if (CollectionUtils.isNotEmpty(professionalMediaInfos)) {
					int numOfImages = min(mediaLimit, professionalMediaInfos.size());
					ProfessionalImages grid = new ProfessionalImages();
					grid.setImages(professionalMediaInfos.subList(0, numOfImages));
					if (mediaV2 == null) {
						mediaV2 = new MediaV2();
					}
					mediaV2.setGrid(grid);
				}
			}
			/* Grid images building END */

		}

		return mediaV2;
	}

	private Media buildMedia(HotelImage hotelImage, com.mmt.hotels.model.response.staticdata.HotelResult hotelResult, String listingType) {

		List<MediaInfo> professionalMediaInfos = new ArrayList<>();
		List<MediaInfo> travelerMediaInfos = new ArrayList<>();
		LinkedHashSet<String> imageTagsOrder = new LinkedHashSet<>();
		professionalMediaInfos = buildProfessionalImagesFromContentResponse(hotelImage, listingType);

		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getTraveller())) {
			Map<String, List<TravellerImageEntity>> traveler = hotelImage.getImageDetails().getTraveller();
			List<TravellerImageEntity> travelerList = traveler.get("H");
			if (CollectionUtils.isNotEmpty(travelerList)) {
				for (TravellerImageEntity travellerImageEntity : travelerList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setDate(travellerImageEntity.getDate());
					mediaInfo.setFilterInfo(travellerImageEntity.getImageFilterInfo());
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTitle(travellerImageEntity.getTitle());
					mediaInfo.setTravelerName(travellerImageEntity.getTravellerName());
					mediaInfo.setUrl(travellerImageEntity.getUrl());
					travelerMediaInfos.add(mediaInfo);
				}
			}
		}

		if (hotelResult != null) {
			List<VideoInfo> videos = hotelResult.getHotelVideos();
			if (CollectionUtils.isNotEmpty(videos)) {
				List<MediaInfo> videoList = new ArrayList<>();
				for (VideoInfo video : videos) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setUrl(video.getUrl());
					mediaInfo.setThumbnailURL(video.getThumbnailUrl());
					mediaInfo.setTags(video.getTags());
					mediaInfo.setTitle(video.getTitle());
					mediaInfo.setMediaType("VIDEO");
					mediaInfo.setText(video.getText());
					mediaInfo.setFilterInfo(video.getVideoFilterInfo());
					videoList.add(mediaInfo);
				}
				if (CollectionUtils.isNotEmpty(videoList))
					professionalMediaInfos.addAll(0,videoList);
			}
		}

		if(Objects.nonNull(hotelResult) && !CollectionUtils.isEmpty(hotelResult.getProfessionalImageTagsOrder())) {
			imageTagsOrder = hotelResult.getProfessionalImageTagsOrder();
		}

		Media media = null;
		if (CollectionUtils.isNotEmpty(professionalMediaInfos)
				|| CollectionUtils.isNotEmpty(travelerMediaInfos)) {
			media = new Media();
			media.setProfessional(professionalMediaInfos);
			media.setTraveller(travelerMediaInfos);
			media.setImageTagsOrder(imageTagsOrder);
		}
		return media;
	}

	private List<MediaInfo> buildProfessionalImagesFromContentResponse(HotelImage hotelImage, String listingType){

		List<MediaInfo> professionalMediaInfos = new ArrayList<>();

		if (hotelImage != null && hotelImage.getImageDetails() != null && MapUtils.isNotEmpty(hotelImage.getImageDetails().getProfessional())) {
			Map<String, List<ProfessionalImageEntity>> professional = hotelImage.getImageDetails().getProfessional();
			List<ProfessionalImageEntity> professionalList = professional.get("H");
			if (CollectionUtils.isNotEmpty(professionalList)) {
				for (ProfessionalImageEntity professionalImageEntity : professionalList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setFilterInfo(professionalImageEntity.getImageFilterInfo());
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTags(professionalImageEntity.getSeekTags());
					mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailURL());
					mediaInfo.setTitle(professionalImageEntity.getTitle());
					mediaInfo.setUrl(professionalImageEntity.getUrl());
					professionalMediaInfos.add(mediaInfo);
				}
			}
			professionalList = professional.get("R");
			//HTL-41751 Not Merging RoomImages in GalleryImages when listingType is Entire Property
			//as same set of images were coming twice, once in gallery images and second time in room images.
			if (CollectionUtils.isNotEmpty(professionalList) && !LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)) {
				for (ProfessionalImageEntity professionalImageEntity : professionalList) {
					MediaInfo mediaInfo = new MediaInfo();
					mediaInfo.setMediaType("IMAGE");
					mediaInfo.setTags(professionalImageEntity.getSeekTags());
					String roomTag = polyglotService.getTranslatedData(STATIC_ROOM_TAG);
					if (mediaInfo.getTags() != null) {
						mediaInfo.getTags().add(roomTag);
					} else {
						List<String> tags = new ArrayList<String>();
						tags.add(roomTag);
						mediaInfo.setTags(tags);
					}
					mediaInfo.setFilterInfo(roomTag);
					mediaInfo.setThumbnailURL(professionalImageEntity.getThumbnailURL());
					mediaInfo.setTitle(professionalImageEntity.getTitle());
					mediaInfo.setUrl(professionalImageEntity.getUrl());
					mediaInfo.setRoomCode(professionalImageEntity.getCatCode());
					professionalMediaInfos.add(mediaInfo);
				}
			}
		}

		return professionalMediaInfos;
	}

	private Map<String,ReviewSummary> getReviewSummary(CBFlyFishSummaryResponse flyfishSummaryResponse, String client, String countryCode) {
		if (flyfishSummaryResponse == null || MapUtils.isEmpty(flyfishSummaryResponse.getSummary()))
			return null;
		Map<String,ReviewSummary> map = new HashMap<>();
		for (OTA ota: flyfishSummaryResponse.getSummary().keySet()) {

			ReviewSummary reviewSummary = new ReviewSummary();
			JsonNode ratingSummary = flyfishSummaryResponse.getSummary().get(ota);
			reviewSummary.setSource(ota.name());
			if (ratingSummary.get("cumulativeRating") != null) {
				reviewSummary.setCumulativeRating(ratingSummary.get("cumulativeRating").floatValue());
			}
//			this will set recent 10 ratings for property into review section on detail page.
			if (ratingSummary.get("recentRatings") != null) {
				reviewSummary.setRecentRatings(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("recentRatings"), new TypeReference<List<Object>>() {
				}));
			}
			if (ratingSummary.get("manualPersuasion") != null) {
				reviewSummary.setManualPersuasion(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("manualPersuasion"), new TypeReference<ManualPersuasion>() {}));
			}
			if (ratingSummary.get("totalRatingCount") != null) {
				reviewSummary.setTotalRatingCount(ratingSummary.get("totalRatingCount").intValue());
			}
			if (ratingSummary.get("totalReviewsCount") != null) {
				reviewSummary.setTotalReviewCount(ratingSummary.get("totalReviewsCount").intValue());
			}
			if (ratingSummary.get("disableLowRating") != null) {
				reviewSummary.setDisableLowRating(ratingSummary.get("disableLowRating").booleanValue());
			}
			if (ratingSummary.get("showUpvoteTag") != null) {
				reviewSummary.setShowUpvote(ratingSummary.get("showUpvoteTag").booleanValue());
			}
			if (ratingSummary.get("ratingText") != null) {
				reviewSummary.setRatingText(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("ratingText"), new TypeReference<String>() {
				}));
			}
			if (ratingSummary.get("crawledData") != null) {
				reviewSummary.setCrawledData(ratingSummary.get("crawledData").booleanValue());
			}
			if (ratingSummary.get("bestReviewTitle") != null) {
				reviewSummary.setBestReviewTitle((objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("bestReviewTitle"), new TypeReference<String>() {})));
			}
			if (ratingSummary.get("travelTypeList") != null) {
				reviewSummary.setTravelTypeList(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("travelTypeList"), new TypeReference<List<TravelType>>() {
				}));
			}
			if (ratingSummary.get("sortingCriterionList") != null) {
				reviewSummary.setSortingCriterionList(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("sortingCriterionList"), new TypeReference<List<CriteriaType>>() {
				}));
			}
			if (ratingSummary.get("preferredOTA") != null) {
				reviewSummary.setPreferredOTA(ratingSummary.get("preferredOTA").booleanValue());
			}
			if (ratingSummary.get("cgptNewIcon") != null) {
				reviewSummary.setCgptNewIcon(ratingSummary.get("cgptNewIcon").asText());
			}
			reviewSummary.setTravelTypes(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("travelTypes"), new TypeReference<List<String>>() {}));
			reviewSummary.setSortingCriterion(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("sortingCriterion"), new TypeReference<List<String>>() {}));
			reviewSummary.setImageTypes(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("imageTypes"), new TypeReference<List<String>>() {}));
			if (Constants.DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
				reviewSummary.setRatingBreakup(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("ratingBreakup"), new TypeReference<Map<String, Integer>>() {}));
			}
			reviewSummary.setReviewBreakup(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("reviewBreakup"), new TypeReference<Map<String, Integer>>() {}));
			reviewSummary.setBest(getBestReview(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("best"), new TypeReference<List<ReviewDescriptionDTO>>() {})));
			reviewSummary.setAltAccoPer(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("altAccoPer"), new TypeReference<List<AltAccoPer>>() {}));
			if(ratingSummary.get("selectedCategory") != null) {
				reviewSummary.setSelectedCategory(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("selectedCategory"), new TypeReference<String>() {}));
			}
			if(reviewSummary.getSelectedCategory() != null && reviewSummary.getSelectedCategory().equals(ReviewCategoryConstants.BUSINESS.name())){
				reviewSummary.setSelectedCategoryIcon(businessRatingIconUrl);
			}
			if (ratingSummary.get("mmtReviewCount") != null) {
				reviewSummary.setMmtReviewCount(ratingSummary.get("mmtReviewCount").asInt());
			}
			if (ratingSummary.get("recentReview") != null) {
				reviewSummary.setRecentReview(ratingSummary.get("recentReview").asBoolean());
			}
			if (ratingSummary.get("additionalInfo") != null) {
				reviewSummary.setAdditionalInfo(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("additionalInfo"), new TypeReference<FlyfishReviewAdditionalInfo>() {}));
			}
			if (ratingSummary.get("highRatedTopic") != null) {
				reviewSummary.setHighRatedTopic(objectMapperUtil.getObjectFromJsonNode(ratingSummary.get("highRatedTopic"), new TypeReference<List<String>>() {
				}));
			}
			if (ratingSummary.get("ratedText") != null) {
				reviewSummary.setRatedText(ratingSummary.get("ratedText").asText());
			}
			if (ratingSummary.get("ratedIcon") != null) {
				reviewSummary.setRatedIcon(ratingSummary.get("ratedIcon").asText());
			}
			JsonNode travellerRatingSummary = ratingSummary.get("travellerRatingSummary");
			if (travellerRatingSummary != null) {
				reviewSummary.setHotelRatingSummary(getHotelRatingSummary(objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary, new TypeReference<TravellerRatingSummaryDTO>() {
				})));
				SeekTagDetails seekTagDetails = new SeekTagDetails();
				if (travellerRatingSummary.get("seekTagsTitle") != null) {
					seekTagDetails.setSeekTagsTitle(travellerRatingSummary.get("seekTagsTitle").asText());
				}
				if (travellerRatingSummary.get("seekTagsSubtitle") != null) {
					seekTagDetails.setSeekTagsSubtitle(travellerRatingSummary.get("seekTagsSubtitle").asText());
				}
				if (travellerRatingSummary.get("seekTagIcon") != null && StringUtils.isNotEmpty(travellerRatingSummary.get("seekTagIcon").asText())) {
					seekTagDetails.setSeekTagIcon(travellerRatingSummary.get("seekTagIcon").asText());
				}
				if (travellerRatingSummary.get("seekTagSummary") != null && StringUtils.isNotEmpty(travellerRatingSummary.get("seekTagSummary").asText())) {
					seekTagDetails.setSeekTagSummary(travellerRatingSummary.get("seekTagSummary").asText());
				}
				if (travellerRatingSummary.get("seekTagSpans") != null) {
					seekTagDetails.setSeekTagSpans(objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary.get("seekTagSpans"), new TypeReference<List<List<Integer>>>() {
					}));
				}
				if (travellerRatingSummary.get("topicSummary") != null) {
					seekTagDetails.setTopicSummaryList(objectMapperUtil.getObjectFromJsonNode(travellerRatingSummary.get("topicSummary"), new TypeReference<List<TopicSummary>>() {
					}));

				}

				reviewSummary.setSeekTagDetails(seekTagDetails);

				if (travellerRatingSummary.get("chatGPTSummaryExists") != null) {
					reviewSummary.setChatGPTSummaryExists(travellerRatingSummary.get("chatGPTSummaryExists").asBoolean());
				}
			}
			if(ota.name().equals("MMT")) {
				updateIconUrl(flyfishSummaryResponse.getContextualizedReviews(), client);
				reviewSummary.setContextualizedReviews(flyfishSummaryResponse.getContextualizedReviews());
			}
			if(ota == OTA.EXT){
				reviewSummary.setDisclaimer(ratingSummary.get("disclaimer"));
				reviewSummary.setReviewHighlights(ratingSummary.get("reviewHighlights"));
				reviewSummary.setReviewHighlightTitle(ratingSummary.get("reviewHighlightTitle"));
			}
			map.put(ota.name(), reviewSummary);
		}
		return map;
	}

//	private List<ConceptSummary> getAltAccoRatingSummary(TravellerRatingSummaryDTO travellerRatingSummaryDTO) {
//		if (travellerRatingSummaryDTO == null || CollectionUtils.isEmpty(travellerRatingSummaryDTO.getAltAccoSummary())){
//			return null;
//		}
//		List<ConceptSummary> altAccoRatingSummary = new ArrayList<>();
//
//		for(ConceptSummaryDTO conceptSummaryDTO : travellerRatingSummaryDTO.getAltAccoSummary()) {
//			ConceptSummary conceptSummary = new ConceptSummary();
//			conceptSummary.setConcept(conceptSummaryDTO.getConcept());
//			conceptSummary.setDisplayText(conceptSummaryDTO.getDisplayText());
//			conceptSummary.setValue(conceptSummaryDTO.getValue());
//			conceptSummary.setReviewCount(conceptSummaryDTO.getReviewCount());
//			altAccoRatingSummary.add(conceptSummary);
//		}
//		return altAccoRatingSummary;
//	}

	private void updateIconUrl(ContextualizeReviews contextualizedReviews, String client){
		if(contextualizedReviews == null){
			return;
		}
		String clientAppend = UNDERSCORE + client.toLowerCase() + DOT;
		if(CollectionUtils.isNotEmpty(contextualizedReviews.getHighlighted())) {
			for (ContextualReviewData highlighted : contextualizedReviews.highlighted) {
				if (StringUtils.isNotBlank(highlighted.iconUrl)) {
					highlighted.iconUrl = highlighted.iconUrl.replaceAll("\\.(?!.*\\.)", clientAppend);
				}
			}
		}
		if(CollectionUtils.isNotEmpty(contextualizedReviews.getCommon())) {
			for (ContextualReviewData common : contextualizedReviews.common) {
				if (StringUtils.isNotBlank(common.iconUrl)) {
					common.iconUrl = common.iconUrl.replaceAll("\\.(?!.*\\.)", clientAppend);
				}
			}
		}
	}


	private List<ConceptSummary> getHotelRatingSummary(TravellerRatingSummaryDTO travellerRatingSummaryDTO) {
		if (travellerRatingSummaryDTO == null || CollectionUtils.isEmpty(travellerRatingSummaryDTO.getHotelSummary())){
			return null;
		}

		List<ConceptSummary> hotelratingSummary = new ArrayList<>();

		for (ConceptSummaryDTO conceptSummaryDTO : travellerRatingSummaryDTO.getHotelSummary()){
			ConceptSummary conceptSummary = new ConceptSummary();
			conceptSummary.setConcept(conceptSummaryDTO.getConcept());
			conceptSummary.setDisplayText(conceptSummaryDTO.getDisplayText());
			conceptSummary.setHeroTag(conceptSummaryDTO.isHeroTag());
			conceptSummary.setReviewCount(conceptSummaryDTO.getReviewCount());
			conceptSummary.setShow(conceptSummaryDTO.getShow());
			conceptSummary.setSubConcepts(buildSubConcepts(conceptSummaryDTO.getSubConcepts()));
			conceptSummary.setValue(conceptSummaryDTO.getValue());

			hotelratingSummary.add(conceptSummary );
		}

		return hotelratingSummary;
	}

	private List<SubConcept> buildSubConcepts(List<SubConceptDTO> subConceptDTOList) {
		List<SubConcept> subConcepts = null;
		if (CollectionUtils.isNotEmpty(subConceptDTOList)){
			subConcepts = new ArrayList<>();
			for (SubConceptDTO subConceptDTO : subConceptDTOList){
				SubConcept subConcept = new SubConcept();
				subConcept.setPriorityScore(subConceptDTO.getPriorityScore());
				subConcept.setRelatedReviewCount(subConceptDTO.getRelatedReviewCount());
				subConcept.setSentiment(subConceptDTO.getSentiment());
				subConcept.setSubConcept(subConceptDTO.getSubConcept());
				subConcept.setDisplayText(subConceptDTO.getDisplayText());
				subConcept.setTagType(subConceptDTO.getTagType());
				subConcept.setSource(subConceptDTO.getSource());

				subConcepts.add(subConcept );
			}
		}
		return subConcepts;
	}

	private List<ReviewObject> getBestReview(List<ReviewDescriptionDTO> best) {
		if (CollectionUtils.isEmpty(best))
			return null;
		List<ReviewObject> bestReviews = new ArrayList<>();
		for (ReviewDescriptionDTO reviewDTO: best) {
			ReviewObject reviewObject = new ReviewObject();
			reviewObject.setCheckinDate(reviewDTO.getCheckinDate());
			reviewObject.setCheckoutDate(reviewDTO.getCheckoutDate());
			reviewObject.setId(reviewDTO.getId());
			reviewObject.setPublishDate(reviewDTO.getPublishDate());
			reviewObject.setRating(reviewDTO.getRating());
			reviewObject.setReviewText(reviewDTO.getReviewText());
			reviewObject.setTitle(reviewDTO.getTitle());
			reviewObject.setTravellerName(reviewDTO.getTravellerName());
			reviewObject.setTravelType(reviewDTO.getTravelType());
			reviewObject.setUpvoted(reviewDTO.isUpvoted());
			bestReviews.add(reviewObject);
		}
		return bestReviews;
	}

	private HotelResult getHotelResult(StaticDetailRequest staticDetailRequestBody,com.mmt.hotels.model.response.staticdata.HotelResult hotelResultCB,
									   String context, Map<String, String> expDataMap, StaticDetailCriteria staticDetailRequest,
									   DeviceDetails deviceDetails, boolean isCorp, boolean isGroupBookingFunnel, String funnel,
									   boolean myPartnerReq) {
		if (null == hotelResultCB) {
			return null;
		}

		boolean isLiteResponse = staticDetailRequestBody != null && staticDetailRequestBody.getFeatureFlags() != null && staticDetailRequestBody.getFeatureFlags().isLiteResponse();

		boolean isNewDetailPageTrue = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentTrue(expDataMap, NEW_DETAIL_PAGE);

		boolean dhCall = true;
		if (staticDetailRequest != null && staticDetailRequest.getCountryCode() != null) {
			dhCall = Constants.DOM_COUNTRY.equalsIgnoreCase(staticDetailRequest.getCountryCode());
		}

		boolean isDisableHostChat = false;
		if (Utility.isGCC()) {
			isDisableHostChat = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentTrue(expDataMap, DISABLE_HOST_CHAT);
		}
		boolean isNewDetailPageDesktop = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, NEW_DETAIL_PAGE_DESKTOP_EXP);
		boolean isCallToBookV2Applicable = MapUtils.isNotEmpty(expDataMap) ? utility.isExperimentValid(expDataMap, callToBook,4) : false;
		HotelResult hotelResult = new HotelResult();
		if (hotelResultCB.getPropertyChain() != null && MapUtils.isNotEmpty(hotelResultCB.getPropertyChain().getSummary())) {
			hotelResult.setPropertyChain(getPropertyChain(hotelResultCB.getPropertyChain()));
		}
		if (hotelResultCB.getPropertyHighlights() != null && CollectionUtils.isNotEmpty(hotelResultCB.getPropertyHighlights().getDetails())) {
			hotelResult.setPropertyHighlights(getPropertyHighLights(hotelResultCB.getPropertyHighlights()));
		}
		hotelResult.setGroupBookingHotel(hotelResultCB.isGroupBookingHotel());
		hotelResult.setAddress(getAddress(hotelResultCB.getAddr1(), hotelResultCB.getAddr2()));
		hotelResult.setMapImageUrl(hotelResultCB.getMapImageUrl());
		hotelResult.setPrimaryArea(hotelResultCB.getPrimaryArea());
		hotelResult.setAltAcco(hotelResultCB.isAltAcco());
		if(StringUtils.isNotEmpty(hotelResultCB.getIngoAltaccoTracking())) {
			hotelResult.setSupplierType(hotelResultCB.getIngoAltaccoTracking());
		}
		hotelResult.setCategories(new ArrayList<>(hotelResultCB.getCategories()));
		hotelResult.setCheckinTime(StringUtils.isNotEmpty(hotelResultCB.getCheckInTimeRange())?hotelResultCB.getCheckInTimeRange():hotelResultCB.getCheckIntime());
		hotelResult.setCheckoutTime(StringUtils.isNotEmpty(hotelResultCB.getCheckOutTimeRange())?hotelResultCB.getCheckOutTimeRange():hotelResultCB.getCheckOutTime());
		hotelResult.setCheckInTimeInRange(hotelResultCB.isCheckInTimeInRange());
		hotelResult.setCheckOutTimeInRange(hotelResultCB.isCheckOutTimeInRange());
		hotelResult.setEmail(hotelResultCB.getEmail());
		hotelResult.setHotelIcon(hotelResultCB.getHotelIcon());
		hotelResult.setId(hotelResultCB.getId());
		hotelResult.setLat(hotelResultCB.getLatitude());
		hotelResult.setLng(hotelResultCB.getLongitude());
		hotelResult.setLocationDetail(buildLocationDetail(hotelResultCB.getCityCode(), hotelResultCB.getCityName(),
				hotelResultCB.getCityCtyCode(), hotelResultCB.getCountry()));
		hotelResult.setLongDesc(hotelResultCB.getLongDescription());
		hotelResult.setMobile(hotelResultCB.getMobile());
		hotelResult.setName(hotelResultCB.getName());
		hotelResult.setMaskedPropertyName(hotelResultCB.isMaskedPropertyName());
		hotelResult.setPinCode(hotelResultCB.getPinCode());
		hotelResult.setPropertyType(hotelResultCB.getPropertyType());
		hotelResult.setPropertyLabel(hotelResultCB.getPropertyLabel());
		hotelResult.setShortDesc(hotelResultCB.getShortDescription());
		hotelResult.setStarRating(hotelResultCB.getStarRating());
		hotelResult.setStarRatingType(hotelResultCB.getStarRatingType());
		hotelResult.setHighSellingAltAcco(hotelResultCB.isHighSellingAltAcco());
        hotelResult.setStayType(hotelResultCB.getStayType());
        hotelResult.setHostInfo(buildHostInfo(hotelResultCB.getUsers(), isDisableHostChat));
		if(hotelResultCB.getReportCardPersuasion() !=null) {
			hotelResult.setReportCardPersuasion(buildReportCardPersuasion(hotelResultCB.getReportCardPersuasion()));
		}
		hotelResult.setPopularType(hotelResultCB.getPopularType());
		if(!hotelResultCB.isActiveButOffline()) {
			HostInfoV2 hostChatInfo = hotelResultCB.getHostInfoV2();
			if (hostChatInfo != null) {
				hostChatInfo.setChatEnabled(!isDisableHostChat && hostChatInfo.isChatEnabled());
				hotelResult.setHostInfoV2(hostChatInfo);
			}
		}
		hotelResult.setStaffInfo(convertStaffInfo(hotelResultCB.getStaffInfo()));
		hotelResult.setAmenitiesRatingData(hotelResultCB.getAmenitiesRatingData());

		boolean isAmenitiesV2Enabled = utility.isAmenitiesV2Enabled(expDataMap);
		hotelResult.setAmenities(commonResponseTransformer.getAmenities(hotelResultCB.getAmenities(), isAmenitiesV2Enabled));
		// the lines below will be set and sent to the client only if it is a case of Available but sold out
		hotelResult.setListingDeeplinkUrl(hotelResultCB.getListingDeeplinkUrl());
		hotelResult.setPropertyUnavailableImg(hotelResultCB.getPropertyUnavailableImg());
		hotelResult.setPropertyRulesTitle(hotelResultCB.getPropertyRulesTitle());
		hotelResult.setShowTimeRangeUi(hotelResultCB.isShowTimeRangeUi());
		hotelResult.setFlexibleCheckinInfo(hotelResultCB.getFlexibleCheckinInfo());
		boolean isTravelPlexEnabled = MapUtils.isNotEmpty(expDataMap) && utility.isExperimentOn(expDataMap, TRAVEL_PLEX_ENABLED) ;
		hotelResult.setChatbotInfo(utility.buildChatbotInfoStaticDetail(hotelResultCB.getChatbotInfo(), isTravelPlexEnabled));
		boolean showChatbotHooks = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(CHATBOT_HOOKS_EXP) && TRUE.equalsIgnoreCase(expDataMap.get(CHATBOT_HOOKS_EXP));
		utility.addHooksData(hotelResult.getChatbotInfo(), showChatbotHooks, isTravelPlexEnabled);
		hotelResult.setHomeStayAwardDetails(buildHomeStayAwardDetails(hotelResultCB.getHomeStayAwardsTitleText(), hotelResultCB.getHomeStayAwardImageUrl(), hotelResultCB.isAltAcco(), deviceDetails));
        if (CollectionUtils.isNotEmpty(hotelResultCB.getLongStayAmenities()) && utility.shouldDisplayOfferDiscountBreakup(expDataMap)) {
			SelectRoomAmenities longStayAmenity = commonResponseTransformer.getAmenities(hotelResultCB.getLongStayAmenities(), isAmenitiesV2Enabled).get(0);
			longStayAmenity.setType("LONG_STAY");
			longStayAmenity.setHighlightedText(polyglotService.getTranslatedData(ConstantsTranslation.LONGSTAY_HIGHLIGHTED_TEXT));
			longStayAmenity.setFocus(true);
			hotelResult.getAmenities().add(0, longStayAmenity);
		}
		if (isGroupBookingFunnel) {
			hotelResult.setHighlightedAmenities(hotelResultCB.getGroupBookingAmenities());
		} else {
			hotelResult.setHighlightedAmenities(commonResponseTransformer.getHighlightedAmenities(hotelResultCB.getHighlightedAmenities()));
			hotelResult.setHighlightedAmenitiesV2(commonResponseTransformer.getHighlightedAmenitiesV2(hotelResultCB.getHighlightedAmenities()));
		}
		if (hotelResultCB.getStreetViewInfo() != null) {
			StreetViewInfo streetViewInfoInfo = new StreetViewInfo();
			BeanUtils.copyProperties(hotelResultCB.getStreetViewInfo(), streetViewInfoInfo);
			hotelResult.setStreetViewInfo(streetViewInfoInfo);
			if(utility.isExperimentOn(expDataMap,ExperimentKeys.showstreetviewondetail.getKey())){
				modifyStreetViewInfo(hotelResult.getStreetViewInfo());
			}
		}
		hotelResult.setShowCallToBook(hotelResultCB.isShowCallToBook());
		if (utility.isExperimentOn(expDataMap, EXP_PLV2) && MapUtils.isNotEmpty(hotelResultCB.getAltAccoRoomInfo())) {
			Set<Space> sharedSpacesList = new HashSet<>();
			Set<Space> privateSpacesList = new HashSet<>();
			AtomicReference<SharedInfo> sharedInfo = new AtomicReference<>();
			hotelResultCB.getAltAccoRoomInfo().forEach((key, roomInfo) -> {
				if(roomInfo!=null && roomInfo.getSharedSpaces()!=null && roomInfo.getSharedSpaces().getSharedInfo()!=null) {
					sharedInfo.set(utility.buildSharedInfo(roomInfo.getSharedSpaces().getSharedInfo()));
				}
				utility.getSpaceDataV2(roomInfo.getSharedSpaces(), false, sharedSpacesList);
				utility.getSpaceDataV2(roomInfo.getPrivateSpaces(), true, privateSpacesList);
			});
			if(CollectionUtils.isNotEmpty(privateSpacesList)) {
				hotelResult.setPrivateSpacesV2(new SpaceData());
				hotelResult.getPrivateSpacesV2().setSpaces(new ArrayList<>(privateSpacesList));
			}
			if(CollectionUtils.isNotEmpty(sharedSpacesList)) {
				hotelResult.setSharedSpacesV2(new SpaceData());
				hotelResult.getSharedSpacesV2().setSharedInfo(sharedInfo.get());
				hotelResult.getSharedSpacesV2().setSpaces(new ArrayList<>(sharedSpacesList));
			}
			hotelResult.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(SINGLE_ENTIRE_PROPERTY_LAYOUT_TEXT), hotelResultCB.getPropertyType()));
		}
		hotelResult.setIsABO(hotelResultCB.isActiveButOffline());
		if(hotelResultCB.isActiveButOffline() && !Utility.isGccOrKsa() &&  !myPartnerReq) {
			hotelResult.setSupportDetails(buildSupportDetailsForABO());
		}
		if(hotelResultCB.isShowCallToBook()) {
			String device = (staticDetailRequestBody!=null && staticDetailRequestBody.getDeviceDetails()!=null && StringUtils.isNotEmpty(staticDetailRequestBody.getDeviceDetails().getBookingDevice()))?staticDetailRequestBody.getDeviceDetails().getBookingDevice(): EMPTY_STRING;
			if(hotelResultCB.isActiveButOffline() && utility.isExperimentTrue(expDataMap, ExperimentKeys.aboApplicable.name())) {
				hotelResult.setRequestCallbackData(utility.buildRequestToCallBackDataForB2C(PAGE_CONTEXT_DETAIL));
			} else if(!hotelResultCB.isActiveButOffline() && isCallToBookV2Applicable){
				hotelResult.setRequestCallbackData(utility.buildRequestToCallBackDataV2(PAGE_CONTEXT_DETAIL));
			}else if (!hotelResultCB.isActiveButOffline() && !isCallToBookV2Applicable) {
				hotelResult.setRequestCallbackData(utility.buildRequestToCallBackData(PAGE_CONTEXT_DETAIL, hotelResult.getName(), device));
			}
		}
		if(isNewDetailPageTrue && CollectionUtils.isNotEmpty(hotelResult.getHighlightedAmenities()) && (Constants.ANDROID.equalsIgnoreCase(deviceDetails.getBookingDevice())
				|| Constants.DEVICE_IOS.equalsIgnoreCase(deviceDetails.getBookingDevice()))){
			if (isGroupBookingFunnel) {
				limitAmenitiesCount(hotelResult.getHighlightedAmenities(), hotelResultCB.isServiceApartment(), hotelResultCB.isAltAcco());
			} else {
				limitAmenitiesCount(hotelResult.getHighlightedAmenities(), hotelResultCB.isServiceApartment(), hotelResultCB.isAltAcco());
				limitAmenitiesCount(hotelResult.getHighlightedAmenitiesV2(), hotelResultCB.isServiceApartment(), hotelResultCB.isAltAcco());
			}
		}
		if(!isLiteResponse) {
			hotelResult.setLongStayAmenities(commonResponseTransformer.getHighlightedAmenities(hotelResultCB.getLongStayAmenities()));
			hotelResult.setFreeWifi(hotelResultCB.isFreeWifi());
		}
		hotelResult.setHouseRules(buildHouseRules(hotelResultCB.getHouseRules(), hotelResultCB.getMustReadRules()));
		hotelResult.setHouseRulesV2(buildHouseRulesV2(hotelResult.getHouseRules(), hotelResultCB.getFoodAndDiningRules(),
				hotelResultCB.getSpokenLanguages(),hotelResultCB.getHouseRules(),hotelResultCB.getDepositPolicy(), !dhCall,
				expDataMap));
		if(isLiteResponse){
			hotelResult.setHouseRules(null);
		}
		boolean foodAndDiningV2 = Boolean.parseBoolean(expDataMap.get(FoodAndDiningV2));
		boolean foodDiningRevamp = !utility.isExperimentValid(expDataMap, Constants.FOOD_DINING_REVAMP, 0);
		boolean foodAndDiningEnhancement = utility.isFoodAndDiningEnhancement(expDataMap);
		boolean isMealDetailsPresent = hotelResultCB.isMealDetailsPresent();

		if (hotelResultCB.getFoodDiningV2() != null) {
			hotelResult.setFoodDiningV2(buildFoodDiningV2(
					hotelResultCB.getFoodDiningV2(),
					hotelResultCB.getFoodRatingData(),
					deviceDetails, hotelResultCB.isAltAcco(),
					dhCall, foodAndDiningEnhancement, isMealDetailsPresent
			));
		} else if (CollectionUtils.isNotEmpty(hotelResultCB.getFoodDining())) {
			hotelResult.setFoodDining(buildFoodDining(hotelResultCB.getFoodDining(), hotelResultCB.isFoodDiningHighlight(), staticDetailRequest, deviceDetails, foodAndDiningV2, foodDiningRevamp, hotelResultCB.getFoodRatingData(), hotelResultCB.isAltAcco(), dhCall, foodAndDiningEnhancement, isMealDetailsPresent));
		}

		if (FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnel))
			suppressFewHouseRules(hotelResult.getHouseRulesV2());

		hotelResult.setFaqData(buildFaqData(hotelResultCB.getFAQs()));

		if (StringUtils.isNotBlank(hotelResultCB.getStayType()) && StringUtils.startsWith(hotelResultCB.getStayType().toUpperCase(), "ENTIRE")) {
			hotelResult.setEntireProperty(true);
		}
        hotelResult.setTotalGuestCount(hotelResultCB.getTotalGuestCount());
        hotelResult.setSharingUrl(hotelResultCB.getSharingUrl());
        if (StringUtils.isNotBlank(hotelResultCB.getDetailDeeplinkUrl())) {
            hotelResult.setDetailDeeplinkUrl(hotelResultCB.getDetailDeeplinkUrl());
        }
		hotelResult.setLocationPersuasion(hotelResultCB.getLocationPersuasion());
        hotelResult.setIngoId(hotelResultCB.getGdsHotelCode());
		hotelResult.setLocationPersuasion(hotelResultCB.getLocationPersuasion());
		hotelResult.setContextType(hotelResultCB.getContextType());
		hotelResult.setAlternateDatesAvailable(hotelResultCB.isAlternateDatesAvailable());
		if (!isCorp && CollectionUtils.isNotEmpty(hotelResult.getCategories()) && hotelResult.getCategories().contains(Constants.MMT_VALUE_STAYS)) {
			hotelResult.setTitleIcon(Utility.isGccOrKsa() ? iconUrlGcc : iconUrl);
			addTitleData(hotelResult, hotelResultCB.getOldCountryCode(), isNewDetailPageDesktop);
		}
		hotelResult.setContext(context);
		if(myPartnerReq && hotelResultCB.isGstAssured()){
			CategoryTag categoryTag = new CategoryTag();
			categoryTag.setIconUrl(iconUrlGSTAssured);
			categoryTag.setStyle(new Style());
			categoryTag.getStyle().setIconHeight(29);
			categoryTag.getStyle().setIconWidth(134);
			categoryTag.setTooltip(commonConfigConsul.getMmtMyPartnerTooltip());
			hotelResult.setCategoryTag(categoryTag);
		}
		else if(hotelResultCB.getLuxe() != null && hotelResultCB.getLuxe()) {
			if(isNewDetailPageTrue && (Constants.ANDROID.equalsIgnoreCase(deviceDetails.getBookingDevice())
					|| Constants.DEVICE_IOS.equalsIgnoreCase(deviceDetails.getBookingDevice()))){
				hotelResult.setLuxeIcon(LUXE_ICON_NEW_APP);
			}else{
				hotelResult.setLuxeIcon(getLuxeIcon());
			}
			if(isNewDetailPageDesktop){
				CategoryTag categoryTag = new CategoryTag();
				categoryTag.setIconUrl(luxeIconNewDetailPageDt);
				Style style = new Style();
				style.setIconHeight(23);
				style.setIconWidth(68);
				categoryTag.setStyle(style);
				hotelResult.setCategoryTag(categoryTag);
			}
		}
		hotelResult.setMmtHotelCategory(hotelResultCB.getMmtHotelCategory());
		hotelResult.setMmtHotelText(hotelResultCB.getMmtHotelText());
		if(CollectionUtils.isNotEmpty(hotelResultCB.getSignatureAmenities())) {
			hotelResult.setSignatureAmenities(hotelResultCB.getSignatureAmenities());
		}
		hotelResult.setHighlightedAmenitiesTag(hotelResultCB.getHighlightedAmenitiesTag());
		hotelResult.setCardTitleMap(buildCardTitleMap());
		hotelResult.setHeroImage(hotelResultCB.getHeroImage());
		hotelResult.setHeroVideoUrl(hotelResultCB.getHeroVideoUrl());
		if(hotelResult.getHostInfo() == null || !hotelResult.getHostInfo().isChatEnabled()){
			hotelResult.setGroupBookingQueryEnabled(hotelResultCB.isGroupBookingAllowed());
			hotelResult.setGroupBookingWebUrl(hotelResultCB.getGroupBookingWebUrl());
		}
		hotelResult.setWishListed(hotelResultCB.isWishListed());
		hotelResult.setCalendarCriteria(buildCalendarCriteria(hotelResultCB.getCalendarCriteria()));
		hotelResult.setCategoryIcon(hotelResultCB.getCategoryIcon());
		hotelResult.setIsABSO(hotelResultCB.getActiveButSoldOut());
		hotelResult.setIsRTB(hotelResultCB.isRequestToBook());
		hotelResult.setIsMLOS(hotelResultCB.getCalendarCriteria()!=null);
		buildGovtPolies(hotelResult, hotelResultCB);
		if(StringUtils.isNotEmpty(hotelResultCB.getCategoryUsp()) && !isLiteResponse) {
			hotelResult.setCategoryUspDetailsText(polyglotService.getTranslatedData(BEACHFRONT_CATEGORY_USP_DETAILS_TEXT));
		}
		hotelResult.setHotelCategories(utility.concatenateWithSeparator(PIPE, hotelResult.getCategories()));
        return hotelResult;
    }

	private PropertyHighlightCG getPropertyHighLights(PropertyHighlights propertyHighlights) {
		PropertyHighlightCG propertyHighlightCG = new PropertyHighlightCG();
		if (propertyHighlights.getDetails() != null) propertyHighlightCG.setDetails(getDetails(propertyHighlights.getDetails()));
		propertyHighlightCG.setTitle(propertyHighlights.getTitle());
		propertyHighlightCG.setIcon(propertyHighlights.getIcon());
		return propertyHighlightCG;
	}

	private ReportCardPersuasion buildReportCardPersuasion(@NonNull PersuasionData reportCardPersuasionBO) {
		//TODO: peitho is to be integrated for details page
		ReportCardPersuasion reportCardPersuasion = new ReportCardPersuasion();
		reportCardPersuasion.setIconUrl(reportCardPersuasionBO.getIconurl());
		reportCardPersuasion.setText(reportCardPersuasionBO.getText());
		return reportCardPersuasion;
	}

	private List<PropertyHighlightDetailCG> getDetails(List<PropertyHighlightDetails> details) {
		List<PropertyHighlightDetailCG> highlightDetailCGList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(details)) {
			for (PropertyHighlightDetails detail: details) {
				PropertyHighlightDetailCG propertyHighlightDetailCG = new PropertyHighlightDetailCG();
				propertyHighlightDetailCG.setDesc(detail.getDesc());
				propertyHighlightDetailCG.setTitle(detail.getTitle());
				propertyHighlightDetailCG.setIcon(detail.getIcon());
				highlightDetailCGList.add(propertyHighlightDetailCG);
			}
		}
		return highlightDetailCGList;
	}

	private SupportDetails buildSupportDetailsForABO() {
		SupportDetails supportDetails = new SupportDetails();
		supportDetails.setOptions(new ArrayList<>());
		supportDetails.getOptions().add(callToBookOption);
		return supportDetails;
	}

	private PropertyChainCG getPropertyChain(PropertyChain propertyChain) {
		PropertyChainCG propertyChainCG = new PropertyChainCG();
		if (propertyChain.getDetails() != null) propertyChainCG.setDetails(getPropertyDetails(propertyChain.getDetails()));
		propertyChainCG.setSummary(propertyChain.getSummary());
		propertyChainCG.setLogo(propertyChain.getLogo());
		return propertyChainCG;
	}

	private PropertyChainDetailCG getPropertyDetails(PropertyChainDetails details) {
		PropertyChainDetailCG propertyChainDetailCG = new PropertyChainDetailCG();
		propertyChainDetailCG.setTitle(details.getTitle());
		propertyChainDetailCG.setDesc(details.getDesc());
		return propertyChainDetailCG;
	}

	/**
	 * @param hotelResult CG hotel static details object
	 * @param hotel HES hotel static details object.
	 * if govt policies are present in HES hotel object it will set in CG response
	 * else govt policies in CG hotel object will be null
	 */
	private void buildGovtPolies(HotelResult hotelResult, com.mmt.hotels.model.response.staticdata.HotelResult hotel) {
		if(CollectionUtils.isNotEmpty(hotel.getGovtPolicies())) {
			List<GovtPolicies> govtPolicies = new ArrayList<>();
			hotel.getGovtPolicies().forEach(govtPolicyFromHES -> {
				if(govtPolicyFromHES != null) {
					GovtPolicies govPolicy = new GovtPolicies();
					govPolicy.setNidhiId(govtPolicyFromHES.getNidhiId());
					govPolicy.setLogoUrl(govtPolicyFromHES.getLogoUrl());
					govPolicy.setTitle(govtPolicyFromHES.getTitle());
					govPolicy.setSubTitle(govtPolicyFromHES.getSubTitle());
					govPolicy.setValidTill(govtPolicyFromHES.getValidTill());
					govtPolicies.add(govPolicy);
				}
			});
			hotelResult.setGovtPolicies(govtPolicies);
		}
	}

	private HomeStayAwardDetails buildHomeStayAwardDetails(String titleText, String imageUrl, boolean isAltAcco, DeviceDetails deviceDetails) {
		HomeStayAwardDetails homeStayAwardDetails = null;
		if (isAltAcco && deviceDetails != null && CLIENT_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice())
				&& StringUtils.isNotEmpty(imageUrl) && StringUtils.isNotEmpty(titleText)) {
			homeStayAwardDetails = new HomeStayAwardDetails();
			homeStayAwardDetails.setImageUrl(imageUrl);
			homeStayAwardDetails.setTitleText(titleText);
		}
		return homeStayAwardDetails;
	}

	// merge all house rule
	private HouseRulesV2 buildHouseRulesV2(HouseRules houseRules, List<com.mmt.hotels.model.response.staticdata.CommonRules> foodAndDiningRule,
										   List<String> spokenLanguages, com.mmt.hotels.model.response.staticdata.HouseRules houseRulesCB,
										   DepositPolicy depositPolicy, boolean isInternationalHotel, Map<String, String> expDataMap) {
		if(houseRules == null){
			return null;
		}
		HouseRulesV2 houseRulesV2 = new HouseRulesV2();
		houseRulesV2.setContextRules(houseRules.getContextRules());

		boolean isIhHouseRuleUiV2Enabled = isInternationalHotel && utility.isExperimentTrue(expDataMap, ExperimentKeys.IH_HOUSE_RULE_UI_REVAMP.getKey());

		List<CommonRules> allRules = new ArrayList<>();
		if(houseRules.getMustReadRules() != null){
			allRules.add(convertMustReadRule(houseRules.getMustReadRules()));
		}

		List<CommonRules> foodAndDining = buildCommonRules(foodAndDiningRule);
		if (CollectionUtils.isNotEmpty(foodAndDining)) {
			CommonRules commonRule = new CommonRules();
			commonRule.setCategory(polyglotService.getTranslatedData(FOOD_AND_DINING));
			commonRule.setId(FOOD_AND_DINING.toLowerCase());
			commonRule.setShowInDetailHome(true);
			commonRule.setSubCategories(foodAndDining);
			allRules.add(commonRule);
		}

		if (CollectionUtils.isNotEmpty(houseRules.getCommonRules())) {
			for(CommonRules commonRules: houseRules.getCommonRules()){
				if(Constants.GUEST_PROFILE.equalsIgnoreCase(commonRules.getId()) || Constants.SAFETY_AND_HYGIENE.equalsIgnoreCase(commonRules.getId())){
					commonRules.setShowInDetailHome(true);
				}
				allRules.add(commonRules);
			}
		}

		List<Rule> rules = new ArrayList<>();
		List<ChildExtraBedPolicy> extraBedPolicy = houseRules.getExtraBedPolicyList();
		if (CollectionUtils.isNotEmpty(extraBedPolicy)) {
			for(ChildExtraBedPolicy childExtraBedPolicy : extraBedPolicy){
				rules.add(new Rule(childExtraBedPolicy.getPolicyInfo()));
				if (CollectionUtils.isNotEmpty(childExtraBedPolicy.getPolicyRules())) {
					for(PolicyRules policyRules : childExtraBedPolicy.getPolicyRules()){
						if (CollectionUtils.isNotEmpty(policyRules.getExtraBedTerms())) {
							for (ExtraBedRules extraBedRules : policyRules.getExtraBedTerms()){
								if(extraBedRules.getValue() != null) {
									rules.add(new Rule(extraBedRules.getValue()));
								}
							}
						}
					}
				}
			}
			CommonRules commonRules = new CommonRules();
			commonRules.setCategory(polyglotService.getTranslatedData(EXTRA_BED_POLICY));
			commonRules.setId(EXTRA_BED_POLICY.toLowerCase());
			commonRules.setRules(rules);
			allRules.add(commonRules);
		}

		if(houseRulesCB != null && CollectionUtils.isNotEmpty(houseRulesCB.getCategoryInfoList())) {
			for (CategoryInfo categoryInfo : houseRulesCB.getCategoryInfoList()) {
				if ("BREAKFAST_CHARGES".equalsIgnoreCase(categoryInfo.getId())
						|| "EXTRA_BED_POLICY".equalsIgnoreCase(categoryInfo.getId())
						|| LANGUAGES_SPOKEN.equalsIgnoreCase(categoryInfo.getId())
				) {
					CommonRules categoryInfoRule = buildCategoryCommonRules(categoryInfo);
					if (categoryInfoRule != null) {
						allRules.add(categoryInfoRule);
					}
				}
			}
		}

		if (depositPolicy != null) {
			com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo ruleTableInfoCG = Utility.buildRuleTableInfo(depositPolicy.getRuleTableInfo());
			if (ruleTableInfoCG != null) {
				CommonRules commonRule = new CommonRules();
				commonRule.setId(depositPolicy.getId());
				commonRule.setCategory(depositPolicy.getCategoryName());
				commonRule.setCategoryName(depositPolicy.getCategoryName());
				commonRule.setCategoryDesc(depositPolicy.getCategoryDesc());
				commonRule.setRuleDesc(depositPolicy.getRuleInfo());
				commonRule.setRuleTableInfo(ruleTableInfoCG);
				commonRule.setRules(buildCategoryRules(depositPolicy.getRuleDesc()));
				commonRule.setShowInDetailHome(true);
				commonRule.setCategoryHeading(depositPolicy.getCategoryHeading());
				allRules.add(commonRule);
			}
		}

		houseRulesV2.setAllRules(allRules);

		//For isIhHouseRuleUiV2Enabled, languages data comes as separate category item in HES response
		if (CollectionUtils.isNotEmpty(spokenLanguages) && !isIhHouseRuleUiV2Enabled) {
			houseRulesV2.setLanguage(prepareSpokenLanguagesString(spokenLanguages));
			houseRulesV2.setLanguageHeader(polyglotService.getTranslatedData(SPOKEN_LANGUAGE_HEADER));
			// Spoken languages is a part of house rules on detail page after release that contains pokus EXTRA_BNB_EXP_KEY
			if (CollectionUtils.isNotEmpty(allRules) && utility.isExperimentTrue(expDataMap, EXTRA_BNB_EXP_KEY)) {
				CommonRules firstRule = allRules.stream()
						.filter(rule -> rule.isShowInDetailHome() && rule.isExpandRules())
						.findFirst()
						.orElse(null);
				if (firstRule != null) {
					Rule rule = new Rule();
					rule.setHeaderTitle(houseRulesV2.getLanguageHeader());
					rule.setSubTitle(houseRulesV2.getLanguage());
					if (CollectionUtils.isNotEmpty(firstRule.getRules()))
						firstRule.getRules().add(rule);
					else
						firstRule.setRules(Collections.singletonList(rule));
				}
			}
		}
		return houseRulesV2;
	}

	 private List<Rule> buildCategoryRules(List<String> rulesList){
		 List<Rule> rules = new ArrayList<>();
		 if(CollectionUtils.isNotEmpty(rulesList)) {
			 for(String ruleDesc: rulesList){
				 Rule rule = new Rule();
				 rule.setText(ruleDesc);
				 rules.add(rule);
			 }
		 }
		 return rules;
	 }

	private CommonRules buildCategoryCommonRules(CategoryInfo categoryInfo){
		if(categoryInfo != null){
			boolean isLanguagesSpokenCategory = LANGUAGES_SPOKEN.equalsIgnoreCase(categoryInfo.getId());
			com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo ruleTableInfoCG = Utility.buildRuleTableInfo(categoryInfo.getRuleTableInfo());
			if (ruleTableInfoCG == null && !isLanguagesSpokenCategory) {
				return null;
			}
			CommonRules commonRules = new CommonRules();
			commonRules.setRuleDesc(categoryInfo.getRuleInfo());
			commonRules.setRuleTableInfo(ruleTableInfoCG);
			commonRules.setCategory(categoryInfo.getCategoryName());
			commonRules.setCategoryDesc(categoryInfo.getCategoryDesc());
			commonRules.setCategoryName(categoryInfo.getCategoryName());
			commonRules.setId(categoryInfo.getId());
			commonRules.setRules(buildCategoryRules(categoryInfo.getRuleDesc()));
			commonRules.setCategoryHeading(categoryInfo.getCategoryHeading());
			commonRules.setShowInDetailHome(true);
			commonRules.setShowArrowInDetailHome(!isLanguagesSpokenCategory);
			commonRules.setShowInL2Page(!isLanguagesSpokenCategory);
			return commonRules;
		}
		return null;
	}

	/**
	 * if list = {a,b,c,d}
	 * Will return a string "a, b, c and d"
	 * @param spokenLanguages
	 * @return
	 */
	private String prepareSpokenLanguagesString(List<String> spokenLanguages) {
		String spokenLangString = null;
		if (CollectionUtils.isNotEmpty(spokenLanguages)) {
			StringBuilder languageBuilder = new StringBuilder();
			if (spokenLanguages.size() == 1) {
				languageBuilder.append(spokenLanguages.get(0).trim());
			} else {
				for (int i = 0; i < spokenLanguages.size(); i++) {
					if (i == spokenLanguages.size() - 1) {
						languageBuilder.append(AND_STRING);
						languageBuilder.append(SPACE);
						languageBuilder.append(spokenLanguages.get(i).trim());
						continue;
					}
					if (i == spokenLanguages.size() - 2) {
						languageBuilder.append(spokenLanguages.get(i).trim());
						languageBuilder.append(SPACE);
						continue;
					}
					languageBuilder.append(spokenLanguages.get(i).trim());
					languageBuilder.append(COMMA);
					languageBuilder.append(SPACE);
				}
			}
			spokenLangString = languageBuilder.toString();
		}
		return spokenLangString;
	}

	private DiningRuleItem buildFoodDiningV2RuleItem(com.mmt.hotels.model.response.staticdata.Rule ruleHES) {
		if (ruleHES == null) {
			return null;
		}
		
		DiningRuleItem diningRuleItem = new DiningRuleItem();

		diningRuleItem.setTitleText(ruleHES.getTitleText());
		diningRuleItem.setText(ruleHES.getText());
		diningRuleItem.setIconUrl(ruleHES.getIconUrl());

		// Convert HES MealTypes to CG MealTypes
		if (CollectionUtils.isNotEmpty(ruleHES.getMealTypes())) {
			List<MealType> cgMealTypes = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.meals.MealType hesMealType : ruleHES.getMealTypes()) {
				if (hesMealType != null) {
					MealType cgMealType = new MealType();
					cgMealType.setName(hesMealType.getName());
					cgMealType.setIcon(hesMealType.getIcon());
					cgMealTypes.add(cgMealType);
				}
			}
			diningRuleItem.setMealTypes(CollectionUtils.isNotEmpty(cgMealTypes) ? cgMealTypes : null);
		}
		
		// Convert infoData to additionalInfo if present
		if (ruleHES.getInfoData() != null && CollectionUtils.isNotEmpty(ruleHES.getInfoData().getData())) {
			List<String> additionalInfo = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.Datum dataItem : ruleHES.getInfoData().getData()) {
				if (dataItem != null) {
					String key = dataItem.getKey();
					String value = dataItem.getValue();
					if (StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(value)) {
						additionalInfo.add(key + ": " + value);
					}
				}
			}
			if (CollectionUtils.isNotEmpty(additionalInfo)) {
				diningRuleItem.setAdditionalInfo(additionalInfo);
				// Wrap text with bold HTML tags when additionalInfo is present
				if (StringUtils.isNotEmpty(diningRuleItem.getText())) {
					diningRuleItem.setText("<b>" + diningRuleItem.getText() + "</b>");
				}
			}
		}
		
		return diningRuleItem;
	}


	private FoodDiningV2 buildFoodDiningV2(
			com.mmt.hotels.model.response.staticdata.FoodDiningV2 foodDiningV2HES,
			UGCRatingData foodRatingData,
			DeviceDetails deviceDetails,
			boolean isAltAcco,
			boolean isDhCall,
			boolean foodAndDiningEnhancement,
			boolean isMealDetailsPresent
	) {
		// Return null if foodDiningV2HES is null or if both diningInfo and summary are null or empty
		if (foodDiningV2HES == null || 
			(CollectionUtils.isEmpty(foodDiningV2HES.getDiningInfo()) && 
			 CollectionUtils.isEmpty(foodDiningV2HES.getSummary()))) {
			return null;
		}

		FoodDiningV2 foodDiningV2CG = new FoodDiningV2();

		foodDiningV2CG.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_CARD_TITLE));

		foodDiningV2CG.setMealDetailsPresent(isMealDetailsPresent);

		List<FoodDiningRule> allRulesList = new ArrayList<>();
		List<SummaryItem> summaryList = new ArrayList<>();
		List<Restaurant> restaurants = new ArrayList<>();

		if (foodDiningV2HES != null && foodDiningV2HES.getDiningInfo() != null && CollectionUtils.isNotEmpty(foodDiningV2HES.getDiningInfo())) {
			for (com.mmt.hotels.model.response.staticdata.CommonRules diningRuleHES : foodDiningV2HES.getDiningInfo()) {

				if (diningRuleHES.getCategory().equalsIgnoreCase(FoodAndDiningEnums.FoodMenu.getName())
						|| diningRuleHES.getCategory().equalsIgnoreCase(FoodAndDiningEnums.Cook.getName())) {
					continue;
				}

				FoodDiningRule diningRuleCG = new FoodDiningRule();

				if (FoodAndDiningEnums.Kitchen.getName().equalsIgnoreCase(diningRuleHES.getCategory())) {
					diningRuleCG.setCategory(KITCHEN_CATEGORY_REVAMP);
				} else {
					diningRuleCG.setCategory(diningRuleHES.getCategory());
				}

				diningRuleCG.setId(diningRuleHES.getId());
				diningRuleCG.setHeading(diningRuleHES.getHeading());
				diningRuleCG.setDescription(diningRuleHES.getDescription());
				diningRuleCG.setImages(diningRuleHES.getImages());
				diningRuleCG.setShowInL2Page(diningRuleHES.isShowInL2Page());
				diningRuleCG.setShowInDetailHome(diningRuleHES.isShowInDetailHome());
				diningRuleCG.setShowArrowInDetailHome(true);

				List<DiningRuleItem> ruleItems = new ArrayList<>();

				if (diningRuleHES.getRules() != null && CollectionUtils.isNotEmpty(diningRuleHES.getRules())) {
					for (com.mmt.hotels.model.response.staticdata.Rule ruleHES : diningRuleHES.getRules()) {
						DiningRuleItem ruleItem = buildFoodDiningV2RuleItem(ruleHES);
						if (ruleItem != null) {
							ruleItems.add(ruleItem);
						}
					}
				}

				diningRuleCG.setRules(CollectionUtils.isNotEmpty(ruleItems) ? ruleItems : null);

				if (diningRuleHES.getMealDetails() != null) {
					if(foodRatingData != null){
						foodRatingData.setMergeId(diningRuleHES.getId());
					}
					commonResponseTransformer.buildMealDetails(diningRuleCG, diningRuleHES.getMealDetails());
				}

				if(FoodAndDiningEnums.Restaurant.getName().equalsIgnoreCase(diningRuleHES.getCategory())){
					if (!isAltAcco && foodAndDiningEnhancement) {
						foodDiningV2CG.setTitle(RESTAURANTS_TITLE);
					}
					if(Utility.isBookingDeviceDesktop(deviceDetails)){
						diningRuleCG.setShowInDetailHome(false);
					}
					if (isDhCall && CollectionUtils.isNotEmpty(diningRuleHES.getImages())) {
						diningRuleCG.setShowArrowInDetailHome(false);
						restaurants.add(new Restaurant(diningRuleHES.getHeading(), diningRuleHES.getImages().get(0), diningRuleHES.getId()));
					}
				}

				allRulesList.add(diningRuleCG);
			}
		}

		if(foodRatingData != null){
			foodDiningV2CG.setRatingData(foodRatingData);
		}

		// Map summary items from foodDiningV2HES to foodDiningV2CG
		if(foodDiningV2HES != null && foodDiningV2HES.getSummary() != null && CollectionUtils.isNotEmpty(foodDiningV2HES.getSummary())) {
			for(FoodDiningSummaryItem summaryItemHES : foodDiningV2HES.getSummary()) {
				SummaryItem summaryItemCG = new SummaryItem();
				if(StringUtils.isNotEmpty(summaryItemHES.getText())) {
					summaryItemCG.setText(summaryItemHES.getText());
				}
				if(StringUtils.isNotEmpty(summaryItemHES.getIconUrl())) {
					summaryItemCG.setIconUrl(summaryItemHES.getIconUrl());
				}
				summaryList.add(summaryItemCG);
			}
			foodDiningV2CG.setSummary(summaryList);
		}

		foodDiningV2CG.setRestaurants(CollectionUtils.isNotEmpty(restaurants) ? restaurants : null);
		foodDiningV2CG.setAllRules(CollectionUtils.isNotEmpty(allRulesList) ? allRulesList : null);
		foodDiningV2CG.setFeedbackData(buildFoodDiningFeedbackData());

		return foodDiningV2CG;
	}

	private HouseRulesV2 buildFoodDining(List<com.mmt.hotels.model.response.staticdata.CommonRules> foodDiningRule, boolean foodDiningHighlight, StaticDetailCriteria staticDetailRequest, DeviceDetails deviceDetails, boolean foodAndDiningV2, boolean foodDiningRevamp, UGCRatingData foodRatingData, boolean isAltAcco, boolean isDhCall, boolean foodAndDiningEnhancement, boolean isMealDetailsPresent) {
		HouseRulesV2 foodDining = new HouseRulesV2();
		foodDining.setTitle(foodDiningRevamp ? polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_CARD_TITLE) : polyglotService.getTranslatedData(FOOD_AND_DINING));
		foodDining.setTag(foodDiningHighlight);

		foodDining.setMealDetailsPresent(isMealDetailsPresent);

		if(foodRatingData != null){
			foodDining.setRatingData(foodRatingData);
			foodDining.getRatingData().setMergeId(null);
		}

		List<CommonRules> allRulesList = new ArrayList<>();
		List<String> summaryList = new ArrayList<>();
		List<SummaryItem> summaryListV2 = new ArrayList<>();
		List<Restaurant> restaurants = new ArrayList<>();
		Map<String, List<com.mmt.hotels.model.response.staticdata.CommonRules>> sectionToRuleMap = buildSectionToRuleMap(foodDiningRule);

		if (MapUtils.isNotEmpty(sectionToRuleMap)) {
			for (Map.Entry<String, List<com.mmt.hotels.model.response.staticdata.CommonRules>> sectionToRuleEntry : sectionToRuleMap.entrySet()) {
				if(CollectionUtils.isNotEmpty(sectionToRuleEntry.getValue())){
					for(com.mmt.hotels.model.response.staticdata.CommonRules commonRule : sectionToRuleEntry.getValue()) {
						if (sectionToRuleEntry.getKey().equalsIgnoreCase(FoodAndDiningEnums.FoodMenu.getName())) {
							continue;
						}
						//Ignoring Cook Section
						if (foodDiningRevamp && sectionToRuleEntry.getKey().equalsIgnoreCase(FoodAndDiningEnums.Cook.getName())) {
							if (Objects.nonNull(commonRule)) {
								summaryList.add(commonRule.getSummaryText());
							}
							continue;
						}
						CommonRules commonRules = new CommonRules();
						if (foodDiningRevamp && Objects.nonNull(commonRule) &&
								FoodAndDiningEnums.Kitchen.getName().equalsIgnoreCase(commonRule.getCategory())) {
							commonRules.setCategory(KITCHEN_CATEGORY_REVAMP);
						} else {
							commonRules.setCategory(commonRule.getCategory());
						}
						commonRules.setDescription(commonRule.getHostCatHeading());

						if (foodDiningMergeSections.contains(commonRules.getCategory()) || FoodAndDiningEnums.IndianFoodOptions.getName().equalsIgnoreCase(commonRule.getCategory()) ) {
							commonRules.setMergeId(FND_MERGE_ID_STRING);
						}

						if (commonRule.getCategory().equalsIgnoreCase(FoodAndDiningEnums.Meals.getName()) && sectionToRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())) {
							commonRules.setRules(buildMealRuleList(sectionToRuleMap, deviceDetails));
						} else if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
							commonRules.setRules(buildRuleList(commonRule));
						}

						if (!(foodAndDiningV2 & ADDITIONAL_INFORMATION.equalsIgnoreCase(commonRule.getCategory()))) {
							commonRules.setShowInDetailHome(true);
						}
						commonRules.setImages(commonRule.getImages());
						if(FoodAndDiningEnums.IndianFoodOptions.getName().equalsIgnoreCase(commonRule.getCategory()) ) {
							commonRules.setId(commonRule.getId());
							commonRules.setShowInDetailHome(commonRule.isShowInDetailHome());
							commonRules.setShowInL2Page(commonRule.isShowInL2Page());
						}
						if(FoodAndDiningEnums.FoodAndDiningPropertyRules.getName().equalsIgnoreCase(commonRule.getCategory()) ) {
							commonRules.setId(commonRule.getId());
							commonRules.setShowInDetailHome(commonRule.isShowInDetailHome());
							commonRules.setShowInL2Page(commonRule.isShowInL2Page());
						}
						if(FoodAndDiningEnums.Restaurant.getName().equalsIgnoreCase(commonRule.getCategory())){
							if (!isAltAcco && foodAndDiningEnhancement) {
								foodDining.setTitle(RESTAURANTS_TITLE);
							}
							commonRules.setId(commonRule.getId());
							if (!foodAndDiningEnhancement)
								commonRules.setMergeId(commonRule.getMergeId());
							commonRules.setShowInDetailHome(commonRule.isShowInDetailHome());
							if(Utility.isBookingDeviceDesktop(deviceDetails)){
								commonRules.setShowInDetailHome(false);
							}
							if (isDhCall && CollectionUtils.isNotEmpty(commonRule.getImages())) {
								commonRules.setShowArrowInDetailHome(false);
								restaurants.add(new Restaurant(commonRule.getHostCatHeading(), commonRule.getImages().get(0), commonRule.getId()));
							}
							if (StringUtils.isNotEmpty(commonRule.getUspText())) {
								commonRules.setUspText(commonRule.getUspText());
							}
						}
						allRulesList.add(commonRules);
						if (!ADDITIONAL_INFORMATION.equalsIgnoreCase(commonRule.getCategory())
								&& !Utility.isBookingDeviceDesktop(deviceDetails)) {
							if(FoodAndDiningEnums.IndianFoodOptions.getName().equalsIgnoreCase(commonRule.getCategory())) {
								if(CollectionUtils.isNotEmpty(commonRule.getRules())){
									for (Rule rule : commonRules.getRules()) {

										if(StringUtils.isNotEmpty(rule.getText())){
											summaryList.add(rule.getText());
										}
										if (StringUtils.isNotEmpty(rule.getText())) {
											summaryListV2.add(new SummaryItem(rule.getText(), rule.getIconUrl()));
										}
									}
								}
							}
							else if (foodAndDiningV2 && StringUtils.isNotEmpty(commonRule.getSummaryText()) && !FoodAndDiningEnums.FoodAndDiningPropertyRules.getName().equalsIgnoreCase(commonRule.getCategory())) {
								summaryList.add(commonRule.getSummaryText());
								if (StringUtils.isNotEmpty(commonRule.getSummaryText())) {
									summaryListV2.add(new SummaryItem(commonRule.getSummaryText(), null));
								}
							}
							else if (StringUtils.isNotEmpty(commonRule.getHostCatHeading()) &&
									Objects.nonNull(commonRule) && !Constants.MEALS_AND_COOK_DETAILS.equalsIgnoreCase(commonRule.getCategory())
									&& !FoodAndDiningEnums.Restaurant.getName().equalsIgnoreCase(commonRule.getCategory()) && !FoodAndDiningEnums.FoodAndDiningPropertyRules.getName().equalsIgnoreCase(commonRule.getCategory())) {
								summaryList.add(commonRule.getHostCatHeading());
								if (StringUtils.isNotEmpty(commonRule.getHostCatHeading())) {
									summaryListV2.add(new SummaryItem(commonRule.getHostCatHeading(), null));
								}
							}
						}
					}
					}
			}
		}
		foodDining.setRestaurants(CollectionUtils.isNotEmpty(restaurants) ? restaurants : null);

		foodDining.setAllRules(CollectionUtils.isNotEmpty(allRulesList) ? allRulesList : null);

		//if there is only one section then put ruleText from ruleList into summary list
		if (CollectionUtils.isNotEmpty(foodDiningRule) && foodDiningRule.size() == 1 && ADDITIONAL_INFORMATION.equalsIgnoreCase(foodDiningRule.get(0).getCategory())) {
			handleOneFoodAndDiningSection(foodDiningRule.get(0), summaryList, allRulesList);
		}

		// these are L1 overview pointers
		if (CollectionUtils.isNotEmpty(summaryList)) {
			// if childContext (child count > 0) is there in request, add node "Special meal for Kids is available on request"
			if (MapUtils.isNotEmpty(sectionToRuleMap) && sectionToRuleMap.containsKey(FoodAndDiningEnums.Cook.getName())) {
				Integer childCount = utility.getTotalChildrenFromRequest(staticDetailRequest.getRoomStayCandidates());
				if (childCount != null && childCount > 0) {
					String mealForKids = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_FOR_KIDS);
					if (StringUtils.isNotEmpty(mealForKids) && !Constants.NULL_STRING.equalsIgnoreCase(mealForKids)) {
						summaryList.add(mealForKids);
					}
				}
			}
			foodDining.setSummary(summaryList);
			foodDining.setSummaryV2(summaryListV2);
		}
		return foodDining;
	}

	private CardAction buildFoodDiningFeedbackData() {
		CardAction cardAction = new CardAction();
		cardAction.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_CARD_TITLE));

		CardActionData cardActionData = new CardActionData();
		cardActionData.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_TITLE));
		
		// Create sections list
		List<Section> sections = new ArrayList<>();
		Section section = new Section();
		section.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_DESCRIPTION));
		
		// Create items list
		List<Item> items = new ArrayList<>();
		
		// First item - "The list was very long"
		Item item1 = new Item();
		item1.setText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_REASON_ONE));
		item1.setType(FEEDBACK_ITEM_TYPE_TEXT);
		items.add(item1);
		
		// Second item - "Did not find the amenity I was looking for"
		Item item2 = new Item();
		item2.setText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_REASON_TWO));
		item2.setType(FEEDBACK_ITEM_TYPE_TEXT);
		items.add(item2);
		
		// Third item - "My reason is not listed here" with textBox
		Item item3 = new Item();
		item3.setText(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_REASON_THREE));
		item3.setType(FEEDBACK_ITEM_TYPE_TEXTBOX);
		item3.setTextBoxTitle(polyglotService.getTranslatedData(ConstantsTranslation.FOOD_DINING_FEEDBACK_SHEET_TEXT_BOX_TITLE));
		items.add(item3);
		
		section.setItems(items);
		sections.add(section);
		cardActionData.setSections(sections);

		cardAction.setData(cardActionData);
		return cardAction;
	}

	private void handleFoodAndDiningBulletsOnL1Page(com.mmt.hotels.model.response.staticdata.CommonRules commonRules, List<String> summaryList) {
		if (CollectionUtils.isNotEmpty(commonRules.getRules())) {

		}
	}

	private Map<String, List<com.mmt.hotels.model.response.staticdata.CommonRules>> buildSectionToRuleMap(List<com.mmt.hotels.model.response.staticdata.CommonRules> foodDiningRule) {
		Map<String, List<com.mmt.hotels.model.response.staticdata.CommonRules>> sectionToRuleMap = new LinkedHashMap<>();

		if (CollectionUtils.isNotEmpty(foodDiningRule)) {
			for (com.mmt.hotels.model.response.staticdata.CommonRules commonRule : foodDiningRule) {
				if (StringUtils.isNotEmpty(commonRule.getCategory())) {
					if(!sectionToRuleMap.containsKey(commonRule.getCategory())) {
						sectionToRuleMap.put(commonRule.getCategory(), new ArrayList<>());
					}
					List<com.mmt.hotels.model.response.staticdata.CommonRules> rulesList = sectionToRuleMap.get(commonRule.getCategory());
					rulesList.add(commonRule);
					sectionToRuleMap.put(commonRule.getCategory(), rulesList);
				}
			}
		}
		return sectionToRuleMap;
	}

	private List<Rule> buildMealRuleList(Map<String, List<com.mmt.hotels.model.response.staticdata.CommonRules>> sectionToCommonRuleMap, DeviceDetails deviceDetails) {
		Rule foodMenuRule = null;
		if (sectionToCommonRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())
				&& CollectionUtils.isNotEmpty(sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules())
				&& null != sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules().get(0)) {
			com.mmt.hotels.model.response.staticdata.Rule foodAndDiningMenuRule = sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules().get(0);
			foodMenuRule = new Rule();
			foodMenuRule.setText(foodAndDiningMenuRule.getText());
			foodMenuRule.setImages(foodAndDiningMenuRule.getImages());
			foodMenuRule.setImageCategory(foodAndDiningMenuRule.getImageCategory());
			foodMenuRule.setInfoData(foodAndDiningMenuRule.getInfoData());
			foodMenuRule.setIconUrl(utility.getUrlFromConfig(foodAndDiningMenuRule.getIconUrl()));
		}

		List<Rule> ruleList = buildRuleList(sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()).get(0));
		if (deviceDetails != null && Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice())) {
			if (foodMenuRule != null && sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()).get(0).getRules().size() > foodMenuPosition) {
				ruleList.add(foodMenuPosition, foodMenuRule);
			}
		} else {
			ruleList.add(foodMenuRule);
		}
		return ruleList;
	}

	private List<Rule> buildRuleList(com.mmt.hotels.model.response.staticdata.CommonRules commonRule) {
		List<Rule> ruleList = new ArrayList<>();
		for (com.mmt.hotels.model.response.staticdata.Rule rules : commonRule.getRules()) {
			Rule rule = new Rule();
			rule.setText(rules.getText());
			rule.setIconUrl(utility.getUrlFromConfig(rules.getIconUrl()));
			rule.setInfoData(rules.getInfoData());
			ruleList.add(rule);
		}
		return ruleList;
	}

	private void handleOneFoodAndDiningSection(com.mmt.hotels.model.response.staticdata.CommonRules commonRule, List<String> summaryList, List<CommonRules> allRulesList) {
		summaryList.clear();
		if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
			int count = 0;
			for (com.mmt.hotels.model.response.staticdata.Rule rule : commonRule.getRules()) {
				count++;
				summaryList.add(rule.getText());

				if (count == foodDiningMinCountConfig) {
					break;
				}
			}
			allRulesList.clear();
		}
	}

	private CommonRules convertMustReadRule(CommonRules mustReadRules){
		if (CollectionUtils.isNotEmpty(mustReadRules.getRulesList())) {
			List<Rule> rules = mustReadRules.getRulesList().stream().map(Rule::new).collect(Collectors.toList());
			mustReadRules.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.RESTRICTIONS));
			mustReadRules.setId(RESTRICTIONS.toLowerCase());
			mustReadRules.setShowInDetailHome(true);
			mustReadRules.setExpandRules(true);
			mustReadRules.setRules(rules);
			mustReadRules.setRulesList(null);
		}
		return mustReadRules;
	}


	protected abstract Map<String, String> buildCardTitleMap();

	protected abstract void addTitleData(HotelResult hotelResult, String countryCode, boolean isNewDetailPageDesktop);

	protected abstract String getLuxeIcon();

	private HouseRules buildHouseRules(com.mmt.hotels.model.response.staticdata.HouseRules houseRules, List<String> mustReadRules) {
		HouseRules houseRulesCG = null;
		if (houseRules != null ) {
			houseRulesCG = new HouseRules();
			houseRulesCG.setChildExtraBedPolicy(buildChildExtraBedPolicy(houseRules.getChildExtraBedPolicy()));
			houseRulesCG.setCommonRules(buildCommonRules(houseRules.getCommonRules()));
			houseRulesCG.setExtraBedPolicyList(buildExtraBedPolicyList(houseRules.getExtraBedPolicyList()));
			houseRulesCG.setOtherInfo(buildCommonRules(houseRules.getOtherInfo()));
			houseRulesCG.setMustReadRules(buildMustReadRules(mustReadRules));
			houseRulesCG.setContextRules(houseRules.getContextRules());
		}
		return houseRulesCG;
	}

	private FaqData buildFaqData( List<Faqs> faqs) {
		FaqData faqData = null;
		if(faqs != null)
		{
			faqData = new FaqData();
			faqData.setFaqs(faqs);
			faqData.setTitle(FAQ_TITLE);
			faqData.setHint(FAQ_HINT);
			faqData.setItemCountForCard(Integer.valueOf(FAQ_ITEM_COUNT));
			faqData.setExtraItemText(FAQ_EXTRA_TEXT.replace("%d", ""+faqs.size()));
		}
		return faqData;
	}

	private CommonRules buildMustReadRules(List<String> mustReadRules) {
		if(CollectionUtils.isEmpty(mustReadRules)) {
			return null;
		}
		CommonRules mustReadRulesCG = new CommonRules();
		mustReadRulesCG.setCategory("must read");
		mustReadRulesCG.setRulesList(mustReadRules);
		return mustReadRulesCG;
	}

	private List<ChildExtraBedPolicy> buildExtraBedPolicyList(List<com.mmt.hotels.model.response.staticdata.ChildExtraBedPolicy> extraBedPolicyList) {
		List<ChildExtraBedPolicy> listCG = null;
		if (CollectionUtils.isNotEmpty(extraBedPolicyList)){
			listCG = new ArrayList<>();
			for(com.mmt.hotels.model.response.staticdata.ChildExtraBedPolicy policy : extraBedPolicyList){
				listCG.add(buildChildExtraBedPolicy(policy));
			}
		}
		return listCG;
	}

	private List<CommonRules> buildCommonRules(List<com.mmt.hotels.model.response.staticdata.CommonRules> commonRules) {
		List<CommonRules> commonRulesCG = null;
		if (CollectionUtils.isNotEmpty(commonRules)){
			commonRulesCG = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.CommonRules commonRule : commonRules){
				CommonRules commonRuleCG = new CommonRules();
				commonRuleCG.setCategory(commonRule.getCategory());
				commonRuleCG.setCategoryId(commonRule.getCategoryId());
				commonRuleCG.setId(commonRule.getId());
				commonRuleCG.setRules(buildRules(commonRule.getRules()));
				commonRuleCG.setHostCatHeading(commonRule.getHostCatHeading());
				commonRuleCG.setShowInHost(commonRule.isShowInHost());
				commonRuleCG.setShowInDetailHome(commonRule.isShowInDetailHome());
				commonRuleCG.setExpandRules(commonRule.isExpandRules());
				commonRuleCG.setImages(commonRule.getImages());
				commonRuleCG.setShowInL2Page(commonRule.isShowInL2Page());
				commonRulesCG.add(commonRuleCG );
			}
		}
		return commonRulesCG;
	}

	private List<Rule> buildRules(List<com.mmt.hotels.model.response.staticdata.Rule> rules) {
		List<Rule> rulesCG = null;
		if (CollectionUtils.isNotEmpty(rules)){
			rulesCG = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.Rule rule : rules){
				Rule ruleCG = new Rule();
				ruleCG.setDisplay(rule.getDisplay());
				ruleCG.setDisplayRank(rule.getDisplayRank());
				ruleCG.setSentiment(rule.getSentiment());
				ruleCG.setTemplateText(rule.getTemplateText());
				ruleCG.setText(rule.getText());
				ruleCG.setIconUrl(utility.getUrlFromConfig(rule.getIconUrl()));
				ruleCG.setInfoData(rule.getInfoData());
				rulesCG.add(ruleCG );
			}
		}
		return rulesCG;
	}

	private ChildExtraBedPolicy buildChildExtraBedPolicy(
			com.mmt.hotels.model.response.staticdata.ChildExtraBedPolicy childExtraBedPolicy) {
		if (null == childExtraBedPolicy){
			return null;
		}
		ChildExtraBedPolicy childExtraBedPolicyCG = new ChildExtraBedPolicy();
		childExtraBedPolicyCG.setId(childExtraBedPolicy.getId());
		childExtraBedPolicyCG.setLabel(childExtraBedPolicy.getLabel());
		childExtraBedPolicyCG.setPaid(childExtraBedPolicy.getPaid());
		childExtraBedPolicyCG.setPolicyInfo(childExtraBedPolicy.getPolicyInfo());
		childExtraBedPolicyCG.setPolicyRules(buildPolicyRules(childExtraBedPolicy.getPolicyRules()));
		return childExtraBedPolicyCG;
	}

	private List<PolicyRules> buildPolicyRules(List<com.mmt.hotels.model.response.staticdata.PolicyRules> policyRules) {
		List<PolicyRules> policyRulesCG = null;
		if (CollectionUtils.isNotEmpty(policyRules)){
			policyRulesCG = new ArrayList<>();
			for (com.mmt.hotels.model.response.staticdata.PolicyRules policyRule : policyRules){
				PolicyRules PolicyRulesCG = new PolicyRules();
				PolicyRulesCG.setAgeGroup(policyRule.getAgeGroup());
				PolicyRulesCG.setExtraBedTerms(buildExtraBedTerms(policyRule.getExtraBedTerms()));
				policyRulesCG.add(PolicyRulesCG);
			}
		}
		return policyRulesCG;
	}

	private Set<ExtraBedRules> buildExtraBedTerms(Set<com.mmt.hotels.model.response.staticdata.ExtraBedRules> extraBedTerms) {
		Set<ExtraBedRules> extraBedRulesCG = null;
		if (CollectionUtils.isNotEmpty(extraBedTerms)){
			extraBedRulesCG = new HashSet<>();
			for (com.mmt.hotels.model.response.staticdata.ExtraBedRules extraBedTerm : extraBedTerms){
				ExtraBedRules extraBedRuleCG = new ExtraBedRules();
				extraBedRuleCG.setLabel(extraBedTerm.getLabel());
				extraBedRuleCG.setValue(extraBedTerm.getValue());
				extraBedRulesCG.add(extraBedRuleCG);
			}
		}
		return extraBedRulesCG;
	}

	private HostInfo buildHostInfo(Map<String, ArrayList<Type>> users, boolean chatWithHostEnabled) {
		HostInfo hostInfo = null;
		if (MapUtils.isNotEmpty(users)){
			List<Type> types = users.get("hotel");
			if (CollectionUtils.isNotEmpty(types)){
				Type type = types.get(0);
				hostInfo = new HostInfo();
				hostInfo.setAbout(type.getAbout());
				hostInfo.setEmail(type.getEmail());
				hostInfo.setHobbies(type.getHobbies());
				hostInfo.setHostImage(type.getPicture());
				hostInfo.setHostingExperienceTotal(type.getHostingExperienceTotal());
				hostInfo.setMobile(type.getMobile());
				hostInfo.setName(type.getName());
				hostInfo.setStarHostDescription(type.getStarHostDescription());
				hostInfo.setStarHostHeading(type.getStarHostHeading());
				hostInfo.setStarHostImageUrl(type.getStarHostImageUrlNew());
				hostInfo.setStarHostIcon(type.getStarHostImageIcon());
				hostInfo.setChatEnabled(type.isChatEnabled() && !chatWithHostEnabled);
				hostInfo.setStarHostReasons(type.getStarHostReasons());
				hostInfo.setTimeSinceHostingOnMmt(type.getTimeSinceHostingOnMmt());
				hostInfo.setResponseTime(type.getResponseTime());
				hostInfo.setResponseRate(type.getResponseRate());
				hostInfo.setLanguage(type.getLanguage());
				hostInfo.setEducation(type.getEducation());
				hostInfo.setGender(type.getGender());
				hostInfo.setPicture(type.getPicture());

			}
		}
		return hostInfo;
	}

	private Address getAddress(String addr1, String addr2) {
		Address address = new Address();
		address.setLine1(addr1);
		address.setLine2(addr2);
		return address;
	}

	private LocationDetail buildLocationDetail(String cityCode, String cityName, String cityCtyCode, String country) {
		LocationDetail locationDetail = new LocationDetail();
		locationDetail.setId(cityCode);
		locationDetail.setName(cityName);
		locationDetail.setType("city");
		locationDetail.setCountryId(cityCtyCode);
		locationDetail.setCountryName(country);
		return locationDetail;
	}

	private Map<String,RoomDetails> buildRoomInfoMap(HotelDetailWrapperResponse hotelDetailWrapperResponse) {
		if (hotelDetailWrapperResponse.getRoomInfoData()==null || MapUtils.isEmpty(hotelDetailWrapperResponse.getRoomInfoData().getRoomInfoMap()))
			return null;

		Map<String,RoomDetails> map = new HashMap<>();
		hotelDetailWrapperResponse.getRoomInfoData().getRoomInfoMap().forEach((key,value)->map.put(key,buildRoomInfo(value)));

		if (MapUtils.isEmpty(map)) {
			return null;
		} else if (hotelDetailWrapperResponse.getHotelImage()!=null && hotelDetailWrapperResponse.getHotelImage().getImageDetails()!=null
					&& hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional()!=null
					&& hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().containsKey("R")) {
			/* Map only Room images : parse professional (only Room, not Hotel) images
			*  and set them in the RoomDetails object for each matching room. */
			List<ProfessionalImageEntity> professionalImageEntityList = hotelDetailWrapperResponse.getHotelImage().getImageDetails().getProfessional().get("R");
			professionalImageEntityList.forEach(
					professionalImageEntity -> {
						String roomCode = professionalImageEntity.getCatCode();
						if (StringUtils.isNotBlank(roomCode) && map.containsKey(roomCode)) {
							if (CollectionUtils.isEmpty(map.get(roomCode).getImages())) {
								map.get(roomCode).setImages(new ArrayList<>());
							}
							map.get(roomCode).getImages().add(professionalImageEntity.getUrl().startsWith("http") ? professionalImageEntity.getUrl() : "https:" + professionalImageEntity.getUrl());
						}
					}
			);
		}
		return map;
	}

	private RoomDetails buildRoomInfo(RoomInfo roomInfo) {
		if (roomInfo == null) {
			return null;
		}
		RoomDetails roomDetails = new RoomDetails();
		roomDetails.setRoomCode(roomInfo.getRoomCode());
		roomDetails.setRoomName(roomInfo.getRoomName());
		roomDetails.setRoomSize(roomInfo.getRoomSize());
		roomDetails.setParentRoomCode(roomInfo.getParentRoomCode());
		roomDetails.setRoomViewName(roomInfo.getRoomViewName());
		roomDetails.setBeds(roomInfo.getBeds());
		roomDetails.setMaxAdult(roomInfo.getMaxAdultCount());
		roomDetails.setMaxGuest(roomInfo.getMaxGuestCount());
		roomDetails.setMaxChild(roomInfo.getMaxChildCount());
		roomDetails.setMaster(roomInfo.isMaster());
		if (StringUtils.isNotBlank(roomInfo.getBedRoomCount())) {
			roomDetails.setBedroomCount(NumberUtils.toInt(roomInfo.getBedRoomCount(),0));
		}
		roomDetails.setBedCount(roomInfo.getBedCount());
		roomDetails.setAmenities(commonResponseTransformer.buildAmenities(roomInfo.getFacilityWithGrp(), roomInfo.getStarFacilities(), roomInfo.getHighlightedFacilities(), false));
		roomDetails.setHighlightedAmenities(commonResponseTransformer.buildHighlightedAmenities(roomInfo.getFacilityHighlights()));
		return roomDetails;
	}

	private boolean isCorp(StaticDetailRequest staticDetailRequest) {
		if (staticDetailRequest!=null && staticDetailRequest.getRequestDetails()!=null
			&& StringUtils.isNotBlank(staticDetailRequest.getRequestDetails().getIdContext())) {
			return Constants.CORP_ID_CONTEXT.equalsIgnoreCase(staticDetailRequest.getRequestDetails().getIdContext());
		}
		return false;
	}

	public abstract StaffInfo convertStaffInfo(StaffInfo staffInfo);

	public void removeIcon(StaffInfo staffInfo){
		if(staffInfo == null){
			return;
		}
		removeIcon(staffInfo.getHost());
		removeIcon(staffInfo.getCook());
		removeIcon(staffInfo.getCaretaker());
	}

	private void removeIcon(Staff staff){
		if(staff == null){
			return;
		}
		for(StaffData staffData : staff.getData()){
			if (CollectionUtils.isNotEmpty(staffData.getGeneralInfo())) {
				staffData.getGeneralInfo().forEach(x -> x.setIconUrl(null));
			}
		}
	}

	public WishListedHotelsDetailResponse convertWishListedStaticDetailResponse(HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse, FlyfishReviewWrapperResponse flyfishReviewWrapperResponse,
																				String client, WishListedHotelsDetailRequest wishListedHotelsDetailRequest, CommonModifierResponse commonModifierResponse, UserReviewResponseForListing userReviewResponseForListing) {
		WishListedHotelsDetailResponse wishListedHotelsDetailResponse = new WishListedHotelsDetailResponse();
		long startTime = System.currentTimeMillis();
		try {
			if(hotStoreHotelsWrapperResponse != null && CollectionUtils.isNotEmpty(hotStoreHotelsWrapperResponse.getHotelResults())){
				String countryCode = wishListedHotelsDetailRequest.getSearchCriteria() != null ? wishListedHotelsDetailRequest.getSearchCriteria().getCountryCode() : Constants.DOM_COUNTRY;
				List<Hotel> hotels = new ArrayList<>();
				hotStoreHotelsWrapperResponse.getHotelResults()
						.forEach(hotelResult -> {
							Hotel hotel = new Hotel();
							hotel.setId(hotelResult.getId());
							hotel.setName(hotelResult.getName());
							hotel.setPropertyType(hotelResult.getPropertyType());
							hotel.setPropertyLabel(hotelResult.getPropertyLabel());
							hotel.setStayType(hotelResult.getStayType());
							hotel.setStarRating(hotelResult.getStarRating());
							hotel.setAlternateDates(hotelResult.isAlternateDatesAvailable());
							hotel.setIsAltAcco(hotelResult.isAltAcco());
							hotel.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelResult));
							hotel.setLocationPersuasion(hotelResult.getLocationPersuasion());
							List<String> images = getImagesForHotel(hotelResult.getId(), hotStoreHotelsWrapperResponse, wishListedHotelsDetailRequest);
							hotel.setMedia(commonResponseTransformer.buildMedia(images, hotelResult.getHotelVideos(), null, false));
							Map<OTA, JsonNode> reviewSummaryForHotel = getReviewSummaryForHotel(hotelResult.getId(), flyfishReviewWrapperResponse);
							com.mmt.hotels.model.response.flyfish.ReviewSummary pfmReviewSummary = null;
							if(userReviewResponseForListing != null && userReviewResponseForListing.getListingSummary() != null){
								pfmReviewSummary = userReviewResponseForListing.getListingSummary().get(hotelResult.getId());
							}
							if(pfmReviewSummary!=null){
								hotel.setReviewSummary(commonResponseTransformer.buildPfmReviewSummary(hotelResult.getCityCtyCode(), pfmReviewSummary));
							} else {
								hotel.setReviewSummary(commonResponseTransformer.buildReviewSummary(hotelResult.getCityCtyCode(), reviewSummaryForHotel, true));
							}
							hotel.setLocationDetail(buildLocationDetail(hotelResult.getCityCode(), hotelResult.getCityName(), hotelResult.getCityCtyCode(), hotelResult.getCountry()));
							hotel.setCategories(hotelResult.getCategories());
							hotel.setDetailDeeplinkUrl(hotelResult.getDetailDeeplinkUrl());
							hotel.setHeroImage(hotelResult.getHeroImage());
							hotel.setHeroVideoUrl(hotelResult.getHeroVideoUrl());
							hotel.setMmtHotelCategory(hotelResult.getMmtHotelCategory());
							hotel.setWishListed(hotelResult.isWishListed());
							hotels.add(hotel);
						});
				wishListedHotelsDetailResponse.setHotels(hotels);
			}
		}finally {
			metricAspect.addToTimeInternalProcess(Constants.PROCESS_DETAIL_RESPONSE_PROCESS, ControllerConstants.DETAIL_SEARCH_ROOMS, System.currentTimeMillis() - startTime);
		}

		return wishListedHotelsDetailResponse;
	}

	private List<String> getImagesForHotel(String hotelId, HotStoreHotelsWrapperResponse hotStoreHotelsWrapperResponse, WishListedHotelsDetailRequest wishListedHotelsDetailRequest) {
		List<String> images = new ArrayList<>();
		if (wishListedHotelsDetailRequest.getImageDetails() != null && CollectionUtils.isNotEmpty(wishListedHotelsDetailRequest.getImageDetails().getTypes())
				&& wishListedHotelsDetailRequest.getImageDetails().getTypes().contains(Constants.PROFESSIONAL)) {
			if (hotStoreHotelsWrapperResponse != null && MapUtils.isNotEmpty(hotStoreHotelsWrapperResponse.getHotelIdToHotelImageMap())) {
				if (hotStoreHotelsWrapperResponse.getHotelIdToHotelImageMap().containsKey(hotelId)) {
					ImageType imageDetails = hotStoreHotelsWrapperResponse.getHotelIdToHotelImageMap().get(hotelId).getImageDetails();
					if (imageDetails != null) {
						if (MapUtils.isNotEmpty(imageDetails.getProfessional())) {
							List<String> imageUrlList = new ArrayList<>();
							for (Map.Entry<String, List<ProfessionalImageEntity>> professionalImageMap : imageDetails.getProfessional().entrySet()) {
								List<ProfessionalImageEntity> professionalImageList = professionalImageMap.getValue();
								if (CollectionUtils.isNotEmpty(professionalImageList)) {
									for (ProfessionalImageEntity imageEntity : professionalImageList) {
										imageUrlList.add(imageEntity.getUrl());
									}
								}
							}
							int imagesCount = imageUrlList.size();
							if (wishListedHotelsDetailRequest.getImageDetails() != null && CollectionUtils.isNotEmpty(wishListedHotelsDetailRequest.getImageDetails().getCategories())) {
								Integer suppliedCount = wishListedHotelsDetailRequest.getImageDetails().getCategories().get(0).getCount();
								imagesCount = min(suppliedCount, imageUrlList.size());
							}
							imageUrlList = imageUrlList.subList(0, imagesCount);
							images.addAll(imageUrlList);
						}
					}
				}
			}
		}
		return images;
	}

	protected Map<OTA, JsonNode> getReviewSummaryForHotel(String hotelId, FlyfishReviewWrapperResponse flyfishReviewWrapperResponse) {
		Map<OTA, JsonNode> reviewSummary = null;
		if (StringUtils.isNotBlank(hotelId) && flyfishReviewWrapperResponse != null && flyfishReviewWrapperResponse.getFlyFishReviewSummary() != null
				&& MapUtils.isNotEmpty(flyfishReviewWrapperResponse.getFlyFishReviewSummary().getSummary())
				&& flyfishReviewWrapperResponse.getFlyFishReviewSummary().getSummary().containsKey(hotelId)) {
			reviewSummary = flyfishReviewWrapperResponse.getFlyFishReviewSummary().getSummary().get(hotelId);
		}
		return reviewSummary;
	}

	private CalendarCriteria buildCalendarCriteria(com.mmt.hotels.pojo.CalendarAvailability.CalendarCriteria calendarCriteriaHES) {
		if(calendarCriteriaHES == null)
			return null;
		CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
		calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getAdvanceDays());
		calendarCriteriaCG.setAvailable(calendarCriteriaHES.isAvailable());
		calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getMaxDate());
		calendarCriteriaCG.setMlos(calendarCriteriaHES.getMlos());
		return calendarCriteriaCG;
	}

	private void suppressFewHouseRules(HouseRulesV2 houseRulesV2) {
		if (null == houseRulesV2 || CollectionUtils.isEmpty(houseRulesV2.getAllRules())) return;
		List<CommonRules> resultCommonRulesList = houseRulesV2.getAllRules().stream().filter(rule -> !supressedHouseRulesList.contains(rule.getCategoryId()) && (null == rule.getId() || !rule.getId().equalsIgnoreCase(EXTRA_BED_POLICY_TO_BE_REMOVED))).collect(Collectors.toList());
		houseRulesV2.setAllRules(resultCommonRulesList);
	}

	private <T> void limitAmenitiesCount(List<T> highLightedAmenities, boolean serviceApartment, boolean isAltAcco) {
		if(CollectionUtils.isEmpty(highLightedAmenities)){
			return;
		}
		int total = highLightedAmenities.size();
		int amenitiesToShow = SIX;
		if (total <= amenitiesToShow) {
			return;
		} else {
			int extra = total - amenitiesToShow;
			if (extra < 0) {
				int q = total / amenitiesToShow;
				extra = total - (q * amenitiesToShow);
			}
			for (int i = 0; i < extra; i++) {
				highLightedAmenities.remove((highLightedAmenities.size() - 1));
			}
		}
	}

	private void sortTravellerTagsBasedImageTagsOrder(List<Tag> tags, List<String> imageTagsOrderList){

		if(CollectionUtils.isEmpty(imageTagsOrderList) || CollectionUtils.isEmpty(tags)){
			return;
		}
		tags.sort(new Comparator<Tag>() {
			@Override
			public int compare(Tag tag1, Tag tag2) {

				//product requirement keep the "others" tag at the last
				if(OTHERS.equalsIgnoreCase(tag1.getName())) {
					return 1;
				} else if(OTHERS.equalsIgnoreCase(tag2.getName())){
					return -1;
				}

				int index1 = imageTagsOrderList.indexOf(tag1.getName());
				int index2 = imageTagsOrderList.indexOf(tag2.getName());

				if(index2 == -1)
					return -1;
				else if(index1 == -1)
					return 1;
				else
					return Integer.compare(index1,index2);
			}
		});
	}

	private void modifyStreetViewInfo(StreetViewInfo streetViewInfo){
		if(Objects.nonNull(commonConfigConsul)){
			streetViewInfo.setIconurl(commonConfigConsul.getStreetViewIconUrl());
		}
		streetViewInfo.setTitle(polyglotService.getTranslatedData(STREET_VIEW_TITLE_TEXT));
		streetViewInfo.setSubTitle(polyglotService.getTranslatedData(STREET_VIEW_SUBTITLE_TEXT));
	}

}
