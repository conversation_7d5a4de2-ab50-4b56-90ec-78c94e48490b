package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.UserAssociatedTraveller;
import com.mmt.hotels.clientgateway.thirdparty.response.UserTravelDocument;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RestConnectorUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.AddOnDetailState;
import com.mmt.hotels.model.request.payment.TravelerDetail;
import com.mmt.hotels.model.response.emi.EmiDetailsRequest;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.ControllerConstants.REVIEW_AVAIL_ROOMS;

@Component
public class AvailRoomsRequestTransformer extends BaseRoomRequestTransformer{

    @Autowired
    CommonHelper commonHelper;

    @Autowired
    Utility utility;

    @Autowired
    private MetricAspect metricAspect;

    private static final Logger LOGGER = LoggerFactory.getLogger(AvailRoomsRequestTransformer.class);

    public FetchLocationsRequestBody convertFetchLocationsRequest(FetchLocationsRequest fetchLocationsRequest) {
        if (fetchLocationsRequest == null)
            return null;
        FetchLocationsRequestBody fetchLocationsRequestBody = new FetchLocationsRequestBody(); // fetch location hes request
        fetchLocationsRequestBody.setPostalCodes(fetchLocationsRequest.getPostalCodes());
        fetchLocationsRequestBody.setCorrelationKey(fetchLocationsRequest.getCorrelationKey());

        return fetchLocationsRequestBody;
    }

    public PriceByHotelsRequestBody convertAvailRoomsRequest(AvailRoomsRequest availRoomsRequest, CommonModifierResponse commonModifierResponse) {

        if (availRoomsRequest == null)
            return null;
        long startTime = System.currentTimeMillis();
        PriceByHotelsRequestBody availRequestCB = new PriceByHotelsRequestBody();
        try {
            super.buildDeviceDetails(availRequestCB, availRoomsRequest.getDeviceDetails());
            buildSearchCriteria(availRequestCB, availRoomsRequest.getSearchCriteria(), commonModifierResponse, availRoomsRequest.getExpDataMap());
            buildRequestDetails(availRequestCB, availRoomsRequest.getRequestDetails(), commonModifierResponse);
            availRequestCB.setResponseFilterFlags(super.buildResponseFilterFlags(availRequestCB, availRoomsRequest, commonModifierResponse));
            availRequestCB.setCorrelationKey(availRoomsRequest.getCorrelationKey());
            availRequestCB.setEmiDetails(buildEmiDetail(availRoomsRequest.getEmiDetail()));
            availRequestCB.setExperimentData(availRoomsRequest.getExpData());
            availRequestCB.setMobile(commonModifierResponse.getMobile());
            availRequestCB.setAffiliateId(commonModifierResponse.getAffiliateId());
            availRequestCB.setCdfContextId(commonModifierResponse.getCdfContextId());
            availRequestCB.setValidExpList(availRoomsRequest.getValidExpList());
            availRequestCB.setVariantKeys(availRoomsRequest.getExpVariantKeys());
            availRequestCB.setRequestIdentifier(utility.buildRequestIdentifier(availRoomsRequest.getRequestDetails()));
            if (commonModifierResponse.getExtendedUser() != null) {
                availRequestCB.setUuid(commonModifierResponse.getExtendedUser().getUuid());
                availRequestCB.setAgencyUUID(Utility.fetchAgencyUUIDFromCorp(commonModifierResponse.getExtendedUser().getCorporateData()));
                availRequestCB.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
                availRequestCB.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
                availRequestCB.setCorpUserID(commonModifierResponse.getExtendedUser().getProfileId());
                availRequestCB.setEmail(commonModifierResponse.getExtendedUser().getPrimaryEmailId());
                if(StringUtils.isNotEmpty(commonModifierResponse.getExtendedUser().getPrimaryEmailId())){
                    ScramblerClient sc = ScramblerClient.getInstance();
                    availRequestCB.setEmailCommId(sc.encode(commonModifierResponse.getExtendedUser().getPrimaryEmailId(), HashType.F));
                }
                String panNo = getUserPAN(commonModifierResponse.getExtendedUser());
                if (StringUtils.isNotEmpty(panNo)) {
                    availRequestCB.setPnAvlbl(true);
                }
            }
            if (null != availRoomsRequest.getRequestDetails() && Constants.CORP_ID_CONTEXT.equalsIgnoreCase(availRoomsRequest.getRequestDetails().getIdContext()) &&
                    CollectionUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getTravellerEmailID())) {
                List<TravelerDetail> travelerDetailsList = new ArrayList<>();
                for (String email : availRoomsRequest.getSearchCriteria().getTravellerEmailID()) {
                    TravelerDetail travelerDetail = new TravelerDetail();
                    try {
                        ScramblerClient sc = ScramblerClient.getInstance();
                        String commEmail = sc.encode(email, HashType.F);
                        travelerDetail.setEmailCommId(commEmail);
                        travelerDetailsList.add(travelerDetail);
                    } catch (ScramblerClientException e) {
                        //Do nothing
                    }
                }
                availRequestCB.setTravelerDetailsList(travelerDetailsList);
            }

            if (commonModifierResponse.getHydraResponse() != null) {
                availRequestCB.setHydraSegments(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
                availRequestCB.setFlightBooker(commonModifierResponse.getHydraResponse().isFlightBooker());
            }


            availRequestCB.setSiteDomain(availRoomsRequest.getRequestDetails().getSiteDomain());
            availRequestCB.setZcpHash(availRoomsRequest.getRequestDetails().getZcp());
            availRequestCB.setOldWorkflowId(availRoomsRequest.getRequestDetails().getOldWorkflowId());
            availRequestCB.setForwardBookingFlow(availRoomsRequest.getRequestDetails().isForwardBookingFlow());
            //setting roomInfoRequired to true for new review revamp API to store room info in txn data
            availRequestCB.setRoomInfoRequired(true);
            availRequestCB.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
            availRequestCB.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());
            if (availRoomsRequest.getSearchCriteria() != null && CollectionUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getRoomCriteria())) {
                availRequestCB.setRoomStayCandidates(buildRoomStayCandidates(availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates()));
                availRequestCB.setRatePlanType(availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRatePlanType());
            }
            if (availRoomsRequest.getSearchCriteria() != null && CollectionUtils.isNotEmpty(availRoomsRequest.getSearchCriteria().getRoomCriteria()) && utility.isDistributeRoomStayCandidates(availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates(), availRoomsRequest.getExpDataMap())) {
                availRequestCB.setRoomStayCandidates(utility.buildRoomStayDistribution(availRoomsRequest.getSearchCriteria().getRoomCriteria().get(0).getRoomStayCandidates(), availRoomsRequest.getExpDataMap()));
            }
            availRequestCB.setUserLocation(commonModifierResponse.getUserLocation());
            availRequestCB.setReviewPriceRequest(commonModifierResponse.isReviewPriceRequest());
            if (commonModifierResponse.getExtendedUser() != null) {
                availRequestCB.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
            }
            if (availRoomsRequest.getSearchCriteria() != null && availRoomsRequest.getSearchCriteria().getMultiCurrencyInfo() != null) {
                availRequestCB.setMultiCurrencyInfo(utility.buildMultiCurrencyInfoRequest(availRoomsRequest.getSearchCriteria().getMultiCurrencyInfo()));
            }
            if (availRoomsRequest.getSearchCriteria().getUserGlobalInfo() != null) {
                availRequestCB.setUserGlobalInfo(utility.buildUserGlobalInfoHES(availRoomsRequest.getSearchCriteria().getUserGlobalInfo()));
            }
            if (availRoomsRequest.getSearchCriteria() != null) {
                availRequestCB.setRscValueForDeepLink(utility.buildRscValueForReview(availRoomsRequest.getSearchCriteria().getRoomCriteria()));
            }
            if (availRoomsRequest.getFilterCriteria() != null) {
                availRequestCB.setAppliedFilterMap(buildAppliedFilterMap(availRoomsRequest.getFilterCriteria()));
            }
        } catch (ScramblerClientException e) {
            LOGGER.warn("Scrambler Exception while encoding emailid ", e);
        } finally {
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_REVIEW_REQUEST_PROCESS, REVIEW_AVAIL_ROOMS, System.currentTimeMillis() - startTime);
        }
        return availRequestCB;

    }

    private String getUserPAN(ExtendedUser extendedUser) {
		String panNo = null;
		List<UserTravelDocument> bookerTravelDocs = extendedUser.getTravelDocuments();
		panNo = findPanInDocs(bookerTravelDocs);
		if(null == panNo && !CollectionUtils.isEmpty(extendedUser.getAssociatedTravellers()) && extendedUser.getPersonalDetails() != null){
			String fname = extendedUser.getPersonalDetails().getName() != null && extendedUser.getPersonalDetails().getName().getFirstName()!=null ? extendedUser.getPersonalDetails().getName().getFirstName() : "";
			String lname = extendedUser.getPersonalDetails().getName() != null && extendedUser.getPersonalDetails().getName().getLastName()!=null ? extendedUser.getPersonalDetails().getName().getLastName() : "";
			for(UserAssociatedTraveller trvlr : extendedUser.getAssociatedTravellers()){
				String trvlFname = trvlr.getName() != null && StringUtils.isNotBlank(trvlr.getName().getFirstName()) ? trvlr.getName().getFirstName() : null;
				String trvlLname = trvlr.getName() != null && StringUtils.isNotBlank(trvlr.getName().getLastName()) ? trvlr.getName().getLastName() : null;
				if(fname.equalsIgnoreCase(trvlFname) && lname.equalsIgnoreCase(trvlLname)){
					List<UserTravelDocument> travelDocs = trvlr.getTravelDocuments();
					panNo = findPanInDocs(travelDocs);
					break;
				}
			}
		}
		return panNo;
	}

    private String findPanInDocs(List<UserTravelDocument> bookerTravelDocs) {
		String panNo = null;
		if(!CollectionUtils.isEmpty(bookerTravelDocs)){
			for(UserTravelDocument travelDocument : bookerTravelDocs){
				if(Constants.DOC_TYPE_PAN.equals(travelDocument.getDocType())
						&& StringUtils.isNotEmpty(travelDocument.getDocNumber())){
					panNo = travelDocument.getDocNumber();
					break;
				}
			}
		}
		return panNo;
	}

    private void buildSearchCriteria(PriceByHotelsRequestBody priceByHotelsRequestBody, AvailPriceCriteria searchCriteria, CommonModifierResponse commonModifierResponse, Map<String, String> expData) {

        List<String> hotelIds = new ArrayList<String>();
        hotelIds.add(searchCriteria.getHotelId());
        super.populateSearchCriteria(priceByHotelsRequestBody, searchCriteria, hotelIds, commonModifierResponse);
        priceByHotelsRequestBody.setPricingKey(searchCriteria.getPricingKey());
        priceByHotelsRequestBody.setRoomCriteria(buildRoomCriteria(searchCriteria.getRoomCriteria(), searchCriteria.getHotelId(), searchCriteria.getPricingKey(), expData));
        priceByHotelsRequestBody.setAddOnDetail(buildAddOnDetails(searchCriteria.getAddOnDetail()));
        priceByHotelsRequestBody.setExtraInfo(buildExtraInfo(searchCriteria.getSearchType()));
        priceByHotelsRequestBody.setMetaChannelInfo(searchCriteria.getMetaChannelInfo());
        priceByHotelsRequestBody.setUpgradeAvailable(searchCriteria.isCheckUpgrade());
        priceByHotelsRequestBody.setHotelType(searchCriteria.getHotelType());
        priceByHotelsRequestBody.setGuestHouseAvailable(searchCriteria.getGuestHouseAvailable());
        priceByHotelsRequestBody.setTripType(searchCriteria.getTripType());
        try {
            ScramblerClient scramblerClient = ScramblerClient.getInstance();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(searchCriteria.getTravellerEmailID())) {
                List<String> commEmails = new ArrayList<>();
                for (String email : searchCriteria.getTravellerEmailID()) {
                    String commEmail = scramblerClient.encode(email, HashType.F);
                    commEmails.add(commEmail);
                }
                priceByHotelsRequestBody.setTravellerEmailId(commEmails);
            }
        } catch (Exception ex) {
            LOGGER.error("Error while encoding email {}", ex.getMessage());
        }
    }

    private Map<String, AddOnDetailState> buildAddOnDetails(Map<String,com.mmt.hotels.clientgateway.request.AddOnDetailState> addOnDetail) {
        Map<String, AddOnDetailState> addOnDetailCB = null;
        if (MapUtils.isNotEmpty(addOnDetail)) {
            addOnDetailCB = new HashMap<>();
            for (Map.Entry<String, com.mmt.hotels.clientgateway.request.AddOnDetailState> entry : addOnDetail.entrySet()) {
                AddOnDetailState addOnDetailState = new AddOnDetailState();
                addOnDetailState.setAddOnType(entry.getValue().getAddOnType());
                addOnDetailState.setSelected(com.mmt.hotels.model.request.AddOnState.valueOf(entry.getValue().getSelected().name()));
                addOnDetailCB.put(entry.getKey(), addOnDetailState);
            }
        }
        return addOnDetailCB;
    }

    private ExtraInfo buildExtraInfo(String searchType) {
        if (searchType == null)
            return null;

        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setSearchType(searchType);
        return extraInfo;

    }

    private List<RoomCriterion> buildRoomCriteria(List<AvailRoomsSearchCriteria> roomCriteria, String hotelId, String pricingKey, Map<String, String> expData) {

        if (CollectionUtils.isNotEmpty(roomCriteria)){
            List<RoomCriterion> roomCriterionList = new ArrayList<>();

            for (AvailRoomsSearchCriteria roomCriteriaCG : roomCriteria){
                RoomCriterion roomCriterion = new RoomCriterion();
                roomCriterion.setHotelId(hotelId);
                roomCriterion.setMtKey(roomCriteriaCG.getMtKey());
                roomCriterion.setRecommendedType(roomCriteriaCG.getRecommendedType());
                roomCriterion.setPricingKey(StringUtils.isNotBlank(roomCriteriaCG.getPricingKey()) ? roomCriteriaCG.getPricingKey() : pricingKey);
                roomCriterion.setRatePlanCode(roomCriteriaCG.getRatePlanCode());
                roomCriterion.setLucky(roomCriteriaCG.isLucky());
                roomCriterion.setRoomCode(roomCriteriaCG.getRoomCode());
                roomCriterion.setRoomStayCandidates(buildRoomStayCandidates(roomCriteriaCG.getRoomStayCandidates()));
                if (utility.isDistributeRoomStayCandidates(roomCriteriaCG.getRoomStayCandidates(), expData)) {
                    roomCriterion.setRoomStayCandidates(utility.buildRoomStayDistribution(roomCriteriaCG.getRoomStayCandidates(), expData));
                }
                roomCriterionList.add(roomCriterion);
            }
            return roomCriterionList;
        }

        return null;

    }

    private List<com.mmt.hotels.model.request.RoomStayCandidate> buildRoomStayCandidates(List<RoomStayCandidate> roomStayCandidates) {


        if(roomStayCandidates==null)
            return null;

        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates){
            com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(RoomStayCandidate roomStayCandidateCG) {


        List<GuestCount> guestCounts = new ArrayList<>();
        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidateCG.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;

    }

    private void buildRequestDetails(PriceByHotelsRequestBody priceByHotelsRequestBody, RequestDetails requestDetails, CommonModifierResponse commonModifierResponse) {

        priceByHotelsRequestBody.setFunnelSource(requestDetails.getFunnelSource());
        priceByHotelsRequestBody.setIdContext(commonHelper.updateIdContext(requestDetails.getIdContext(), priceByHotelsRequestBody.getBookingDevice()));
        priceByHotelsRequestBody.setChannel(requestDetails.getChannel());
        priceByHotelsRequestBody.setPaymentChannel(requestDetails.getChannel());
        priceByHotelsRequestBody.setPageContext(requestDetails.getPageContext());
        priceByHotelsRequestBody.setVisitorId(requestDetails.getVisitorId());
        priceByHotelsRequestBody.setVisitNumber(requestDetails.getVisitNumber() != null ?
                String.valueOf(requestDetails.getVisitNumber()) : "");
        priceByHotelsRequestBody.setLoggedIn(requestDetails.isLoggedIn());
        priceByHotelsRequestBody.setNotifCoupon(requestDetails.getNotifCoupon());
        priceByHotelsRequestBody.setPayMode(requestDetails.getPayMode());
        if (null != requestDetails.getTrafficSource()) {
            priceByHotelsRequestBody.setTrafficSource(buildTrafficSource(requestDetails.getTrafficSource()));
        }
        priceByHotelsRequestBody.setPreApprovedValidity(requestDetails.getPreApprovedValidity());
        if (commonModifierResponse.getUserLocation() != null) {
            priceByHotelsRequestBody.setSrCty(commonModifierResponse.getUserLocation().getCity());
            priceByHotelsRequestBody.setSrcState(commonModifierResponse.getUserLocation().getState());
            priceByHotelsRequestBody.setSrCon(commonModifierResponse.getUserLocation().getCountry());
        }
        priceByHotelsRequestBody.setSrLat(requestDetails.getSrLat());
        priceByHotelsRequestBody.setSrLng(requestDetails.getSrLng());
        priceByHotelsRequestBody.setCouponCount(requestDetails.getCouponCount() != null ? requestDetails.getCouponCount() : 0);
        priceByHotelsRequestBody.setFirstTimeUserState(requestDetails.getFirstTimeUserState());
        priceByHotelsRequestBody.setWishCode(requestDetails.getWishCode());
        priceByHotelsRequestBody.setRequisitionID(requestDetails.getRequisitionID());
        priceByHotelsRequestBody.setMyBizFlowIdentifier(requestDetails.getMyBizFlowIdentifier());
        priceByHotelsRequestBody.setPreviousTxnKey(requestDetails.getPreviousTxnKey());
        priceByHotelsRequestBody.setUserConsent(requestDetails.isPromoConsent());
        setOtherDetails(priceByHotelsRequestBody);
        priceByHotelsRequestBody.setRequestIdentifier(utility.buildRequestIdentifier(requestDetails));
        priceByHotelsRequestBody.setReqContext(requestDetails.isPremium() ? Constants.PREMIUM : "");
    }

    private void setOtherDetails(PriceByHotelsRequestBody priceByHotelsRequestBody) {

        priceByHotelsRequestBody.setApplicationId("310");
        priceByHotelsRequestBody.setDomain("B2C");
        priceByHotelsRequestBody.setRequestType("B2CAgent");
        priceByHotelsRequestBody.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());

    }

    private GuestRecommendationEnabledReqBody buildGuestRecommendationEnabled() {
        GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
        guestRecommendationEnabledReqBody.setMaxRecommendations("1");
        guestRecommendationEnabledReqBody.setText("true");
        return guestRecommendationEnabledReqBody;
    }

    private com.mmt.hotels.model.request.TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {

        com.mmt.hotels.model.request.TrafficSource trafficSourceCB = new com.mmt.hotels.model.request.TrafficSource();
        trafficSourceCB.setSource(trafficSource.getSource());
        trafficSourceCB.setType(trafficSource.getType());
        // Pass aud field to downstream APIs
        if (trafficSource.getAud() != null) {
            trafficSourceCB.setAud(trafficSource.getAud());
        }
        return trafficSourceCB;

    }

    private EmiDetailsRequest buildEmiDetail(EMIDetail emiDetail) {

        if (emiDetail == null)
            return null;

        EmiDetailsRequest emiDetailCB = new EmiDetailsRequest();
        emiDetailCB.setBankId(emiDetail.getBankId());
        emiDetailCB.setBankName(emiDetail.getBankName());
        emiDetailCB.setPayOption(emiDetail.getPayOption());
        emiDetailCB.setTenure(emiDetail.getTenure());
        return emiDetailCB;

    }

}