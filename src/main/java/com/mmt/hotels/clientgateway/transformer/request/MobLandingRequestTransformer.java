package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.helpers.FilterHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.moblanding.CardInfo;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.TrafficSource;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.flyfish.FlyFishSummaryRequest;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.flyfish.SubConceptFilterDTO;
import com.mmt.hotels.model.request.flyfish.SummaryFilterCriteriaDTO;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.orchestrator.enums.SubPageContext;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.listing.personalization.SelectedTags;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.pojo.request.landing.RequiredApis;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

public abstract class MobLandingRequestTransformer  {

    @Autowired
    Utility utility;

    @Autowired
    FilterHelper filterHelper;

    private static final Logger logger = LoggerFactory.getLogger(MobLandingRequestTransformer.class);

    public HotelLandingMobRequestBody convertMobLandingRequest(
            MobLandingRequest mobLandingRequestGateway, CommonModifierResponse commonModifierResponse) {
        HotelLandingMobRequestBody hotelLandingMobRequestBody = new HotelLandingMobRequestBody();
        convertMobLandingRequest(hotelLandingMobRequestBody, mobLandingRequestGateway, commonModifierResponse);
        return hotelLandingMobRequestBody;
    }

    public void convertMobLandingRequest(
            HotelLandingMobRequestBody hotelLandingMobRequestBody, MobLandingRequest mobLandingRequestGateway, CommonModifierResponse commonModifierResponse) {

        hotelLandingMobRequestBody.setCountryCode(mobLandingRequestGateway.getSearchCriteria().getCountryCode());
        if (StringUtils.isNotEmpty(mobLandingRequestGateway.getSearchCriteria().getLocationId()))
            hotelLandingMobRequestBody.setCityCode(mobLandingRequestGateway.getSearchCriteria().getLocationId());
        else
            hotelLandingMobRequestBody.setCityCode(mobLandingRequestGateway.getSearchCriteria().getCityCode());

        if (Objects.nonNull(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates().get(0)) && (
                Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(mobLandingRequestGateway.getRequestDetails().getFunnelSource()) ||
                        Constants.FUNNEL_SOURCE_HOMESTAY_NEW.equalsIgnoreCase(mobLandingRequestGateway.getRequestDetails().getFunnelSource()))) {
            hotelLandingMobRequestBody.setBedRoomCount(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates().get(0).getRooms());
        }


        hotelLandingMobRequestBody.setRoomRecommendationToggle(utility.getRoomRecommndationToggleValue(mobLandingRequestGateway.getFilterCriteria()));
        hotelLandingMobRequestBody.setHotelSearchRequest(buildHotelsSearchRequest(mobLandingRequestGateway, commonModifierResponse));
        hotelLandingMobRequestBody.setRoomStayParams(buildRoomStayCandidates(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates()));
        if (mobLandingRequestGateway.getSearchCriteria() != null && utility.isDistributeRoomStayCandidates(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates(), mobLandingRequestGateway.getExpDataMap())) {
            hotelLandingMobRequestBody.setRoomStayParams(utility.buildRoomStayDistribution(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates(), mobLandingRequestGateway.getExpDataMap()));
        }
        if(mobLandingRequestGateway.getSearchCriteria().getUserSearchType()!=null){
            hotelLandingMobRequestBody.setUserSearchType(mobLandingRequestGateway.getSearchCriteria().getUserSearchType());
        }

        if (CollectionUtils.isNotEmpty(mobLandingRequestGateway.getFilterCriteria()))
            hotelLandingMobRequestBody.getHotelSearchRequest().setAdvancedFiltering(true);

        hotelLandingMobRequestBody.setDeviceId(mobLandingRequestGateway.getDeviceDetails().getDeviceId());

        hotelLandingMobRequestBody.setUuids(mobLandingRequestGateway.getUuids());

        buildGuidedSearchDetail(hotelLandingMobRequestBody, mobLandingRequestGateway.getGuidedSearchRequest());

        hotelLandingMobRequestBody.setTravelType(mobLandingRequestGateway.getTravellerType());

        /* Review */
        if (mobLandingRequestGateway.getReviewDetails() != null && CollectionUtils.isNotEmpty(mobLandingRequestGateway.getReviewDetails().getOtas())) {
            hotelLandingMobRequestBody.getHotelSearchRequest().setFlyfishSummaryRequest(new FlyFishSummaryRequest());
            hotelLandingMobRequestBody.getHotelSearchRequest().getFlyfishSummaryRequest().setFilter(new SummaryFilterCriteriaDTO());
            hotelLandingMobRequestBody.getHotelSearchRequest().getFlyfishSummaryRequest().getFilter().setOtas(new ArrayList<>());
            for (String ota : mobLandingRequestGateway.getReviewDetails().getOtas()) {
                hotelLandingMobRequestBody.getHotelSearchRequest().getFlyfishSummaryRequest().getFilter().getOtas().add(OTA.valueOf(ota));
            }

            hotelLandingMobRequestBody.getHotelSearchRequest().getFlyfishSummaryRequest().getFilter().setSubConcept(new SubConceptFilterDTO());
            hotelLandingMobRequestBody.getHotelSearchRequest().getFlyfishSummaryRequest().getFilter().getSubConcept().setTagTypes(mobLandingRequestGateway.getReviewDetails().getTagTypes());
        }

        if(commonModifierResponse.getHydraResponse() != null && CollectionUtils.isNotEmpty(commonModifierResponse.getHydraResponse().getHydraMatchedSegment())){
            hotelLandingMobRequestBody.getHotelSearchRequest().setUserSegments((commonModifierResponse.getHydraResponse().getHydraMatchedSegment().toArray(new String[0])));
        }
        /* Required Apis */
        boolean isChatbotRequest = mobLandingRequestGateway.getRequestDetails() != null && StringUtils.isNotEmpty(mobLandingRequestGateway.getRequestDetails().getMyraMsgId());
        hotelLandingMobRequestBody.setRequiredApis(buildRequiredApis(mobLandingRequestGateway.getRequiredApis(), isChatbotRequest));

        if(Objects.nonNull(mobLandingRequestGateway.getRequestDetails()) && StringUtils.isNotEmpty(mobLandingRequestGateway.getRequestDetails().getSubPageContext())){
            if(mobLandingRequestGateway.getRequestDetails().getSubPageContext().equalsIgnoreCase(SubPageContext.LISTING_COLLECTION.getSubPageContext())){
                hotelLandingMobRequestBody.getRequiredApis().setIsPersonalizationRequired(false);
            }
        }
        //Matchmaker show hotel left

        hotelLandingMobRequestBody.setSiteDomain(mobLandingRequestGateway.getRequestDetails().getSiteDomain());
        hotelLandingMobRequestBody.getHotelSearchRequest().setSiteDomain(mobLandingRequestGateway.getRequestDetails().getSiteDomain());
        hotelLandingMobRequestBody.getHotelSearchRequest().setDomain(commonModifierResponse.getDomain());

        hotelLandingMobRequestBody.getHotelSearchRequest().setUserLocation(commonModifierResponse.getUserLocation());

        if (StringUtils.isBlank(hotelLandingMobRequestBody.getCorrelationKey()) && StringUtils.isNotBlank(mobLandingRequestGateway.getCorrelationKey())) {
            hotelLandingMobRequestBody.setCorrelationKey(mobLandingRequestGateway.getCorrelationKey());
        }
        if (mobLandingRequestGateway.getSearchCriteria() != null && mobLandingRequestGateway.getSearchCriteria().getMultiCurrencyInfo() != null) {
            hotelLandingMobRequestBody.setMultiCurrencyInfo(utility.buildMultiCurrencyInfoRequest(mobLandingRequestGateway.getSearchCriteria().getMultiCurrencyInfo()));
        }
        if (mobLandingRequestGateway.getSearchCriteria().getUserGlobalInfo() != null) {
            hotelLandingMobRequestBody.setUserGlobalInfo(utility.buildUserGlobalInfoHES(mobLandingRequestGateway.getSearchCriteria().getUserGlobalInfo()));
        }

        if (commonModifierResponse.getExtendedUser() != null) {
            hotelLandingMobRequestBody.setBusinessIdentificationEnableFromUserService(utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser()));
        }
        if (mobLandingRequestGateway.getSearchCriteria() != null && CollectionUtils.isNotEmpty(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates())) {
            hotelLandingMobRequestBody.setRscValueForDeepLink(utility.buildRscValue(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates()));
        }
    }

    private void buildGuidedSearchDetail(HotelLandingMobRequestBody hotelLandingMobRequestBody,
			GuidedSearchRequest guidedSearchRequest) {
		if (guidedSearchRequest == null)
			return;
		hotelLandingMobRequestBody.setGsFlowIdentifier(guidedSearchRequest.getGsFlowIdentifier());
		if (CollectionUtils.isNotEmpty(guidedSearchRequest.getCurrentSelectedTags()) ||
			CollectionUtils.isNotEmpty(guidedSearchRequest.getPreviousSelectedTags())){
			SelectedTags selectedTags = new SelectedTags();
			selectedTags.setCurrentSelected(guidedSearchRequest.getCurrentSelectedTags());
			selectedTags.setPreviousSelected(guidedSearchRequest.getPreviousSelectedTags());
			selectedTags.setLevel(guidedSearchRequest.getLevel());
			hotelLandingMobRequestBody.setSelectedTags(selectedTags);
		}
	}

	private RequiredApis buildRequiredApis(com.mmt.hotels.clientgateway.request.RequiredApis requiredApis, boolean isChatbotRequest) {
        if (requiredApis == null)
            return null;

        RequiredApis requiredApi = new RequiredApis();
        requiredApi.setIsFilterSuggestionRequired(requiredApis.isFilterSuggestionRequired());
        requiredApi.setIsMetaRequired(requiredApis.isMetaRequired());
        requiredApi.setIsMMRRequired(requiredApis.isMmrRequired());
        requiredApi.setMMRV2Required(requiredApi.isMMRV2Required());
        requiredApi.setIsPersonalizationRequired((!isChatbotRequest && requiredApis.isPersonalizationRequired()) || requiredApis.isCardRequired());

        return requiredApi;
    }

    private List<RoomStayCandidate> buildRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        if (roomStayCandidates == null)
            return null;

        List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();

        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG : roomStayCandidates){
            RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
            roomStayCandidate.setGuestCounts(buildGuestCounts(roomStayCandidateCG));
            roomStayCandidateList.add(roomStayCandidate);
        }

        return roomStayCandidateList;
    }

    private List<GuestCount> buildGuestCounts(com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidateCG) {
        if (roomStayCandidateCG == null)
            return null;

        List<GuestCount> guestCounts = new ArrayList<>();

        GuestCount guestCount = new GuestCount();
        guestCount.setAgeQualifyingCode("10");
        guestCount.setAges(roomStayCandidateCG.getChildAges());
        guestCount.setCount(String.valueOf(roomStayCandidateCG.getAdultCount()));
        guestCounts.add(guestCount);
        return guestCounts;
    }

    private SearchWrapperInputRequest buildHotelsSearchRequest(MobLandingRequest mobLandingRequestGateway, CommonModifierResponse commonModifierResponse) {
        if (mobLandingRequestGateway==null)
            return null;

        SearchWrapperInputRequest searchWrapperInputRequest = new SearchWrapperInputRequest();
        if(mobLandingRequestGateway.getRequestDetails() != null && mobLandingRequestGateway.getFilterCriteria() != null && Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(mobLandingRequestGateway.getRequestDetails().getFunnelSource())){
            Optional<Filter> bedRoomCountFilter = mobLandingRequestGateway.getFilterCriteria().stream().filter(f -> com.mmt.hotels.clientgateway.response.filter.FilterGroup.BEDROOM_COUNT.name().equalsIgnoreCase(f.getFilterGroup().name())).findFirst();
            if(bedRoomCountFilter.isPresent()){
                utility.modifyRoomStayCandidateRequestForHomestayFunnel(bedRoomCountFilter.get(),mobLandingRequestGateway.getSearchCriteria());
            }
        }
        searchWrapperInputRequest.setGuestRecommendationEnabled(buildGuestRecommendationEnabled());
        searchWrapperInputRequest.setCheckin(mobLandingRequestGateway.getSearchCriteria().getCheckIn());
        searchWrapperInputRequest.setCheckout(mobLandingRequestGateway.getSearchCriteria().getCheckOut());
        searchWrapperInputRequest.setCurrency(mobLandingRequestGateway.getSearchCriteria().getCurrency());
        searchWrapperInputRequest.setCityCode(mobLandingRequestGateway.getSearchCriteria().getCityCode());
        searchWrapperInputRequest.setCountryCode(mobLandingRequestGateway.getSearchCriteria().getCountryCode());
        searchWrapperInputRequest.setLocationId(mobLandingRequestGateway.getSearchCriteria().getLocationId());
        searchWrapperInputRequest.setLocationType(mobLandingRequestGateway.getSearchCriteria().getLocationType());
        searchWrapperInputRequest.setLatitude(mobLandingRequestGateway.getSearchCriteria().getLat());
        searchWrapperInputRequest.setLongitude(mobLandingRequestGateway.getSearchCriteria().getLng());
        searchWrapperInputRequest.setAppVersion(mobLandingRequestGateway.getDeviceDetails().getAppVersion());
        searchWrapperInputRequest.setBookingDevice(mobLandingRequestGateway.getDeviceDetails().getBookingDevice());
        searchWrapperInputRequest.setDeviceType(mobLandingRequestGateway.getDeviceDetails().getDeviceType());
        searchWrapperInputRequest.setDeviceId(mobLandingRequestGateway.getDeviceDetails().getDeviceId());
        searchWrapperInputRequest.setDeviceName(mobLandingRequestGateway.getDeviceDetails().getDeviceName());
        searchWrapperInputRequest.setNetworkType(mobLandingRequestGateway.getDeviceDetails().getNetworkType());
        searchWrapperInputRequest.setExperimentData(mobLandingRequestGateway.getExpData());
        searchWrapperInputRequest.setMmtAuth(commonModifierResponse.getMmtAuth());
        searchWrapperInputRequest.setNoOfPersuasions(mobLandingRequestGateway.getFeatureFlags().getNoOfPersuasions());
        searchWrapperInputRequest.setNumberOfCoupons(mobLandingRequestGateway.getFeatureFlags().getNoOfCoupons());
        searchWrapperInputRequest.setSortCriteria(buildSortCriteria(mobLandingRequestGateway.getSortCriteria()));
        searchWrapperInputRequest.setFunnelSource(mobLandingRequestGateway.getRequestDetails().getFunnelSource());
        searchWrapperInputRequest.setReqContext(mobLandingRequestGateway.getRequestDetails().isPremium() ? Constants.PREMIUM : Constants.DEFAULT);
        searchWrapperInputRequest.setVisitNumber(String.valueOf(mobLandingRequestGateway.getRequestDetails().getVisitNumber()));
        searchWrapperInputRequest.setNotifCoupon(mobLandingRequestGateway.getRequestDetails().getNotifCoupon());
        searchWrapperInputRequest.setIdContext(mobLandingRequestGateway.getRequestDetails().getIdContext());
        searchWrapperInputRequest.setSrLng(mobLandingRequestGateway.getRequestDetails().getSrLng());
        searchWrapperInputRequest.setSrLat(mobLandingRequestGateway.getRequestDetails().getSrLat());
        if (commonModifierResponse.getUserLocation() != null) {
            searchWrapperInputRequest.setSrCty(commonModifierResponse.getUserLocation().getCity());
            searchWrapperInputRequest.setSrState(commonModifierResponse.getUserLocation().getState());
            searchWrapperInputRequest.setSrCon(commonModifierResponse.getUserLocation().getCountry());
        }
        searchWrapperInputRequest.setVisitorId(mobLandingRequestGateway.getRequestDetails().getVisitorId());
        if (commonModifierResponse.getExtendedUser() != null) {
            if (commonModifierResponse.getExtendedUser().getPersonalDetails() != null && null != commonModifierResponse.getExtendedUser().getPersonalDetails().getName())
                searchWrapperInputRequest.setFirstName(commonModifierResponse.getExtendedUser().getPersonalDetails().getName().getFirstName());
            searchWrapperInputRequest.setUUID(commonModifierResponse.getExtendedUser().getUuid());
            searchWrapperInputRequest.setAgencyUUID(Utility.fetchAgencyUUIDFromCorp(commonModifierResponse.getExtendedUser().getCorporateData()));
            searchWrapperInputRequest.setProfileType(commonModifierResponse.getExtendedUser().getProfileType());
            searchWrapperInputRequest.setSubProfileType(commonModifierResponse.getExtendedUser().getAffiliateId());
        }
        searchWrapperInputRequest.setLoggedIn(mobLandingRequestGateway.getRequestDetails().isLoggedIn());
        searchWrapperInputRequest.setTrafficSource(buildTrafficSource(mobLandingRequestGateway.getRequestDetails().getTrafficSource()));
        searchWrapperInputRequest.setLimit(mobLandingRequestGateway.getSearchCriteria().getLimit());
        searchWrapperInputRequest.setMatchMakerRequest(mobLandingRequestGateway.getMatchMakerDetails());
        if(mobLandingRequestGateway.getFeatureFlags() != null ) {
            searchWrapperInputRequest.setNumberOfAddons(mobLandingRequestGateway.getFeatureFlags().getNoOfAddons());
            searchWrapperInputRequest.setNumberOfSoldOuts(mobLandingRequestGateway.getFeatureFlags().getNoOfSoldouts());
            searchWrapperInputRequest.setNumberOfCoupons(mobLandingRequestGateway.getFeatureFlags().getNoOfCoupons());
            searchWrapperInputRequest.setResponseFilterFlags(new ResponseFilterFlags());
            searchWrapperInputRequest.getResponseFilterFlags().setAddOnRequired(mobLandingRequestGateway.getFeatureFlags().isAddOnRequired());
            searchWrapperInputRequest.getResponseFilterFlags().setStaticData(mobLandingRequestGateway.getFeatureFlags().isStaticData());
            searchWrapperInputRequest.getResponseFilterFlags().setApplyAbsorption(mobLandingRequestGateway.getFeatureFlags().isApplyAbsorption());
            searchWrapperInputRequest.getResponseFilterFlags().setPriceInfoReq(true);
            searchWrapperInputRequest.getResponseFilterFlags().setNewCorp(Constants.CORP_ID_CONTEXT.equalsIgnoreCase(searchWrapperInputRequest.getIdContext()));
            searchWrapperInputRequest.getResponseFilterFlags().setBestCoupon(mobLandingRequestGateway.getFeatureFlags().isCoupon());
            searchWrapperInputRequest.getResponseFilterFlags().setWalletRequired(mobLandingRequestGateway.getFeatureFlags().isWalletRequired());
            searchWrapperInputRequest.getResponseFilterFlags().setFlyfishSummaryRequired(mobLandingRequestGateway.getFeatureFlags().isReviewSummaryRequired());
            searchWrapperInputRequest.getResponseFilterFlags().setShortlistRequired(mobLandingRequestGateway.getFeatureFlags().isShortlistingRequired());
            searchWrapperInputRequest.getResponseFilterFlags().setCheckAvailibility(mobLandingRequestGateway.getFeatureFlags().isCheckAvailability());
            //searchWrapperInputRequest.getResponseFilterFlags().setDealOfTheDayRequired(mobLandingRequestGateway.getFeatureFlags().isDealOfTheDayRequired());
            //Removing flag dependency fix for SWAT-4039659
            searchWrapperInputRequest.getResponseFilterFlags().setDealOfTheDayRequired(true);
            searchWrapperInputRequest.getResponseFilterFlags().setRoomLevelDetails(mobLandingRequestGateway.getFeatureFlags().isRoomInfoRequired());
            searchWrapperInputRequest.getResponseFilterFlags().setCorporateMMRLocationsRequired(mobLandingRequestGateway.getFeatureFlags().isCorpMMRRequired());
            searchWrapperInputRequest.getResponseFilterFlags().setExtraAltAccoPropertiesRequired(mobLandingRequestGateway.getFeatureFlags().isExtraAltAccoRequired());
            searchWrapperInputRequest.getResponseFilterFlags().setUnmodifiedAmenities(mobLandingRequestGateway.getFeatureFlags().isUnmodifiedAmenities());
            searchWrapperInputRequest.getResponseFilterFlags().setPoisRequiredOnMap(mobLandingRequestGateway.getFeatureFlags().isPoisRequiredOnMap());
            searchWrapperInputRequest.getResponseFilterFlags().setLimitedFilterCall(true);
            searchWrapperInputRequest.getResponseFilterFlags().setFlashDealClaimed(mobLandingRequestGateway.getFeatureFlags().isFlashDealClaimed());
            searchWrapperInputRequest.getResponseFilterFlags().setCityTaxExclusive(commonModifierResponse.isCityTaxExclusive());
            searchWrapperInputRequest.getResponseFilterFlags().setShowRushDealsBottomSheet(mobLandingRequestGateway.getFeatureFlags().isShowRushDealsBottomSheet());
        }

        searchWrapperInputRequest.setRoomStayCandidates(buildRoomStayCandidates(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates()));
        if (mobLandingRequestGateway.getSearchCriteria() != null && utility.isDistributeRoomStayCandidates(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates(), mobLandingRequestGateway.getExpDataMap())) {
            searchWrapperInputRequest.setRoomStayCandidates(utility.buildRoomStayDistribution(mobLandingRequestGateway.getSearchCriteria().getRoomStayCandidates(), mobLandingRequestGateway.getExpDataMap()));
        }

        searchWrapperInputRequest.setCancellationPolicyRulesReq("yes");
        searchWrapperInputRequest.setRequestType("B2CAgent");
        searchWrapperInputRequest.setChannel(mobLandingRequestGateway.getRequestDetails().getChannel());
        searchWrapperInputRequest.setPageContext(mobLandingRequestGateway.getRequestDetails().getPageContext());

        if(mobLandingRequestGateway.getImageDetails() != null) {
            searchWrapperInputRequest.setImageCategory(buildImageCategories(mobLandingRequestGateway.getImageDetails().getCategories()));
            searchWrapperInputRequest.setImageType(mobLandingRequestGateway.getImageDetails().getTypes());
        }
        searchWrapperInputRequest.setApplicationId(String.valueOf(commonModifierResponse.getApplicationId()));
        searchWrapperInputRequest.setCollectionRequired(true);
        // On the basis of the locationId/cityCode we are adding preAppliedFilter in the filterCriteria in the client request
        List<Filter> preAppliedFiltersForLocationId = utility.getPreAppliedFiltersForLocationId(mobLandingRequestGateway.getSearchCriteria(), commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : null);
        if (CollectionUtils.isNotEmpty(preAppliedFiltersForLocationId)) {
            if (CollectionUtils.isEmpty(mobLandingRequestGateway.getFilterCriteria())) {
                mobLandingRequestGateway.setFilterCriteria(new ArrayList<>());
            }
            if (CollectionUtils.isNotEmpty(preAppliedFiltersForLocationId)) {
                mobLandingRequestGateway.getFilterCriteria().addAll(preAppliedFiltersForLocationId);
            }
        }
        if(CollectionUtils.isNotEmpty(mobLandingRequestGateway.getFilterCriteria())) {
            Set<String> dptInlineAppliedCategories = new HashSet<>();
            searchWrapperInputRequest.setAppliedFilterMap(buildAppliedFilterMap(mobLandingRequestGateway.getFilterCriteria(), mobLandingRequestGateway.getMatchMakerDetails(), dptInlineAppliedCategories));
            searchWrapperInputRequest.setDptInlineAppliedCollections(dptInlineAppliedCategories);
        }
        if(MapUtils.isNotEmpty(commonModifierResponse.getManthanExpDataMap())){
            searchWrapperInputRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        }
        searchWrapperInputRequest.setAppliedBatchKeys(buildAppliedBatchKeys(mobLandingRequestGateway.getAppliedBatchKeys()));
        searchWrapperInputRequest.setRoomRecommendationToggle(utility.getRoomRecommndationToggleValue(mobLandingRequestGateway.getFilterCriteria()));
        searchWrapperInputRequest.setRoomPreferenceEnabled(utility.checkIfFilterValueExistsInAppliedFilterMap(mobLandingRequestGateway.getFilterCriteria()));
        searchWrapperInputRequest.setExperimentData(utility.updateExpDataForBedRoomCountFilter(mobLandingRequestGateway.getFilterCriteria(), searchWrapperInputRequest.getExperimentData()));
        searchWrapperInputRequest.setExcludeCards(buildExcludeCards(mobLandingRequestGateway.getRequestDetails().getExcludeCards()));
        searchWrapperInputRequest.setCardIds(buildCardIds(mobLandingRequestGateway.getCardIds()));
        return searchWrapperInputRequest;
    }

    private List<CardData> buildExcludeCards(List<CardInfo> excludeCards) {
        if (CollectionUtils.isEmpty(excludeCards)) {
            return null;
        }
        
        List<CardData> excludeCardDataList = new ArrayList<>();
        for (CardInfo cardInfo : excludeCards) {
            CardData cardData = new CardData();
            cardData.setCardId(cardInfo.getId());
            cardData.setCardSubType(cardInfo.getSubType());
            excludeCardDataList.add(cardData);
        }
        
        return excludeCardDataList;
    }

    private List<CardData> buildCardIds(List<String> cardIds){
        if(CollectionUtils.isEmpty(cardIds)){
            return null;
        }

        List<CardData> hesCardIds = new ArrayList<>();
        for(String cardId: cardIds){
            CardData cardData = new CardData();
            cardData.setCardId(cardId);
            hesCardIds.add(cardData);
        }

        return hesCardIds;
    }

    private List<com.mmt.hotels.filter.BatchKey> buildAppliedBatchKeys(List<BatchKey> appliedBatchKeys) {
        if(CollectionUtils.isEmpty(appliedBatchKeys)){
            return null;
        }
        List<com.mmt.hotels.filter.BatchKey> appliedBatchKeysRequest = new ArrayList<>();
        appliedBatchKeys.forEach(appliedBatchKey -> appliedBatchKeysRequest.add(createBatchKey(appliedBatchKey)));
        return appliedBatchKeysRequest;
    }

    private com.mmt.hotels.filter.BatchKey createBatchKey(BatchKey appliedBatchKey) {
        com.mmt.hotels.filter.BatchKey appliedBatch = new com.mmt.hotels.filter.BatchKey();
        appliedBatch.setStaticBatch(appliedBatchKey.getStaticBatch());
        appliedBatch.setFilterValue(appliedBatchKey.getFilterValue());
        return appliedBatch;
    }

    private Map<FilterGroup, Set<com.mmt.hotels.filter.Filter>> buildAppliedFilterMap(List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria, MatchMakerRequest matchMakerDetails, Set<String> dptInlineAppliedCategories) {

        if (CollectionUtils.isNotEmpty(filterCriteria)) {
            Map<FilterGroup, Set<com.mmt.hotels.filter.Filter>> appliefFilterMapCB = new HashMap<>();
            for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
                if(filterCG.getFilterGroup() != null) {
                    if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCG.getFilterGroup().name()) || (BEDROOM_COUNT.equalsIgnoreCase(filterCG.getFilterGroup().name()))) {
                        continue;
                    }
                }
                com.mmt.hotels.filter.Filter filterCB = new com.mmt.hotels.filter.Filter();
                com.mmt.hotels.filter.FilterGroup filterGroup = com.mmt.hotels.filter.FilterGroup.
                        getFilterGroupFromFilterName(filterCG.getFilterGroup().name());
                filterCB.setFilterGroup(filterGroup);
                filterCB.setFilterRange(buildFilterRange(filterCG.getFilterRange()));
                filterCB.setFilterValue(filterCG.getFilterValue());
                filterCB.setRangeFilter(filterCG.isRangeFilter());

                if (appliefFilterMapCB.get(filterGroup) == null) {
                    Set<com.mmt.hotels.filter.Filter> filterSet = new HashSet<>();
                    filterSet.add(filterCB);
                    appliefFilterMapCB.put(filterGroup, filterSet);

                } else {
                    Set<com.mmt.hotels.filter.Filter> filterSet = appliefFilterMapCB.get(filterGroup);
                    filterSet.add(filterCB);
                    appliefFilterMapCB.put(filterGroup, filterSet);
                }
            }
            filterHelper.updateAppliedFilterMapDptCollections(appliefFilterMapCB, matchMakerDetails, dptInlineAppliedCategories);
            return appliefFilterMapCB;
        }
        return null;
    }

    private com.mmt.hotels.filter.FilterRange buildFilterRange(FilterRange filterRange) {
        if (filterRange == null)
            return null;

        com.mmt.hotels.filter.FilterRange filterRangeCB = new com.mmt.hotels.filter.FilterRange();
        filterRangeCB.setMaxValue(filterRange.getMaxValue());
        filterRangeCB.setMinValue(filterRange.getMinValue());

        return filterRangeCB;
    }

    private List<ImageCategoryEntityBO> buildImageCategories(List<ImageCategory> imageCategories) {
        if (imageCategories == null)
            return null;

        List<ImageCategoryEntityBO> imageCategoryEntityBOList = new ArrayList<>();

        for (ImageCategory imageCategory: imageCategories){

        	ImageCategoryEntityBO imageCategoryCB = new ImageCategoryEntityBO();
            imageCategoryCB.setWidth(imageCategory.getWidth());
            imageCategoryCB.setHeight(imageCategory.getHeight());
            imageCategoryCB.setCount(imageCategory.getCount());
            imageCategoryCB.setCategory(imageCategory.getType());
            imageCategoryCB.setOutputFormat(imageCategory.getImageFormat());
            imageCategoryEntityBOList.add(imageCategoryCB);
        }

        return imageCategoryEntityBOList;
    }

    private TrafficSource buildTrafficSource(com.mmt.hotels.clientgateway.request.TrafficSource trafficSource) {
        if (trafficSource == null)
            return null;

        TrafficSource trafficSourceCB  = new TrafficSource();
        trafficSourceCB.setType(trafficSource.getType());
        trafficSourceCB.setSource(trafficSource.getSource());
        // Pass aud field to downstream APIs
        if (trafficSource.getAud() != null) {
            trafficSourceCB.setAud(trafficSource.getAud());
        }

        return trafficSourceCB;
    }

    private GuestRecommendationEnabledReqBody buildGuestRecommendationEnabled() {
        GuestRecommendationEnabledReqBody guestRecommendationEnabledReqBody = new GuestRecommendationEnabledReqBody();
        guestRecommendationEnabledReqBody.setMaxRecommendations("1");
        guestRecommendationEnabledReqBody.setText("true");
        return guestRecommendationEnabledReqBody;
    }

    public com.mmt.hotels.model.request.SortCriteria buildSortCriteria(com.mmt.hotels.clientgateway.request.SortCriteria sortCriteria) {
        if (sortCriteria == null)
            return null;
        com.mmt.hotels.model.request.SortCriteria sortCriteriaCB = new com.mmt.hotels.model.request.SortCriteria();
        sortCriteriaCB.setOrder(sortCriteria.getOrder());
        sortCriteriaCB.setField(sortCriteria.getField());
        return sortCriteriaCB;
    }
}
