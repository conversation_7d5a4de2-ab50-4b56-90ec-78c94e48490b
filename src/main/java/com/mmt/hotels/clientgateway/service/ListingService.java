package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.CBError;
import com.mmt.hotels.clientgateway.enums.DependencyLayer;
import com.mmt.hotels.clientgateway.enums.ErrorType;
import com.mmt.hotels.clientgateway.enums.MarshallingErrors;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.FetchCollectionHelper;
import com.mmt.hotels.clientgateway.helpers.ListingHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.CityOverviewRequest;
import com.mmt.hotels.clientgateway.request.FetchCollectionRequest;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.GroupBookingRequest;
import com.mmt.hotels.clientgateway.request.ListingMapRequest;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.MobLandingRequest;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchHotelsRequest;
import com.mmt.hotels.clientgateway.request.TravelTipRequest;
import com.mmt.hotels.clientgateway.request.filter.EvaluateFilterRankOrderRequest;
import com.mmt.hotels.clientgateway.response.GroupBookingResponse;
import com.mmt.hotels.clientgateway.response.TravelTipResponse;
import com.mmt.hotels.clientgateway.response.TravelTipWrapperResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterCategory;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listing.UpsellRateplanResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.moblanding.MatchMakerResponseCG;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.restexecutors.FilterExecutor;
import com.mmt.hotels.clientgateway.restexecutors.ListingMapExecutor;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchHotelsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.TravelTripExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.FilterFactory;
import com.mmt.hotels.clientgateway.transformer.factory.FilterPillFactory;
import com.mmt.hotels.clientgateway.transformer.factory.ListingMapFactory;
import com.mmt.hotels.clientgateway.transformer.factory.MobLandingFactory;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.factory.TravelTipFactory;
import com.mmt.hotels.clientgateway.transformer.request.FilterRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.cardEngine.CardEngineResponseTransformerFactory;
import com.mmt.hotels.clientgateway.util.ClientBackendUtility;
import com.mmt.hotels.clientgateway.util.CrossSellUtil;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.filter.DPTExperimentDetails;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterShownDetails;
import com.mmt.hotels.kafka.JsonKafkaProducer;
import com.mmt.hotels.model.request.CityOverviewHesRequest;
import com.mmt.hotels.model.request.FilterCountLoggingRequest;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.athena.Card;
import com.mmt.hotels.model.response.athena.TrendingNowCard;
import com.mmt.hotels.model.response.athena.TrendingNowData;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.searchwrapper.CardCollections;
import com.mmt.hotels.model.response.searchwrapper.CollectionsResponseBo;
import com.mmt.hotels.model.response.searchwrapper.Cta;
import com.mmt.hotels.model.response.searchwrapper.FeaturedCollections;
import com.mmt.hotels.model.response.searchwrapper.ListingPagePersonalizationResponsBO;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntityAbridged;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperResponseBO;
import com.mmt.hotels.orchestrator.enums.SubPageContext;
import com.mmt.hotels.pojo.listing.personalization.CardAction;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.matchmaker.WikiResponse;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import com.mmt.hotels.pojo.response.landing.HotelLandingWrapperResponse;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.codehaus.plexus.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.CLIENT_DESKTOP;
import static com.mmt.hotels.clientgateway.constants.Constants.CallToBook;
import static com.mmt.hotels.clientgateway.constants.Constants.EMPTY_STRING;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_RECOMMENDATION;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_VALUE;
import static com.mmt.hotels.clientgateway.constants.Constants.FLEXIBLE_ROOM_VALUE;
import static com.mmt.hotels.clientgateway.constants.Constants.HighValue;
import static com.mmt.hotels.clientgateway.constants.Constants.ListAllProp;
import static com.mmt.hotels.clientgateway.constants.Constants.MULTI_ROOM_EXP;
import static com.mmt.hotels.clientgateway.constants.Constants.NULL_CITY;
import static com.mmt.hotels.clientgateway.constants.Constants.RULE_BASED_FILTER_SETTINGS_ENABLED;
import static com.mmt.hotels.clientgateway.constants.Constants.VERTICAL;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXACT_ROOM_LANDING_TEXT;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_MULTI;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_SINGLE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FLEXIBLE_ROOMS_FILTER_VALUE;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.FLEXIBLE_ROOM_LANDING_TEXT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.FETCH_UPSELL_RATEPLAN;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_FILTER_COUNT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;


@Component
public class ListingService {

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Autowired
    CommonConfigConsul commonConfigConsul;
    @Autowired
    private MobLandingFactory mobLandingFactory;

    @Autowired
    private MobLandingExecutor mobLandingExecutor;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private ListingHelper listingHelper;

    @Autowired
    private SearchHotelsExecutor searchHotelsExecutor;

    @Autowired
    private FilterExecutor filterExecutor;

    @Autowired
    private FilterFactory filterFactory;

    @Autowired
    private ListingMapFactory listingMapFactory;

    @Autowired
    private ListingMapExecutor listingMapExecutor;

    @Autowired
    private MobConfigHelper mobConfigHelper;

    @Autowired
    private OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private HotelMetaDataService hotelMetaDataService;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private FilterResponseTransformer filterResponseTransformer;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    CrossSellUtil crossSellUtil;

    @Autowired
    private Utility utility;

    @Autowired
    private FetchCollectionHelper fetchCollectionHelper;

    @Autowired
    private PropertyManager propManager;

    @Autowired
    private FilterPillFactory filterPillFactory;

    private Map<String, FilterDetail> landingFilterConditions;
    
    @Autowired
    @Qualifier("pdtLoggingThreadPool")
    private ThreadPoolTaskExecutor pdtLoggingThreadPool;

//    @Autowired
//    @Qualifier("jsonKafkaProducer")
    JsonKafkaProducer<GroupBookingRequest> jsonKafkaProducer;

    @Value("${group.booking.kafka.topic:groupBookingTopic}")
    private String groupBookingKafkaTopic;

    @Value("${group.booking.success.msg:Your request has been submitted successfully}")
    private String groupBookingSuccessMsg;

    @Value("${filter.title.purpose.stay}")
    private String purposeStayFilterTitle;

    @Value("${rule.based.filter.settings}")
    private boolean ruleBasedFilterSettings = false;

    private Map<String, String> purposeStayFilterTitleMap;

    @Autowired
    private RequestHandler requestHandler;

    @Autowired
    @Qualifier("listingThreadPool")
    private ThreadPoolTaskExecutor listingThreadPool;

    @Autowired
    @Qualifier("kafkaThreadPool")
    private ThreadPoolTaskExecutor kafkaThreadPool;

    @Autowired
    private CardEngineService cardEngineService;

    @Value("${request.callback.count}")
    private int requestCallbackCountProp;

    @Value("${request.callback.green.icon}")
    private String requestCallbackGreenIcon;

    @Autowired
    private TravelTripExecutor travelTipExecutor;
    private static final Logger logger = LoggerFactory.getLogger(ListingService.class);

    private static Gson gson = new Gson();

    @Value("${use.new.listing.service}")
    private boolean useNewListingService;

    @Autowired
    private OrchListingService orchListingService;

    @Autowired
    private TravelTipFactory travelTipFactory;

    @Autowired
    private CardEngineResponseTransformerFactory cardEngineResponseTransformerFactory;

    @PostConstruct
    public void init() {
        if(consulFlag){
            landingFilterConditions = commonConfigConsul.getLandingFilterConditions();
            logger.debug("Fetching values from commonConfig consul");
        }
        else {
            CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
            landingFilterConditions = commonConfig.landingFilterConditions();
            commonConfig.addPropertyChangeListener("landingFilterConditions", event -> landingFilterConditions = commonConfig.landingFilterConditions());
        }
    }

    /*
     * This is old searchHotels just for the sake of maintaining backward compatibility
     * of older apps.
     */
    public String searchHotelsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap, boolean isCrossSellRequest) throws ClientGatewayException {
    	try {
    		SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
    		CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            if (utility.isRearchFlow(useNewListingService, searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getRequestId() : "", searchHotelsRequest.getExpDataMap())) {
                return orchListingService.searchHotelsScion(searchHotelsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
            }

            return searchHotelsExecutor.searchHotelsOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap, isCrossSellRequest);
        } catch (Throwable e) {
    	    if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in searchHotelsOld: " + e.getMessage());
                logger.debug("error occurred in searchHotelsOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in searchHotelsOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String landingDiscoveryOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.landingDiscoveryOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in landingDiscoveryOld: " + e.getMessage());
                logger.debug("error occurred in landingDiscoveryOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in landingDiscoveryOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String searchPersonalizedHotelsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
    	try {

    		SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
    		CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
        	//searchHotelsRequest.setSortCriteria(listingHelper.getSortCriteria(searchHotelsRequest, commonModifierResponse, searchHotelsRequest.getSearchCriteria()));
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.searchPersonalizedHotelsOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
        	else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in searchPersonalizedHotelsOld: " + e.getMessage());
        		logger.debug("error occurred in searchPersonalizedHotelsOld: " + e.getMessage(), e);
        	}else
        		logger.error("error occurred in searchPersonalizedHotelsOld: " + e.getMessage(), e);

        	ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String listingMapOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.listingMapOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in listingMapOld: " + e.getMessage());
                logger.debug("error occurred in listingMapOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in listingMapOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public SearchHotelsResponse searchHotels(SearchHotelsRequest searchHotelsRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        boolean isCrossSellRequest = crossSellUtil.isCrossSellRequest(searchHotelsRequest);
        try {
            long startTime = System.currentTimeMillis();
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            if (utility.isRearchFlow(useNewListingService, searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getRequestId() : "", searchHotelsRequest.getExpDataMap())) {
                return orchListingService.searchHotels(searchHotelsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
            }

            if (searchHotelsRequest.getSearchCriteria() != null) {
                utility.setPaginatedToMDC(searchHotelsRequest.getSearchCriteria());
                utility.setLoggingParametersToMDC(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates(), searchHotelsRequest.getSearchCriteria().getCheckIn(),
                        searchHotelsRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_COMMON_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);
            SearchWrapperInputRequest searchWrapperInputRequest = searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                    .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            ListingPagePersonalizationResponsBO listingPagePersonalizationResponseBO = null;
            boolean isChainRequest =  searchHotelsRequest.getMatchMakerDetails()!=null && CollectionUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels()) && searchHotelsRequest.getMatchMakerDetails().getHotels().get(0).getPropertyInfo()!=null && StringUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels().get(0).getPropertyInfo().getChainId());
            if (searchHotelsRequest.getSearchCriteria().isNearBySearch()) {
                SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = searchHotelsExecutor.nearByHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
                listingPagePersonalizationResponseBO = listingHelper.convertSearchHotelsToPersonalizedHotels(searchWrapperResponseBO,searchHotelsRequest);
            } else if (searchHotelsRequest.getSearchCriteria().isPersonalizedSearch() || isChainRequest) {
                listingPagePersonalizationResponseBO = searchHotelsExecutor.searchPersonalizedHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap, isCrossSellRequest);
            } else {
                SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = searchHotelsExecutor.searchHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
                listingPagePersonalizationResponseBO = listingHelper.convertSearchHotelsToPersonalizedHotels(searchWrapperResponseBO,searchHotelsRequest);
            }
            return searchHotelsFactory.getResponseService(searchHotelsRequest.getClient())
                    .convertSearchHotelsResponse(listingPagePersonalizationResponseBO,searchHotelsRequest,commonModifierResponse);

        } catch (Throwable e) {
            if (isCrossSellRequest) {
                SearchHotelsResponse response = new SearchHotelsResponse();
                response.setCrossSellData(crossSellUtil.getCrossSellData(true, searchHotelsRequest.getDeviceDetails().getBookingDevice(), NULL_CITY,searchHotelsRequest,httpHeaderMap,null));
                return response;
            }
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in searchHotels: " + e.getMessage());
                logger.debug("error occurred in searchHotels: " + e.getMessage(), e);
            } else
                logger.error("error occurred in searchHotels: " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public MobLandingResponse mobLanding(MobLandingRequest mobLandingRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        try {
            commonHelper.updateCurrencyAndSource(mobLandingRequest.getSearchCriteria(), mobLandingRequest.getRequestDetails(), httpHeaderMap, mobLandingRequest.getDeviceDetails());
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(mobLandingRequest.getSearchCriteria(), mobLandingRequest, httpHeaderMap);
            HotelLandingMobRequestBody hotelLandingMobRequestBody =
                    mobLandingFactory.getRequestService(mobLandingRequest.getClient())
                            .convertMobLandingRequest(mobLandingRequest, commonModifierResponse);

            String response = mobLandingExecutor.moblanding(hotelLandingMobRequestBody, parameterMap, httpHeaderMap);
            HotelLandingWrapperResponse hotelLandingWrapperResponse = objectMapperUtil.getObjectFromJson(response, HotelLandingWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
            if (hotelLandingWrapperResponse != null && hotelLandingWrapperResponse.getErrorResponse() != null
                    && CollectionUtils.isNotEmpty(hotelLandingWrapperResponse.getErrorResponse().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, hotelLandingWrapperResponse.getErrorResponse().getErrorList().get(0).getErrorCode(),
                        hotelLandingWrapperResponse.getErrorResponse().getErrorList().get(0).getErrorMessage());
            }

            return mobLandingFactory.getResponseService(mobLandingRequest.getClient())
                    .convertMobLandingResponse(mobLandingRequest,hotelLandingWrapperResponse, mobLandingRequest.getClient(), commonModifierResponse.getExpDataMap());

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in mobLanding: " + e.getMessage());
                logger.debug("error occurred in mobLanding: " + e.getMessage(), e);
            } else
                logger.error("error occurred in mobLanding: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }
    public TravelTipResponse travelTip(TravelTipRequest travelTipRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        try {

            String response = travelTipExecutor.fetchTravelTips(travelTipRequest, parameterMap, httpHeaderMap);
            if (StringUtils.isEmpty(response)) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM,Constants.ERROR_CODE_DATA_NOT_FOUND,
                        Constants.ERROR_MESSAGE_CITY_DATA_NOT_AVAILABLE);
            }
           TravelTipWrapperResponse  travelTipWrapperResponse = objectMapperUtil.getObjectFromJson(response, TravelTipWrapperResponse.class, DependencyLayer.ORCHESTRATOR);


            if (travelTipWrapperResponse != null && travelTipWrapperResponse.getErrorResponse() != null
                    && CollectionUtils.isNotEmpty(travelTipWrapperResponse.getErrorResponse().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, travelTipWrapperResponse.getErrorResponse().getErrorList().get(0).getErrorCode(),
                        travelTipWrapperResponse.getErrorResponse().getErrorList().get(0).getErrorMessage());
            }

            return travelTipFactory.getResponseService(travelTipRequest.getClient())
                    .convertTravelTipResponse( travelTipWrapperResponse, travelTipRequest.getClient());


        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in travelTip: " + e.getMessage());
                logger.debug("error occurred in travelTip: " + e.getMessage(), e);
            } else
                logger.error("error occurred in travelTip: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }
    public String mobLandingOld(HotelLandingMobRequestBody mobLandingBody, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        try {
            MobLandingRequest mobLandingRequest = oldToNewerRequestTransformer.updateMobLandingRequest(mobLandingBody);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(mobLandingRequest.getSearchCriteria(), mobLandingRequest, httpHeaderMap);
            commonHelper.updateCurrencyAndSource(mobLandingRequest.getSearchCriteria(), mobLandingRequest.getRequestDetails(), httpHeaderMap, mobLandingRequest.getDeviceDetails());
            HotelLandingMobRequestBody hotelLandingMobRequestBody =
                    mobLandingFactory.getRequestService(mobLandingRequest.getClient())
                            .convertMobLandingRequest(mobLandingRequest, commonModifierResponse);

            return  mobLandingExecutor.moblanding(hotelLandingMobRequestBody,parameterMap,httpHeaderMap);

        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in mobLanding: " + e.getMessage());
                logger.debug("error occurred in mobLanding: " + e.getMessage(), e);
            } else
                logger.error("error occurred in mobLanding: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    /*
     * This is old searchHotels just for the sake of maintaining backward compatibility
     * of older apps.
     */
    public String filterCountOld(SearchWrapperInputRequest filterRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {

            ListingSearchRequest filterRequestNew = oldToNewerRequestTransformer.updateSearchHotelsRequest(filterRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(filterRequestNew.getSearchCriteria(), filterRequestNew, httpHeaderMap);
           // filterRequestNew.setSortCriteria(listingHelper.getSortCriteria(filterRequestNew, commonModifierResponse, filterRequestNew.getSearchCriteria()));
            SearchWrapperInputRequest filterRequestHES =
                    filterFactory.getRequestService(filterRequest.getBookingDevice())
                            .convertSearchRequest(filterRequestNew, commonModifierResponse);
           return filterExecutor.filterCount(filterRequestHES, httpHeaderMap,String.class);

        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in filterCountOld: " + e.getMessage());
            }else
                logger.error("error occurred in filterCountOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public FilterResponse filterCount(FilterCountRequest filterRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, Boolean seoCorp, List<DPTExperimentDetails> dptExperimentDetailsList) throws ClientGatewayException {
        FilterResponse filterResponse = null;
        try {
            long startTime = System.currentTimeMillis();
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(filterRequest.getSearchCriteria(), filterRequest, httpHeaderMap);
//          Adding this filter In case of filter-count Request to sort the properties on the basis of AltAcco
            if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(filterRequest.getRequestDetails().getFunnelSource()) || Constants.FUNNEL_SOURCE_HOMESTAY_NEW.equalsIgnoreCase(filterRequest.getRequestDetails().getFunnelSource())) {
                if (CollectionUtils.isEmpty(filterRequest.getFilterCriteria())) {
                    filterRequest.setFilterCriteria(new ArrayList<>());
                }
                com.mmt.hotels.clientgateway.request.Filter homeStayfilter = new com.mmt.hotels.clientgateway.request.Filter(FilterGroup.PROPERTY_CATEGORY, Constants.ALT_ACCO_PROPERTIES);
                filterRequest.getFilterCriteria().add(0, homeStayfilter);
                filterRequest.getFiltersToRemove().add(new com.mmt.hotels.clientgateway.request.Filter(FilterGroup.ALT_ACCO_PROPERTY, Constants.ALTACCO));
            }
            if (seoCorp != null && Boolean.TRUE.equals(seoCorp) && filterRequest.getRequestDetails() != null) {
                filterRequest.getRequestDetails().setSeoCorp(true);
                filterRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_FILTER_REQUEST_PROCESSOR, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            SearchWrapperInputRequest filterRequestHES =
                    filterFactory.getRequestService(filterRequest.getClient())
                            .convertSearchRequest(filterRequest, commonModifierResponse);

            FilterSearchMetaDataResponse filterResponseHES = filterExecutor.filterCount(filterRequestHES, httpHeaderMap, FilterSearchMetaDataResponse.class);
            Future<String> filterSettingsFromRuleEngineFuture = null;
            boolean ruleBasedFilterSettingsEnabled = ruleBasedFilterSettings && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && Boolean.valueOf(commonModifierResponse.getExpDataMap().get(RULE_BASED_FILTER_SETTINGS_ENABLED));
            if (ruleBasedFilterSettingsEnabled) {
                EvaluateFilterRankOrderRequest evaluateFilterRankOrderRequest = filterFactory.getRequestService(filterRequest.getClient())
                        .buildEvaluateFilterRankOrderRequest(filterRequestHES);
                filterSettingsFromRuleEngineFuture = filterExecutor.evaluateFilterRankOrder(evaluateFilterRankOrderRequest, filterRequest.getCorrelationKey(), httpHeaderMap);
            }
            if (filterResponseHES != null && filterResponseHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(filterResponseHES.getResponseErrors().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(),
                        filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            } else if (MapUtils.isEmpty(filterResponseHES.getFilterDataMap())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.MARSHALLING, MarshallingErrors.NO_DATA_FOUND.getErrorCode(), MarshallingErrors.NO_DATA_FOUND.getErrorMsg());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DOWNSTREAM_FILTER_HES_CALL, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();

            /*
             * myPartner change log : commonModifierResponse floated down, check the method comment for more info
             * */
            String filterSettingsFromRuleEngine = filterSettingsFromRuleEngineFuture != null ? filterSettingsFromRuleEngineFuture.get() : null;
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_FILTER_PMS_CONFIGURATION, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();

            // This is the wrapper that holds config for filter pills
            boolean isFilterPillExperiment = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.FILTER_PILL_EXP));
            boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
            FilterPillConfigurationWrapper filterPillConfigurationWrapper = null;
            if (isFilterPillExperiment && !isMyPartnerRequest && (filterRequest.getRequestDetails() != null) && (filterRequest.getSearchCriteria() != null)) {
                boolean isSortCheapestBestReviewedApplicable = (commonModifierResponse!=null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), Constants.CHEAPEST_BEST_REVIEWED_ENABLE_EXP));
                boolean isSortMostAndBestReviewedApplicable = (commonModifierResponse != null && utility.isExperimentTrue(commonModifierResponse.getExpDataMap(), Constants.MOST_AND_BEST_REVIEWED_SORTING_ENABLE_EXP));
                filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(
                        filterRequest.getRequestDetails().getFunnelSource(),
                        filterRequest.getSearchCriteria().getLocationType(),
                        filterRequest.getRequestDetails().getIdContext(),
                        isSortCheapestBestReviewedApplicable,
                        isSortMostAndBestReviewedApplicable,
                        filterResponseHES.getCityHeroPoiName(),
                        false,
                        filterResponseHES.isSavedForCompanyDataAvailable(),
                        filterRequest.getRequestDetails()!=null && filterRequest.getRequestDetails().isPremium(),
                        filterResponseHES.getAccessPoints()
                );
            }

            if (filterRequest.getRequestDetails() != null && commonModifierResponse != null && filterRequest.getExpDataMap() != null) {
                if (utility.isExperimentOn(filterRequest.getExpDataMap(), Constants.FILTER_SCREEN_V4)) {
                    FilterConfigurationV2 filterConfigurationV2 = filterFactory.getFilterConfigurationV2(commonModifierResponse, filterRequest, filterResponseHES.getUserCohort());
                    filterResponse = filterFactory.getResponseService(filterRequest.getClient())
                            .convertFilterResponseV2(filterResponseHES, filterConfigurationV2, filterRequest, commonModifierResponse.getExpDataMap(), commonModifierResponse, filterPillConfigurationWrapper);
                } else {
                    FilterConfiguration filterConfiguration = filterFactory.getFilterConfiguration(filterRequest.getClient(),
                            filterRequest.getRequestDetails().getIdContext(), filterRequest.getRequestDetails().getFunnelSource(), commonModifierResponse, filterResponseHES.getLocationName(), filterRequest, filterSettingsFromRuleEngine);
                    filterResponse = filterFactory.getResponseService(filterRequest.getClient())
                            .convertFilterResponse(filterResponseHES, filterConfiguration, filterRequest, commonModifierResponse.getExpDataMap(), commonModifierResponse, filterPillConfigurationWrapper);
                }
            }
            listingHelper.populateDPTExperimentDetailsList(dptExperimentDetailsList, filterResponseHES);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_FILTER_RESPONSE_PROCESS, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in filterCount: " + e.getMessage());
                logger.debug("error occurred in filterCount: " + e.getMessage(), e);
            } else
                logger.error("error occurred in filterCount: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return filterResponse;
    }
    
	public void filterCountLogging(FilterCountRequest filterRequest, FilterResponse response,
			Map<String, String> httpHeaderMap, boolean seoCorp, List<DPTExperimentDetails> dptExperimentDetailsList) {
		try {
			if(response==null) {
				logger.error("Skipping Kafka logging as filter-count response is empty!!");
			} else {
				logger.debug("Filter Response : {}", response.toString());	
				CommonModifierResponse commonModifierResponse = commonHelper.processRequest(filterRequest.getSearchCriteria(), filterRequest, httpHeaderMap);
	            if (Boolean.TRUE.equals(seoCorp) && filterRequest.getRequestDetails() != null) {
	                filterRequest.getRequestDetails().setSeoCorp(true);
	                filterRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
	            }
	            FilterRequestTransformer filterRequestTransformer = filterFactory.getRequestService(filterRequest.getClient());
	            SearchWrapperInputRequest searchWrapperInputRequest = filterRequestTransformer.convertSearchRequest(filterRequest, commonModifierResponse);
	            searchWrapperInputRequest.setInitialCohortId(filterRequest.getInitialCohortId());
	            FilterCountLoggingRequest filterCountLoggingRequest = new FilterCountLoggingRequest();
	            filterCountLoggingRequest.setCurrentCohort(response.getCurrentCohortId());
	            filterCountLoggingRequest.setFilterShownList(buildFilterShown(response.getFilterList()));	
	            filterCountLoggingRequest.setSearchWrapperInputRequest(searchWrapperInputRequest);
                filterCountLoggingRequest.setDptExperimentDetailsList(dptExperimentDetailsList);
	            logger.debug("Request sent to hes {}",filterCountLoggingRequest.toString());
	            pdtLoggingThreadPool.submit(() -> {
						try {
							filterExecutor.filterCountLogging(filterCountLoggingRequest,httpHeaderMap);
						} catch (ClientGatewayException e) {
							logger.error("Error occured in executing filter PDT kafka logging job", e);
						}
	            });
			}
		} catch(Exception e) {
			logger.error("Error occured while logging filterCount request and response", e);
		}		
	}

    private List<FilterShownDetails> buildFilterShown(List<FilterCategory> filterList) {
    	logger.debug("Filter list : {}", filterList);
    	List<FilterShownDetails> filterShown = new ArrayList<>();
    	try {
    		for (FilterCategory filterCategory : filterList) {
        		List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = filterCategory.getFilters();
        		if(CollectionUtils.isNotEmpty(filters)) {
        			List<FilterShownDetails> filterDetails = filters.stream().map(filter -> {
                    	FilterShownDetails filterDetail = new FilterShownDetails();
                    	filterDetail.setCategoryName(filterCategory.getCategoryName());
                    	filterDetail.setFilterGroup(filter.getFilterGroup());
                    	filterDetail.setFilterValue(filter.getFilterValue());
                    	if(filter.getRangeFilter()!=null) {
                    		filterDetail.setRangeFilter(filter.getRangeFilter());
                    		if(filter.getRangeFilter() && filter.getFilterRange()!=null) {
                				filterDetail.setStartRange((long)filter.getFilterRange().getMinValue());
                				filterDetail.setEndRange((long)filter.getFilterRange().getMaxValue());
                			}
                    	}
        				return filterDetail;
            		}).collect(Collectors.toList());
                    
        			filterShown.addAll(filterDetails);  
        		}
                 		
        	}
    	} catch (Exception e) {
    		logger.error("Error in building FilterShown value :",e);
    		}
    	return filterShown;
	}

	public FilterResponse batchFilterResponse(FilterCountRequest filterRequest, Map<String, String> httpHeaderMap, Boolean seoCorp) throws ClientGatewayException {

        FilterResponse batchFilterResponse = null;
        try {
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(filterRequest.getSearchCriteria(), filterRequest, httpHeaderMap);
            if (Boolean.TRUE.equals(seoCorp) && filterRequest.getRequestDetails() != null) {
                filterRequest.getRequestDetails().setSeoCorp(true);
                filterRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
            }
            SearchWrapperInputRequest filterRequestHES = filterFactory.getRequestService(filterRequest.getClient())
                            .convertSearchRequest(filterRequest, commonModifierResponse);
            filterRequestHES.setBatchFiltersRequest(true);
            FilterSearchMetaDataResponse filterResponseHES = filterExecutor.filterCount(filterRequestHES, httpHeaderMap, FilterSearchMetaDataResponse.class);
            if (filterResponseHES != null && filterResponseHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(filterResponseHES.getResponseErrors().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(),
                        filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            } else if (filterResponseHES != null && CollectionUtils.isEmpty(filterResponseHES.getBatchFilters())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.MARSHALLING, MarshallingErrors.NO_DATA_FOUND.getErrorCode(), MarshallingErrors.NO_DATA_FOUND.getErrorMsg());
            }

            batchFilterResponse = filterFactory.getResponseService(filterRequest.getClient()).convertBatchFilterResponse(filterResponseHES);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in batchFilterResponse: " + e.getMessage());
                logger.debug("error occurred in batchFilterResponse: " + e.getMessage(), e);
            } else
                logger.error("error occurred in batchFilterResponse: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return batchFilterResponse;
    }

    public ListingMapResponse listingMap(ListingMapRequest listingMapRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        try {
        	CommonModifierResponse commonModifierResponse = commonHelper.processRequest(listingMapRequest.getSearchCriteria(), listingMapRequest, httpHeaderMap);

            SearchWrapperInputRequest listingMapRequestCB =
                    listingMapFactory.getRequestService(listingMapRequest.getClient())
                            .convertListingMapRequest(listingMapRequest, commonModifierResponse);

            HotelListingMapResponse hotelListingMapResponse = listingMapExecutor.listingMap(listingMapRequestCB, httpHeaderMap);

            return listingMapFactory.getResponseService(listingMapRequest.getClient())
                    .convertListingMapResponse(hotelListingMapResponse,utility.getExpDataMap(listingMapRequest.getExpData()),listingMapRequest.getDeviceDetails(),listingMapRequest.getRequestDetails(), commonModifierResponse);
        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in listingMap: " + e.getMessage());
                logger.debug("error occurred in listingMap: " + e.getMessage(), e);
            } else
                logger.error("error occurred in listingMap: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String fetchCollectionsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
            listingHelper.updateCollectionCounts(searchHotelsRequest);
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            String responseHes = searchHotelsExecutor.fetchCollectionsOld(searchWrapperInputRequestModified,parameterMap,httpHeaderMap);
            return convertHesCollectionResponseToOldFormat(responseHes);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in fetch collections: " + e.getMessage());
                logger.debug("error occurred in fetch collections: " + e.getMessage(), e);
            } else
                logger.error("error occurred in fetch collections: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String convertHesCollectionResponseToOldFormat(String responseHes) {
        try{
            CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = objectMapperUtil.getObjectFromJsonWithType
                    (responseHes, new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {}, DependencyLayer.ORCHESTRATOR);
            if(collectionsResponseBO!=null &&  CollectionUtils.isNotEmpty(collectionsResponseBO.getCardCollections())){
                TrendingNowData trendingNowData=new TrendingNowData();
                CardCollections cardCollection=collectionsResponseBO.getCardCollections().get(0);
                trendingNowData.setTrendingNowCards(new ArrayList<>());
                BeanUtils.copyProperties(cardCollection,trendingNowData);
                cardCollection.getCardList().forEach(card->{
                    TrendingNowCard trendingNowCard=new TrendingNowCard();
                    BeanUtils.copyProperties(card,trendingNowCard);
                    trendingNowData.getTrendingNowCards().add(trendingNowCard);
                });

                collectionsResponseBO.setTrendingNowData(trendingNowData);
                collectionsResponseBO.setCardCollections(null);
            }
            return objectMapperUtil.getJsonFromObject(collectionsResponseBO, DependencyLayer.CLIENTGATEWAY);
        }catch(Exception e){
            logger.error("Exception occurred while converting hes response to old format " + e.getMessage(),e);
        }
        return responseHes;
    }

    public FetchCollectionResponse fetchCollections(FetchCollectionRequest fetchCollectionRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            String requestSource = httpHeaderMap.get(Constants.REQUEST_SOURCE_KEY);
            String responseHes = fetchCollectionOld(fetchCollectionRequest, parameterMap, httpHeaderMap);
            return convertHesCollectionResponse(responseHes,fetchCollectionRequest, requestSource);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in fetchCollections: " + e.getMessage());
                logger.debug("error occurred in fetchCollections: " + e.getMessage(), e);
            } else
                logger.error("error occurred in fetchCollections: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private String fetchCollectionOld(FetchCollectionRequest fetchCollectionRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        CommonModifierResponse commonModifierResponse = commonHelper.processRequest(fetchCollectionRequest.getSearchCriteria(), fetchCollectionRequest, httpHeaderMap);
        listingHelper.updateCollectionCounts(fetchCollectionRequest);
if (fetchCollectionRequest.getSearchCriteria() != null) {
                utility.setLoggingParametersToMDC(fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates(), fetchCollectionRequest.getSearchCriteria().getCheckIn(),
                        fetchCollectionRequest.getSearchCriteria().getCheckOut());
            }        SearchWrapperInputRequest searchWrapperInputRequestModified =
                searchHotelsFactory.getRequestService(fetchCollectionRequest.getClient())
                        .convertSearchRequest(fetchCollectionRequest, commonModifierResponse);

        return searchHotelsExecutor.fetchCollectionsOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
    }

    public FetchCollectionResponseV2 fetchCollectionsV2(FetchCollectionRequest fetchCollectionRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            String requestSource = httpHeaderMap.get(Constants.REQUEST_SOURCE_KEY);
            String responseHes = fetchCollectionOld(fetchCollectionRequest, parameterMap, httpHeaderMap);
            return convertHesCollectionResponseV2(responseHes, fetchCollectionRequest, requestSource);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in fetchCollectionsV2: " + e.getMessage());
                logger.debug("error occurred in fetchCollectionsV2: " + e.getMessage(), e);
            } else
                logger.error("error occurred in fetchCollectionsV2: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private FetchCollectionResponseV2 convertHesCollectionResponseV2(String responseHes, FetchCollectionRequest fetchCollectionRequest, String requestSource) {
        FetchCollectionResponseV2 fetchCollectionResponseV2 = new FetchCollectionResponseV2();
        try {

            Map<String, String> expDataMap = new HashMap<>();
            if (fetchCollectionRequest != null && org.apache.commons.lang3.StringUtils.isNotEmpty(fetchCollectionRequest.getExpData())) {
                expDataMap = utility.getExpDataMap(fetchCollectionRequest.getExpData());
            }
            CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = objectMapperUtil.getObjectFromJsonWithType
                    (responseHes, new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {
                    }, DependencyLayer.ORCHESTRATOR);
            fetchCollectionResponseV2.setCorrelationKey(collectionsResponseBO.getCorrelationKey());
            if (collectionsResponseBO.getResponseErrors() != null) {
                fetchCollectionResponseV2.setResponseErrors(collectionsResponseBO.getResponseErrors());
                return fetchCollectionResponseV2;
            }

            purposeStayFilterTitleMap = objectMapperUtil.getObjectFromJsonWithType(purposeStayFilterTitle, new TypeReference<Map<String, String>>() {}, DependencyLayer.CLIENTGATEWAY);
            fetchCollectionResponseV2.setTitle(fetchCollectionHelper.getTitleForBottomFilterSheet(fetchCollectionRequest, purposeStayFilterTitleMap));
            fetchCollectionResponseV2.setFetchCollectionList(getCollectionList(collectionsResponseBO, fetchCollectionRequest, requestSource));
            if (fetchCollectionHelper.shouldAddFilters(Constants.MORE_FILTERS, fetchCollectionRequest, landingFilterConditions)) {
                List<FilterCategory> moreFilters = getMoreFilters(collectionsResponseBO, fetchCollectionRequest);
                fetchCollectionResponseV2.setMoreFilters(moreFilters);
            }
            if (fetchCollectionHelper.shouldAddFilters(Constants.SUGGESTED_FILTERS, fetchCollectionRequest, landingFilterConditions)) {
                if (collectionsResponseBO.getSuggestedFilters() != null && MapUtils.isNotEmpty(collectionsResponseBO.getSuggestedFilters().getPreferredFilters())) {
                    CollectionFilters suggestedFilters = new CollectionFilters();
                    suggestedFilters.setHeading(collectionsResponseBO.getSuggestedFilters().getHeading());
                    suggestedFilters.setSubHeading(collectionsResponseBO.getSuggestedFilters().getSubHeading());
                    List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
                    for (Set<Filter> filterSet : collectionsResponseBO.getSuggestedFilters().getPreferredFilters().values()) {
                        filters.addAll(filterResponseTransformer.buildFilter(new ArrayList<>(filterSet)));
                    }
                    suggestedFilters.setFilters(filters);
                    fetchCollectionResponseV2.setSuggestedFilters(suggestedFilters);
                }
            }
            if (fetchCollectionHelper.shouldAddFilters(Constants.OTHER_FILTER_CATEGORY, fetchCollectionRequest, landingFilterConditions)) {
                FilterCategory otherFilterCategory = getFilterCategoryFromHistoGramBuckets(fetchCollectionRequest);
                if(otherFilterCategory != null){
                    fetchCollectionResponseV2.setOtherFilterCategory(otherFilterCategory);
                }
            }
            fetchCollectionResponseV2.setShowPetSelection(collectionsResponseBO.getShowPetSelection());

            if(fetchCollectionResponseV2 != null && fetchCollectionRequest != null && MapUtils.isNotEmpty(fetchCollectionRequest.getExpDataMap())
                    && fetchCollectionRequest.getExpDataMap().containsKey(MULTI_ROOM_EXP)) {
                buildingPaxFilters(fetchCollectionResponseV2, fetchCollectionRequest);
            }
            fetchCollectionResponseV2.setCurrency(collectionsResponseBO.getCurrency());

            if (fetchCollectionResponseV2 == null) {
                List<Error> errors = new ArrayList<>();
                errors.add(new Error.Builder().buildErrorCode(CBError.NO_COLLECTION_DATA_FROM_CG.getCode(), CBError.NO_COLLECTION_DATA_FROM_CG.getDescription()).build());
                fetchCollectionResponseV2.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
            } else {
                listingHelper.sortBasedOnPriority(fetchCollectionResponseV2.getFetchCollectionList());
            }
            if(collectionsResponseBO.getPropertiesPager() != null){
                fetchCollectionResponseV2.setPropertiesPager(collectionsResponseBO.getPropertiesPager());
            }

            if (collectionsResponseBO.getChatbotInfo() != null){
                boolean isTravelPlexEnabled = utility.isExperimentOn(fetchCollectionRequest.getExpDataMap(), Constants.TRAVEL_PLEX_ENABLED);
                fetchCollectionResponseV2.setChatbotInfo(utility.buildChatbotInfoStaticDetail(collectionsResponseBO.getChatbotInfo(), isTravelPlexEnabled));
            }
        } catch (Exception e) {
            logger.error("Exception occurred while converting HES Response String to object :  " + e.getMessage(), e);
            List<Error> errors = new ArrayList<>();
            errors.add(new Error.Builder().buildErrorCode(CBError.GENERIC_ERROR.getCode(), CBError.GENERIC_ERROR.getDescription()).build());
            fetchCollectionResponseV2.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
        }
        return fetchCollectionResponseV2;
    }

    private FetchCollectionResponse convertHesCollectionResponse(String responseHes, FetchCollectionRequest fetchCollectionRequest, String requestSource) {
        FetchCollectionResponse fetchCollectionResponse = new FetchCollectionResponse();
        try{
            CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = objectMapperUtil.getObjectFromJsonWithType
                    (responseHes, new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {}, DependencyLayer.ORCHESTRATOR);
            fetchCollectionResponse.setCorrelationKey(collectionsResponseBO.getCorrelationKey());
            if(collectionsResponseBO!=null && collectionsResponseBO.getResponseErrors()!=null){
                fetchCollectionResponse.setResponseErrors(collectionsResponseBO.getResponseErrors());
                return fetchCollectionResponse;
            }
            fetchCollectionResponse.setFetchCollectionList(getCollectionList(collectionsResponseBO,fetchCollectionRequest, requestSource));
            FetchCollection suggestedFilters = getSuggestedFilters(collectionsResponseBO);
            fetchCollectionResponse.setSuggestedFilters(suggestedFilters);
            List<FilterCategory> moreFilters = getMoreFilters(collectionsResponseBO, fetchCollectionRequest);
            fetchCollectionResponse.setMoreFilters(moreFilters);

            if(CollectionUtils.isEmpty(fetchCollectionResponse.getFetchCollectionList()) && CollectionUtils.isEmpty(fetchCollectionResponse.getMoreFilters()) &&
                    fetchCollectionRequest.getRequestDetails() != null && fetchCollectionRequest.getRequestDetails().getPageContext() != null &&
                    !fetchCollectionRequest.getRequestDetails().getPageContext().equalsIgnoreCase(Constants.PAGE_CONTEXT_LISTING)){
                List<Error> errors = new ArrayList<>();
                errors.add(new Error.Builder().buildErrorCode(CBError.NO_COLLECTION_DATA_FROM_CG.getCode(),CBError.NO_COLLECTION_DATA_FROM_CG.getDescription()).build());
                fetchCollectionResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
            }else{
                listingHelper.sortBasedOnPriority(fetchCollectionResponse.getFetchCollectionList());
            }
        }catch(Exception e){
            logger.error("Exception occurred while converting HES Response String to object :  " +e.getMessage(),e);
            List<Error> errors = new ArrayList<>();
            errors.add(new Error.Builder().buildErrorCode(CBError.GENERIC_ERROR.getCode(),CBError.GENERIC_ERROR.getDescription()).build());
            fetchCollectionResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
        }
        return fetchCollectionResponse;
    }

    private List<FilterCategory> getMoreFilters(CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO, FetchCollectionRequest fetchCollectionRequest) {
        if(MapUtils.isEmpty(collectionsResponseBO.getMoreFiltersMap())) {
            return null;
        }
        Map<String, List<Filter>> moreFiltersMap = collectionsResponseBO.getMoreFiltersMap();
        List<FilterCategory> moreFilters = new ArrayList<>();
        moreFiltersMap.forEach((cardTitle, filterList) -> {
            FilterCategory filterCategory = new FilterCategory();
            filterCategory.setTitle(cardTitle);
            filterCategory.setViewType(filterList.get(0).getViewType() != null ? filterList.get(0).getViewType() : Constants.VIEW_TYPE_FLEX);
            filterCategory.setCategoryName(Constants.FILTER_POPULAR);
            filterCategory.setVisible(true);
            filterCategory.setFilters(filterResponseTransformer.buildFilter(filterList));
            moreFilters.add(filterCategory);
        });
        FilterCategory otherFilterCategory = getFilterCategoryFromHistoGramBuckets(fetchCollectionRequest);
        if(otherFilterCategory != null){
            moreFilters.add(otherFilterCategory);
        }
        return moreFilters;
    }

    public FilterCategory getFilterCategoryFromHistoGramBuckets(FetchCollectionRequest fetchCollectionRequest) {
        List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = filterResponseTransformer.createDefaultPriceHistogramBuckets(fetchCollectionRequest.getSearchCriteria());
        if(CollectionUtils.isNotEmpty(filters)){
            FilterCategory hotelPriceFilterCategory = new FilterCategory();
            if(utility.isExpPdoPrnt(fetchCollectionRequest.getExpData())){
                hotelPriceFilterCategory.setTitle(polyglotService.getTranslatedData(Constants.PRICE_PER_ROOM_PER_NIGHT));
            }else{
                hotelPriceFilterCategory.setTitle(polyglotService.getTranslatedData(Constants.PRICE_PER_NIGHT));
            }
            hotelPriceFilterCategory.setViewType(Constants.VIEW_TYPE_GRAPH);
            hotelPriceFilterCategory.setVisible(true);
            hotelPriceFilterCategory.setCategoryName(Constants.PRICE_CATEGORY_NAME);
            hotelPriceFilterCategory.setFilters(filters);
            return hotelPriceFilterCategory;

        }
        return null;
    }

    private List<FetchCollection> getCollectionList(CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO, FetchCollectionRequest fetchCollectionRequest, String requestSource) {
        List<FetchCollection> fetchCollectionList=new ArrayList<>();
        addFeaturedCollectionsToList(collectionsResponseBO.getCollectionsResponse(),fetchCollectionRequest,fetchCollectionList, requestSource);
        addCardCollectionsToList(collectionsResponseBO.getCardCollections(),fetchCollectionList);

        return fetchCollectionList;
    }

    private FetchCollection getSuggestedFilters(CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO){

        if (collectionsResponseBO.getSuggestedFilters()==null){
            return null;
        }

        CardCollections suggestedFilter = collectionsResponseBO.getSuggestedFilters();

        FetchCollection fetchCollection=new FetchCollection();
        fetchCollection.setCardType(suggestedFilter.getCardType());
        fetchCollection.setHeading(suggestedFilter.getHeading());
        fetchCollection.setSubHeading(suggestedFilter.getSubHeading());
        fetchCollection.setAppliedFilterMap(suggestedFilter.getPreferredFilters());
        return fetchCollection;
    }

    private void addCardCollectionsToList(List<CardCollections> cardCollectionList, List<FetchCollection> fetchCollectionList) {
        if(CollectionUtils.isNotEmpty(cardCollectionList)){
            cardCollectionList.forEach(cardCollection ->{
                FetchCollection fetchCollection=new FetchCollection();
                fetchCollection.setCardType(cardCollection.getCardType());
                fetchCollection.setHeading(cardCollection.getHeading());
                fetchCollection.setSubHeading(cardCollection.getSubHeading());
                if(CollectionUtils.isNotEmpty(cardCollection.getCardList())){
                    List<CardCG> cardCGS = new ArrayList<>();
                    for(Card card : cardCollection.getCardList()){
                        CardCG cardCG = new CardCG();
                        BeanUtils.copyProperties(card, cardCG);
                        cardCGS.add(cardCG);
                    }
                    // fetchCollection.setCardList(cardCollection.getCardList());
                    fetchCollection.setCardList(cardCGS);
                }
                if(cardCollection!=null && cardCollection.getCardInfo()!=null){
                    fetchCollection.setCardInfo(getCardInfo(cardCollection));
                }
                fetchCollection.setPriority(cardCollection.getPriority());
                fetchCollection.setTemplate(cardCollection.getTemplate());
                fetchCollection.setBgImageUrl(cardCollection.getBgImageUrl());
                fetchCollection.setCollectionDescription(cardCollection.getCollectionDescription());
                fetchCollection.setCta(cardCollection.getCta());
                fetchCollectionList.add(fetchCollection);
            });
        }

    }

    private CardData getCardInfo(CardCollections cardCollection){
        CardData cardInfo = new CardData();
        cardInfo.setSubText(StringUtils.isNotEmpty(cardCollection.getCardInfo().getSubText())?cardCollection.getCardInfo().getSubText():null);
        cardInfo.setTitleText(StringUtils.isNotEmpty(cardCollection.getCardInfo().getTitleText())?cardCollection.getCardInfo().getTitleText():null);
        cardInfo.setIndex(cardCollection.getCardInfo().getIndex());
        cardInfo.setStarText(cardCollection.getCardInfo().getStarText());
        cardInfo.setCardId(StringUtils.isNotEmpty(cardCollection.getCardInfo().getCardId())?cardCollection.getCardInfo().getCardId():null);
        cardInfo.setIconUrl(StringUtils.isNotEmpty(cardCollection.getCardInfo().getIconUrl())?cardCollection.getCardInfo().getIconUrl():null);
        cardInfo.setBgImageUrl(StringUtils.isNotEmpty(cardCollection.getCardInfo().getBgImageUrl())?cardCollection.getCardInfo().getBgImageUrl():null);
        cardInfo.setTemplateId(StringUtils.isNotEmpty(cardCollection.getCardInfo().getTemplateId())?cardCollection.getCardInfo().getTemplateId():null);
        if(CollectionUtils.isNotEmpty(cardCollection.getCardInfo().getCardAction())){
            List<CardAction> cardActionList = new ArrayList<>();
            for(CardAction ca: cardCollection.getCardInfo().getCardAction()){
                CardAction cardAction = new CardAction();
                cardAction.setWebViewUrl(StringUtils.isNotEmpty(ca.getWebViewUrl())?ca.getWebViewUrl():null);
                cardAction.setTitle(StringUtils.isNotEmpty(ca.getTitle())?ca.getTitle():null);
                cardActionList.add(cardAction);
            }
            cardInfo.setCardAction(cardActionList);
        }
        if(cardCollection.getCardInfo().getCardPayload()!=null) {
            cardInfo.setCardPayload(buildCardPayload(cardCollection.getCardInfo().getCardPayload()));
        }
        return cardInfo;
    }

    public CardPayloadResponse buildCardPayload(CardPayloadResponse cardPayloadResponse){
        CardPayloadResponse cardPayloadResponse1 = new CardPayloadResponse();
        List<GenericCardPayloadData> genericCardPayloadDataList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cardPayloadResponse.getGenericCardData()) && CollectionUtils.isNotEmpty(cardPayloadResponse.getGenericCardData().get(0).getData())){
            for (GenericCardPayloadData gpdata: cardPayloadResponse.getGenericCardData().get(0).getData()){
                GenericCardPayloadData genericCardPayloadData = new GenericCardPayloadData();
                genericCardPayloadData.setTitleText(StringUtils.isNotEmpty(gpdata.getTitleText())?gpdata.getTitleText():null);
                genericCardPayloadData.setIconUrl(StringUtils.isNotEmpty(gpdata.getIconUrl())?gpdata.getIconUrl():null);
                genericCardPayloadDataList.add(genericCardPayloadData);
            }
            cardPayloadResponse1.setGenericCardData(genericCardPayloadDataList);
        }
        return cardPayloadResponse1;
    }

    private void addFeaturedCollectionsToList(List<FeaturedCollections<SearchWrapperHotelEntity>> collectionsResponse, FetchCollectionRequest fetchCollectionRequest, List<FetchCollection> fetchCollectionList, String requestSource) {
        if(CollectionUtils.isNotEmpty(collectionsResponse)){
            Map<String, String> expDataMap = utility.getExpDataMap(fetchCollectionRequest.getExpData());
            collectionsResponse.forEach(collectionCard->{
                FetchCollection fetchCollection=new FetchCollection();
                fetchCollection.setCardType("COLLECTION");
                fetchCollection.setHeading(collectionCard.getHeading());
                fetchCollection.setSubHeading(collectionCard.getSubHeading());

                List<CardCG> cardCGS = null;
                if (Utility.isGccOrKsa() && Constants.REQUEST_SOURCE_SCION.equalsIgnoreCase(requestSource)
                        && CollectionUtils.isNotEmpty(collectionCard.getHotels()) && StringUtils.isNotBlank(collectionCard.getCardId())) {
                    fetchCollection.setHotelList(collectionCard.getHotels());
                    fetchCollection.setCardId(collectionCard.getCardId());
                } else {
                    cardCGS = new ArrayList<>();
                    List<Hotel> hotels = searchHotelsFactory.getResponseService(fetchCollectionRequest.getClient()).buildPersonalizedHotels(collectionCard.getHotels(), expDataMap, fetchCollectionRequest, null,null, null, null, VERTICAL);
                    if (CollectionUtils.isNotEmpty(hotels)) {
                        for (Hotel hotel : hotels) {
                            CardCG cardCG = new CardCG();
                            cardCG.setHotel(hotel);
                            cardCGS.add(cardCG);
                        }
                    }
                }

                fetchCollection.setCardList(cardCGS);
                fetchCollection.setPriority(collectionCard.getPriority());
                fetchCollection.setTemplate(collectionCard.getTemplate());
                fetchCollection.setThreshold(collectionCard.getThreshold());
                fetchCollection.setCta(collectionCard.getCta());
                if(null != fetchCollection.getCta())
                    fetchCollection.getCta().setDeeplink(collectionCard.getDeepLink());
                else if(StringUtils.isNotBlank(collectionCard.getDeepLink()))
                    fetchCollection.setCta(new Cta("",collectionCard.getDeepLink()));
                fetchCollectionList.add(fetchCollection);
            });
        }
    }

    public String getMobConfig(Map<String, String> httpHeaderMap, Map<String, String[]> paramMap) throws ClientGatewayException {
        try {
            String versionOnClient = paramMap.containsKey("v") ? paramMap.get("v")[0] : "";
            String variant = paramMap.containsKey("variant") ? paramMap.get("variant")[0] : "";
            return mobConfigHelper.getHotelMobConfigAsString(versionOnClient, variant, httpHeaderMap);
        } catch (Throwable e) {
            logger.error("error occurred in get mob config: " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String getMetaDataByCityResponse(String cityId, String locationId, String locationType, String filterCode,
                                            String correlationKey, Map<String, String> requestParams) throws ClientGatewayException {

        String srCon = requestParams.get(Constants.SOURCE_CON);
        String destCon = requestParams.get(Constants.DES_CON);
        String srcClient = requestParams.get(Constants.SOURCE_CLIENT);
        String currencyCode = commonHelper.getInboundCurrencyCode(srCon, destCon, srcClient);

        requestParams.put(Constants.LOCATIONID, locationId);
        requestParams.put(Constants.LOCATIONTYPE, locationType);
        requestParams.put(Constants.CURRENCYCODE, currencyCode);
        requestParams.put(Constants.CORRELATIONKEY, correlationKey);

        String response = searchHotelsExecutor.getMetaDataResponse(cityId, requestParams);

        return hotelMetaDataService.filterLocationAndFaclity(response, filterCode);

    }

    public String listingPersonalizationOld(HotelLandingMobRequestBody listPersonalizationRequest, Map<String, String[]> paramMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(listPersonalizationRequest.getHotelSearchRequest());
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
          //  searchHotelsRequest.setSortCriteria(listingHelper.getSortCriteria(searchHotelsRequest, commonModifierResponse, searchHotelsRequest.getSearchCriteria()));
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            listPersonalizationRequest.setHotelSearchRequest(searchWrapperInputRequestModified);
            return mobLandingExecutor.listPersonalizedCards(listPersonalizationRequest, paramMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in listPersonalizationOld: " + e.getMessage());
                logger.debug("error occurred in listPersonalizationOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in listPersonalizationOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    public String nearByOld(SearchWrapperInputRequest nearBySearchRequest, Map<String, String[]> paramMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateNearByHotelsRequest(nearBySearchRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
           // searchHotelsRequest.setSortCriteria(listingHelper.getSortCriteria(searchHotelsRequest, commonModifierResponse, searchHotelsRequest.getSearchCriteria()));
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.nearByHotelsOld(searchWrapperInputRequestModified, paramMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in nearByHotelsOld: " + e.getMessage());
                logger.debug("error occurred in nearByHotelsOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in nearByHotelsOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }


    public GroupBookingResponse submitGroupBooking(GroupBookingRequest groupBookingRequest, Integer requestCallBackCount) throws ClientGatewayException {
        try {
            logger.debug("Pushing data in queue!");
            kafkaThreadPool.submit(() -> {
                jsonKafkaProducer.sendMessage(groupBookingRequest,groupBookingKafkaTopic);
            });
        } catch (Exception e) {
            logger.error("Exception occurred in submitGroupBooking method : " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        String callBackType = Optional.ofNullable(groupBookingRequest)
                .map(GroupBookingRequest::getRequestDetails)
                .map(RequestDetails::getCallBackType)
                .orElse("");
        boolean isCallToBookReq = Optional.ofNullable(groupBookingRequest)
                .map(GroupBookingRequest::getRequestDetails)
                .map(RequestDetails::isRequestCallBack)
                .orElse(false);
        boolean isListAllPropCall = ListAllProp.equalsIgnoreCase(callBackType);
        boolean isCallBackReq = StringUtils.isNotEmpty(callBackType) ? CallToBook.equalsIgnoreCase(callBackType) : isCallToBookReq;
        boolean isHighValueCall = HighValue.equalsIgnoreCase(callBackType);
        String device = (groupBookingRequest!=null && groupBookingRequest.getDeviceDetails()!=null && StringUtils.isNotEmpty(groupBookingRequest.getDeviceDetails().getBookingDevice()))?groupBookingRequest.getDeviceDetails().getBookingDevice(): EMPTY_STRING;
        return (isCallBackReq || isListAllPropCall || isHighValueCall) ? buildGroupBookingResponse(requestCallBackCount, device) :new GroupBookingResponse();
    }

    public GroupBookingResponse buildGroupBookingResponse(Integer requestCallBackCount, String device) {
        GroupBookingResponse groupBookingResponse = new GroupBookingResponse();
        String key = ConstantsTranslation.CALL_TO_BOOK_REQUEST_SUBMITTED;
        if(CLIENT_DESKTOP.equalsIgnoreCase(device)) {
            if(requestCallBackCount!=null && requestCallBackCount<requestCallbackCountProp){
             groupBookingResponse.setIconUrl(requestCallbackGreenIcon);
            }
            key = ConstantsTranslation.CALL_TO_BOOK_REQUEST_SUBMITTED_DESKTOP;
        }
        groupBookingResponse.setDesc(requestCallBackCount!=null && requestCallBackCount<requestCallbackCountProp ? polyglotService.getTranslatedData(key): polyglotService.getTranslatedData(ConstantsTranslation.CALL_TO_BOOK_REQUEST_LIMIT_EXCEEDED));
        return groupBookingResponse;
    }

    //fetch and convert Hes MatchMaker response for cityOverView api to CG matchMaker Response
    public MatchMakerResponseCG fetchCityOverViewResponse(CityOverviewRequest cityOverviewRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String correlationKey) throws ClientGatewayException {
        try {
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(cityOverviewRequest.getSearchCriteria(), cityOverviewRequest, httpHeaderMap);
            WikiResponse responseHes = getCityOverview(cityOverviewRequest, commonModifierResponse, parameterMap, httpHeaderMap,correlationKey);
            return mobLandingFactory.getResponseService(cityOverviewRequest.getClient())
                    .buildMatchMakerResponse(responseHes);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in CityOverview: " + e.getMessage());
                logger.debug("error occurred in CityOverview: " + e.getMessage(), e);
            } else
                logger.error("error occurred in CityOverview: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public UpsellRateplanResponse fetchUpsellRateplanResponse(SearchHotelsRequest fetchUpsellRateplanRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, String correlationKey) throws ClientGatewayException {
        try {
            long startTime = System.currentTimeMillis();
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(fetchUpsellRateplanRequest.getSearchCriteria(), fetchUpsellRateplanRequest, httpHeaderMap);
            if (fetchUpsellRateplanRequest.getSearchCriteria() != null) {
                utility.setPaginatedToMDC(fetchUpsellRateplanRequest.getSearchCriteria());
                utility.setLoggingParametersToMDC(fetchUpsellRateplanRequest.getSearchCriteria().getRoomStayCandidates(), fetchUpsellRateplanRequest.getSearchCriteria().getCheckIn(),
                        fetchUpsellRateplanRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_COMMON_REQUEST_PROCESS, FETCH_UPSELL_RATEPLAN, System.currentTimeMillis() - startTime);
            SearchWrapperInputRequest searchWrapperInputRequest = searchHotelsFactory.getRequestService(fetchUpsellRateplanRequest.getClient())
                    .convertSearchRequest(fetchUpsellRateplanRequest, commonModifierResponse);
            boolean isCrossSellRequest = crossSellUtil.isCrossSellRequest(fetchUpsellRateplanRequest);
            UpsellRateplanResponse upsellRateplanResponse = null;
            boolean rearchFlow = utility.isRearchFlow(useNewListingService, correlationKey, fetchUpsellRateplanRequest.getExpDataMap());
            if (rearchFlow) {
                return orchListingService.fetchUpsellRatePlanResponse(fetchUpsellRateplanRequest, parameterMap, httpHeaderMap, commonModifierResponse);
            } else {
                ListingPagePersonalizationResponsBO searchWrapperResponseBO = searchHotelsExecutor.searchPersonalizedHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap, isCrossSellRequest);
                upsellRateplanResponse = listingHelper.convertFetchUpsellRateplanresponse(searchWrapperResponseBO, fetchUpsellRateplanRequest, commonModifierResponse);
                if (searchWrapperResponseBO != null) {
                    upsellRateplanResponse.setCurrency(searchWrapperResponseBO.getCurrency());
                }
                return upsellRateplanResponse;
            }

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in fetchUpsellRateplans: " + e.getMessage());
                logger.debug("error occurred in fetchUpsellRateplans: " + e.getMessage(), e);
            } else
                logger.error("error occurred in fetchUpsellRateplans: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private WikiResponse getCityOverview(CityOverviewRequest cityOverviewRequest, CommonModifierResponse commonModifierResponse , Map<String, String[]> parameterMap, Map<String, String> headerMap, String correlationKey) throws ClientGatewayException {
        if(cityOverviewRequest != null && cityOverviewRequest.getDeviceDetails() != null &&
                cityOverviewRequest.getRequestDetails() != null && commonModifierResponse != null){
            String profileType = null;
            if(commonModifierResponse.getExtendedUser() != null){
                profileType = commonModifierResponse.getExtendedUser().getProfileType();
            }
            CityOverviewHesRequest cityOverviewHesRequest = utility.buildCityOverviewHesRequest(cityOverviewRequest,correlationKey,profileType);
            try {
                return listingMapExecutor.fetchCityOverview(cityOverviewHesRequest,parameterMap,headerMap);
            } catch (JsonParseException e) {
                e.printStackTrace();
                logger.debug("Exception Occured in Fetch City overview: ",e);
            }
        }
        return null;
    }

    public SearchHotelsResponse searchHotelsWithCommonResponse(SearchHotelsRequest searchHotelsRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        boolean isCrossSellRequest = crossSellUtil.isCrossSellRequest(searchHotelsRequest);
        try {
            if (utility.isRearchFlow(useNewListingService, searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getRequestId() : "", searchHotelsRequest.getExpDataMap())) {
                return orchListingService.searchHotels(searchHotelsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
            }

            if (searchHotelsRequest.getSearchCriteria() != null) {
                utility.setPaginatedToMDC(searchHotelsRequest.getSearchCriteria());
                utility.setLoggingParametersToMDC(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates(), searchHotelsRequest.getSearchCriteria().getCheckIn(),
                        searchHotelsRequest.getSearchCriteria().getCheckOut());
            }
            SearchWrapperInputRequest searchWrapperInputRequest = searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                    .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            ListingPagePersonalizationResponsBO listingPagePersonalizationResponseBO = null;
            boolean isChainRequest =  searchHotelsRequest.getMatchMakerDetails()!=null && CollectionUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels()) && searchHotelsRequest.getMatchMakerDetails().getHotels().get(0).getPropertyInfo()!=null && StringUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels().get(0).getPropertyInfo().getChainId());
            if (searchHotelsRequest.getSearchCriteria().isNearBySearch()) {
                SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = searchHotelsExecutor.nearByHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
                listingPagePersonalizationResponseBO = listingHelper.convertSearchHotelsToPersonalizedHotels(searchWrapperResponseBO,searchHotelsRequest);
            } else if (searchHotelsRequest.getSearchCriteria().isPersonalizedSearch() || isChainRequest) {
                listingPagePersonalizationResponseBO = searchHotelsExecutor.searchPersonalizedHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap, isCrossSellRequest);
            } else {
                SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = searchHotelsExecutor.searchHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
                listingPagePersonalizationResponseBO = listingHelper.convertSearchHotelsToPersonalizedHotels(searchWrapperResponseBO,searchHotelsRequest);
            }
            return searchHotelsFactory.getResponseService(searchHotelsRequest.getClient())
                    .convertSearchHotelsResponse(listingPagePersonalizationResponseBO,searchHotelsRequest,commonModifierResponse);
        } catch (Exception e) {
            logger.error("Error occurred in searchHotelsWithCommonResponse: " + e.getMessage(), e);
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in searchHotelsWithCommonResponse: " + e.getMessage());
                logger.debug("error occurred in searchHotelsWithCommonResponse: " + e.getMessage(), e);
            } else
                logger.error("error occurred in searchHotelsWithCommonResponse: " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public Future<SearchHotelsResponse> executeSearchHotelsAsync(HttpServletRequest httpServletRequest, String client, SearchHotelsRequest searchHotelsRequest, String correlationKey, Tuple<String, Map<String, String>> tup, CommonModifierResponse commonModifierResponse) {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        return listingThreadPool.submit(() -> {
            long startTime = System.currentTimeMillis();
            try {
                //logger.warn("listingThreadPool searchHotels activeCount : " + listingThreadPool.getActiveCount());
                logger.warn("listingThreadPool searchHotels activeCount : " + listingThreadPool.getActiveCount() + " "
                        + listingThreadPool.getThreadPoolExecutor().getQueue().remainingCapacity() + " "
                        + listingThreadPool.getThreadPoolExecutor().getLargestPoolSize() + " "
                        + listingThreadPool.getThreadPoolExecutor().getTaskCount() + " "
                        + listingThreadPool.getThreadPoolExecutor().getCompletedTaskCount());
                if (mdcMap != null) {
                    MDC.setContextMap(mdcMap);
                }
                Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
                Map<String, String> headerMap = tup.getY();
                if (headerMap == null) {
                    headerMap = new HashMap<>();
                }
                return searchHotelsWithCommonResponse(searchHotelsRequest, parameterMap, headerMap, commonModifierResponse);
            } finally {
                logger.warn("Time taken by executeSearchHotelsAsync {} millis", System.currentTimeMillis() - startTime);
                MDC.clear();
            }
        });
    }

    public SearchHotelsResponse executeListing(HttpServletRequest httpServletRequest, String client, SearchHotelsRequest searchHotelsRequest, String correlationKey, Tuple<String, Map<String, String>> tup) throws ClientGatewayException {
        //invalid funnel = corp, gcc, ksa, dayuse, groupbooking
        boolean isInvalidFunnel =  searchHotelsRequest != null &&
                (Utility.isGccOrKsa()
                        || Constants.CORP_ID_CONTEXT.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext())
                        || Constants.FUNNEL_SOURCE_GROUP_BOOKING.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
                        || Constants.FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
                );
        boolean isPaginationCall = searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null && StringUtils.isNotEmpty(searchHotelsRequest.getSearchCriteria().getLastHotelId()) ;
        
        if(isInvalidFunnel ||isPaginationCall){
            Map<String, String> mdcMap = MDC.getCopyOfContextMap();
            if (mdcMap != null) {
                MDC.setContextMap(mdcMap);
            }
            Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
            Map<String, String> headerMap = tup.getY();
            if (headerMap == null) {
                headerMap = new HashMap<>();
            }
            return searchHotels(searchHotelsRequest, parameterMap, headerMap);
        } else {
            return executeSearchHotelsWithCardEngineService(httpServletRequest,client,searchHotelsRequest,correlationKey,tup);
        }
    }

    public SearchHotelsResponse executeSearchHotelsWithCardEngineService(HttpServletRequest httpServletRequest, String client, SearchHotelsRequest searchHotelsRequest, String correlationKey, Tuple<String, Map<String, String>> tup) throws ClientGatewayException {
        long startTime = System.currentTimeMillis();
        try {
            // Step 1: Get CommonModifierResponse once
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, tup.getY());
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_COMMON_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);

            // Step 2: Execute both services in parallel using the same CommonModifierResponse
            Future<SearchHotelsResponse> listingTaskAsync = executeSearchHotelsAsync(httpServletRequest, client, searchHotelsRequest, correlationKey, tup, commonModifierResponse);
            cardEngineService.setCardIdInSearchHotelRequest(searchHotelsRequest);
            Future<String> cardEngineServiceAsync = cardEngineService.executeCardEngineServiceAsync(searchHotelsRequest, correlationKey, tup.getY(), commonModifierResponse);

            // Step 3: Combine responses
            SearchHotelsResponse searchHotelsResponse = null;
            try {
                String cardEngineResponse = cardEngineResponseTransformerFactory
                        .getResponseService(searchHotelsRequest.getRequestDetails().getSubPageContext())
                        .transformCardEngineResponse(cardEngineServiceAsync.get());
                logger.warn("listingThreadPool executeCardEngineServiceAsync responseTime : {}", System.currentTimeMillis() - startTime);
                Object headerSection = gson.fromJson(cardEngineResponse, Object.class);
                searchHotelsResponse = listingTaskAsync.get();
                logger.warn("listingThreadPool searchHotels responseTime : {}", System.currentTimeMillis() - startTime);

                if (searchHotelsResponse != null) {
                    searchHotelsResponse.setHeaderSection(headerSection);
                }

                cardEngineResponseTransformerFactory.getResponseService(searchHotelsRequest.getRequestDetails().getSubPageContext())
                        .updateSearchHotelsResponse(searchHotelsResponse, cardEngineResponse);
            } catch (Throwable e) {
                logger.error("error occurred in SearchHotelsWithCardEngineService: " + e.getMessage(), e);
                throw (ClientGatewayException) e.getCause();
            }

            return searchHotelsResponse;
        } catch (Exception e) {
            logger.error("Error occurred in executeSearchHotelsWithCardEngineService: " + e.getMessage(), e);
            if(e instanceof ClientGatewayException) {
                throw e;
            } else {
                throw new ClientGatewayException(DependencyLayer.CLIENTGATEWAY, ErrorType.UNEXPECTED, "500", e.getMessage());
            }
        }
    }

    public String processCrossSellData(String response, SearchWrapperInputRequest searchWrapperInputRequest) {
        String modifiedResponse = null;
        SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = null;
        try {
            if (StringUtils.isNotEmpty(response)) {
                searchWrapperResponseBO = objectMapperUtil.getObjectFromJson(response, SearchWrapperResponseBO.class, DependencyLayer.ORCHESTRATOR);
                searchWrapperResponseBO.setCrossSellData(crossSellUtil.getCrossSellDataScion(searchWrapperResponseBO.getCityName(), searchWrapperInputRequest, false));
            } else {
                searchWrapperResponseBO = new SearchWrapperResponseBO.Builder().build();
                searchWrapperResponseBO.setCrossSellData(crossSellUtil.getCrossSellDataScion(NULL_CITY, searchWrapperInputRequest, true));
            }
            modifiedResponse = objectMapperUtil.getJsonFromObject(searchWrapperResponseBO, DependencyLayer.CLIENTGATEWAY);
        } catch (Exception ex) {
            logger.error("Exception occurred in processCrossSellData: " + ex.getMessage(), ex);
        }
        return modifiedResponse;
    }

    public String processCrossSellPersuasion(String response){
        String modifiedResponse = null;
        SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = null;
        try {
            if (StringUtils.isNotEmpty(response)) {
                searchWrapperResponseBO = objectMapperUtil.getObjectFromJson(response, SearchWrapperResponseBO.class, DependencyLayer.ORCHESTRATOR);
                if (searchWrapperResponseBO != null && CollectionUtils.isNotEmpty(searchWrapperResponseBO.getHotelList())) {
                    for (SearchWrapperHotelEntityAbridged hotel : searchWrapperResponseBO.getHotelList()) {
                        hotel.setLoyaltyPersuasion(crossSellUtil.modifyHotelPersuasions(hotel));
                    }
                }
            }
            modifiedResponse = objectMapperUtil.getJsonFromObject(searchWrapperResponseBO, DependencyLayer.CLIENTGATEWAY);
        } catch (Exception ex) {
            logger.error("Exception occurred in processCrossSellPersuasion: " + ex.getMessage(), ex);
        }
        return modifiedResponse;
    }

    public void buildingPaxFilters(FetchCollectionResponseV2 fetchCollectionResponseV2, FetchCollectionRequest fetchCollectionRequest) {
        try {
            if (fetchCollectionResponseV2 != null && fetchCollectionRequest != null && MapUtils.isNotEmpty(fetchCollectionRequest.getExpDataMap())
                    && fetchCollectionRequest.getExpDataMap().containsKey(MULTI_ROOM_EXP)) {

                com.mmt.hotels.clientgateway.response.filter.Filter flexibleRoomFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();

                int noOfRoomsRequested = 0, totalGuest = 0, totalAdults = 0, totalChild = 0;

                if (fetchCollectionRequest.getSearchCriteria() != null && CollectionUtils.isNotEmpty(fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates())) {
                    for (RoomStayCandidate roomStayCandidate : fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates()) {
                        if (roomStayCandidate != null) {
                            if (roomStayCandidate.getAdultCount() != null) {
                                totalGuest += roomStayCandidate.getAdultCount();
                                totalAdults += roomStayCandidate.getAdultCount();
                            }
                            if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                                totalGuest += roomStayCandidate.getChildAges().size();
                                totalChild += roomStayCandidate.getChildAges().size();
                            }
                            noOfRoomsRequested += 1;
                        }
                    }
                }

                flexibleRoomFilter.setFilterGroup(EXACT_ROOM_RECOMMENDATION);


                if (EXACT_ROOM_VALUE.equalsIgnoreCase(fetchCollectionRequest.getExpDataMap().get(MULTI_ROOM_EXP))) {
                    flexibleRoomFilter.setFilterValue(polyglotService.getTranslatedData(FLEXIBLE_ROOMS_FILTER_VALUE));
                    flexibleRoomFilter.setSelectedFilterText(polyglotService.getTranslatedData(FLEXIBLE_ROOMS_FILTER_VALUE));
                    flexibleRoomFilter.setTitle(polyglotService.getTranslatedData(EXACT_ROOM_LANDING_TEXT));
                    fetchCollectionResponseV2.setPaxFilters(Arrays.asList(flexibleRoomFilter));
                }

                if (FLEXIBLE_ROOM_VALUE.equalsIgnoreCase(fetchCollectionRequest.getExpDataMap().get(MULTI_ROOM_EXP))) {
                    String filterValue = "";
                    if (noOfRoomsRequested == 1 || totalGuest >= (3 * noOfRoomsRequested)) {
                        filterValue = polyglotService.getTranslatedData(EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_SINGLE);
                    } else if (totalAdults <= (2 * noOfRoomsRequested)) {
                        filterValue = polyglotService.getTranslatedData(EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_MULTI);
                    } else {
                        filterValue = polyglotService.getTranslatedData(EXACT_ROOM_RECOMMENDATION_TITLE_TEXT_SINGLE);
                    }

                    if (StringUtils.isNotEmpty(filterValue)) {
                        filterValue = filterValue.replace("{numRooms}", String.valueOf(noOfRoomsRequested));
                        if (noOfRoomsRequested == 1) {
                            filterValue = filterValue.replace("Rooms", "Room");
                        }
                    }
                    flexibleRoomFilter.setFilterValue(filterValue);
                    flexibleRoomFilter.setSelectedFilterText(filterValue);
                    flexibleRoomFilter.setTitle(polyglotService.getTranslatedData(FLEXIBLE_ROOM_LANDING_TEXT));
                    fetchCollectionResponseV2.setPaxFilters(Arrays.asList(flexibleRoomFilter));
                }
            }
        }catch (Exception e){
            logger.error("Exception occurred while building pax filters: " + e.getMessage(), e);
        }

    }

}
