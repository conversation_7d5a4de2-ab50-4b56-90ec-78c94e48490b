package com.mmt.hotels.clientgateway.util;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.gommt.hotels.orchestrator.enums.TrafficSource;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.RoomDataRequest;
import com.mmt.hotels.clientgateway.businessobjects.RoomDataResponse;
import com.mmt.hotels.clientgateway.businessobjects.IHGInclusionConfig;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.constants.ControllerConstants;
import com.mmt.hotels.clientgateway.constants.DuplicateBookingKey;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.consul.consulHelper.FunnelRange;
import com.mmt.hotels.clientgateway.consul.consulHelper.HighValueCallSupportDetails;
import com.mmt.hotels.clientgateway.consul.MobConfigPropsConsul;
import com.mmt.hotels.clientgateway.consul.properties.ExtraAdultChildInclusionConfig;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.modification.ModifiedGuestCount;
import com.mmt.hotels.clientgateway.request.modification.ModifiedRoomStayCandidate;
import com.mmt.hotels.clientgateway.response.AvailRoomsResponse;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicy;
import com.mmt.hotels.clientgateway.response.BookedCancellationPolicyType;
import com.mmt.hotels.clientgateway.response.BookedInclusion;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.IconType;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.UserSessionData;
import com.mmt.hotels.clientgateway.response.ConsentData;
import com.mmt.hotels.clientgateway.response.DataList;
import com.mmt.hotels.clientgateway.response.HotelPermissions;
import com.mmt.hotels.clientgateway.response.HotelPermissionsInfo;
import com.mmt.hotels.clientgateway.response.dayuse.rooms.DayUsePersuasion;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.moblanding.*;
import com.mmt.hotels.clientgateway.response.rooms.*;
import com.mmt.hotels.clientgateway.response.rooms.MediaData;
import com.mmt.hotels.clientgateway.response.rooms.RatePlan;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.Space;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheet;
import com.mmt.hotels.clientgateway.response.searchHotels.BottomSheetData;
import com.mmt.hotels.clientgateway.restexecutors.HESRestExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchRoomsExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionData;
import com.mmt.hotels.clientgateway.thirdparty.response.PersuasionObject;
import com.mmt.hotels.clientgateway.thirdparty.response.UserName;
import com.mmt.hotels.clientgateway.thirdparty.response.UserPersonalDetail;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResult;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.model.enums.BNPLVariant;
import com.mmt.hotels.model.enums.SectionsType;
import com.mmt.hotels.model.persuasion.peitho.request.hotelDetails.BlackBenefits;
import com.mmt.hotels.model.persuasion.response.Persuasion;
import com.mmt.hotels.model.request.MultiCurrencyInfo;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.*;
import com.mmt.hotels.model.request.UserGlobalInfo;
import com.mmt.hotels.model.request.dayuse.Slot;
import com.mmt.hotels.model.request.payment.DeviceDetails;
import com.mmt.hotels.model.request.payment.UserDetail;
import com.mmt.hotels.model.response.MpFareHoldStatus;
import com.mmt.hotels.model.response.persuasion.SectionFeature;
import com.mmt.hotels.model.response.corporate.DuplicateBookingDetails;
import com.mmt.hotels.model.response.pricing.*;
import com.mmt.hotels.model.response.pricing.CancellationTimeline;
//import com.mmt.hotels.model.response.pricing.RatePlan;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.model.response.staticdata.SleepingInfoArrangement;
import com.mmt.hotels.model.response.txn.CorporateData;
import com.mmt.hotels.model.response.txn.CouponInfo;
import com.mmt.hotels.model.response.txn.CouponStatus;
import com.mmt.hotels.model.response.txn.Hotels;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import com.mmt.hotels.pojo.listing.personalization.BGLinearGradient;
import com.mmt.hotels.pojo.listing.personalization.IconTag;
import com.mmt.hotels.pojo.request.detail.mob.HotelDetailsMobRequestBody;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import com.mmt.scrambler.ScramblerClient;
import com.mmt.scrambler.exception.ScramblerClientException;
import com.mmt.scrambler.utils.HashType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.BEDROOM;
import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static java.lang.Math.ceil;
import static java.lang.Math.max;

import com.mmt.hotels.clientgateway.constants.TrafficSourceConstants;
import static com.mmt.hotels.clientgateway.constants.Constants.CLIENT_DESKTOP;
import static com.mmt.hotels.clientgateway.constants.Constants.BKG_DEVICE_PWA;
import static com.mmt.hotels.clientgateway.constants.Constants.TRAFFIC_SOURCE_SEO;
import static com.mmt.hotels.clientgateway.constants.Constants.TRAFFIC_SOURCE_SEM;

@Component
public class Utility {

    @Value("${consul.enable}")
    private boolean consulFlag;

    @Autowired
    CommonConfigConsul commonConfigConsul;
    private static Gson gson = new Gson();

    @Value("${dot.icon.url}")
    private String dotIconUrl;

    @Value("${red.cross.icon}")
    private String redCrossIconUrl;

    @Value("${green.tick.icon}")
    private String greenTickIcon;

    @Value("${searchrooms.rateplan.name.config}")
    private String ratePlanNameConfig;

    @Value("${searchrooms.rateplan.redesign}")
    private String ratePlanNameConfigRedesign;

    @Value("${mmt.ratings.count.exp.threshold}")
    private int mmtRatingsCountThreshold;


	@Value("${mypartner.location.restricted.icon.url}")
	private String mypartnerRestrictedIcon;
    @Value("${food.dining.icon.veg}")
    private String vegIconUrl;

    @Value("${food.dining.icon.non.veg}")
    private String nonVegIconUrl;

    @Autowired
    protected PersuasionUtil persuasionUtil;

    @Autowired
    SearchRoomsExecutor searchRoomsExecutor;

    @Autowired
    ObjectMapperUtil objectMapperUtil;private Map<String, Map<String, Map<String, String>>> ratePlanNameMap;

    private  Map<String, Map<String, Map<String, String>>> ratePlanNameMapRedesign;

    @Autowired
    private PropertyManager propertyManager;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private DateUtil dateUtil;

    @Autowired
    MobConfigPropsConsul mobConfigPropsConsul;

    @Value("#{'${homestay.bedType.priority.order}'.split(',')}")
    private List<String> bedTypePriorityOrder;

    @Value("${age.qualifying.code}")
    private String ageQualifyingCode;

    @Value("${app.version.android}")
    private String allowedAppVersionAndroid;

    @Value("${app.version.ios}")
    private String allowedAppVersionIOS;

    @Value("${app.version.android.currency}")
    private String allowedAppVersionAndroidCurrency;

    @Value("${app.version.ios.currency}")
    private String allowedAppVersionIOSCurrency;

    @Value("${los.icon.url.avail}")
    private String losIconUrl;

    @Value("${free.kids.inclusion.icon.url}")
    private String freeChildInclusionIcon;

    @Value("#{'${inclusion.list}'.split(',')}")
    private List<String> inclusionList;

    @Value("${inclusion.icon.red.cross}")
    private String inclusionIconRedCross;
    @Value("${inclusion.icon.double.tick.green}")
    private String inclusionIconDoubleTickGreen;

    private int apLimitForInclusionIcons = 2;

    private String requestToCallBackDataConfig;
    private Map<String,BottomSheet> requestToCallBackDataConfigV2;
    private Map<String,BottomSheet> requestToCallBackDataConfigB2C;
    private Map<String,BottomSheet> requestToCallBackDataConfigForHighValue;
    private Map<String, Map<String, String>> requestToCallBackDataMap;

    private String requestToCallhotelIdConfig;

    private Map<String, String> requestToCallHotelIdMap;

    @Autowired
    private UserServiceExecutor userServiceExecutor;

    @Autowired
    private HESRestExecutor hesRestExecutor;

    private Map<String, List<Filter>> preAppliedFiltersCityWise;

    private IHGInclusionConfig ihgInclusionConfig;

    private NumberFormat numberFormatter;

    private static final Logger LOGGER = LoggerFactory.getLogger(Utility.class);

    private static final String PROFILE_TYPE = "CTA";
    private static final String SUB_PROFILE_TYPE = "MYPARTNER";

    private static final Pattern HH_AA_TIME_REGEX = Pattern.compile("((1[0-2]|0?[0-9])\\s*([AaPp][.]*[Mm][.]*))", Pattern.CASE_INSENSITIVE);

    @Value("#{'${business.Identification.Affiliates.list}'.split(',')}")
    private Set<String> businessIdentificationAffiliates;

    @Value("#{'${business.Identification.Segments.list}'.split(',')}")
    private Set<String> businessIdentificationSegments;

    @Value("#{'${flyer.Hydra.SegmentId.list.mmt.intl}'.split(',')}")
    private Set<String> intlFlyerHydraSegmentIds;

    private Map<String, com.mmt.hotels.clientgateway.response.moblanding.CardData> ihCashbackCardConfig;
    private ChatbotInfo chatbotInfoConsul;
    private ChatbotInfo chatBotInfoConsulV2; // config used for travelplex
    private Map<String, HooksData> chatbotInfoMediaV2;
    private ExtraAdultChildInclusionConfig extraAdultChildInclusionConfig;

    public static String addisUgcV2ToURL(String relativeUrl, boolean isUgcV2) {
        if (!StringUtils.isBlank(relativeUrl)) {
            try {
                StringBuilder sb = new StringBuilder(relativeUrl);
                sb.append(Constants.AMP);
                sb.append(Constants.IS_UGC_V2);
                sb.append(Constants.EQUI);
                sb.append(isUgcV2);
                return sb.toString();
            } catch (Exception e) {
                LOGGER.error("Url modification failed. - {}" + e.getMessage());
            }
        }
        return relativeUrl;
    }

    @PostConstruct
    public void init() {
        if(consulFlag){
            apLimitForInclusionIcons = commonConfigConsul.getApLimitForInclusionIcons();
            requestToCallBackDataConfig = commonConfigConsul.getRequestToCallBackData();
            requestToCallBackDataConfigV2 = commonConfigConsul.getRequestToCallBackDataV2();
            requestToCallBackDataConfigB2C = commonConfigConsul.getRequestToCallBackDataB2C();
            requestToCallBackDataConfigForHighValue = commonConfigConsul.getRequestToCallBackDataConfigForHighValue();
            requestToCallBackDataMap = gson.fromJson(requestToCallBackDataConfig, new TypeToken<Map<String,Map<String, String>> >() {
            }.getType());
            requestToCallhotelIdConfig = commonConfigConsul.getRequestToCallHotelIdData();
            requestToCallHotelIdMap = gson.fromJson(requestToCallhotelIdConfig, new TypeToken<Map<String,String> >() {
            }.getType());

            ratePlanNameMap = gson.fromJson(ratePlanNameConfig, new TypeToken<Map<String,Map<String,Map<String,String>>> >() {
            }.getType());
            ratePlanNameMapRedesign = gson.fromJson(ratePlanNameConfigRedesign, new TypeToken<Map<String,Map<String,Map<String,String>>> >() {
            }.getType());
            preAppliedFiltersCityWise = filterTimeBoundPreAppliedFiltersCityWise(commonConfigConsul.getPreAppliedFiltersCityWise());
            ihCashbackCardConfig = commonConfigConsul.getIhCashBackConfig();
            ihgInclusionConfig = commonConfigConsul.getIhgInclusionConfig();
            chatbotInfoConsul = commonConfigConsul.getChatbotInfo();
            chatBotInfoConsulV2 = commonConfigConsul.getChatbotInfoV2();
            chatbotInfoMediaV2 = commonConfigConsul.getChatbotInfoMediaV2();
            extraAdultChildInclusionConfig = commonConfigConsul.getExtraAdultChildInclusionConfig();

        }
        else {
            CommonConfig commonConfig = propertyManager.getProperty("commonConfig", CommonConfig.class);
            apLimitForInclusionIcons = commonConfig.apLimitForInclusionIcons();
            ratePlanNameMap = gson.fromJson(ratePlanNameConfig, new TypeToken<Map<String,Map<String,Map<String,String>>> >() {
            }.getType());
            ratePlanNameMapRedesign = gson.fromJson(ratePlanNameConfigRedesign, new TypeToken<Map<String,Map<String,Map<String,String>>> >() {
            }.getType());
        }

        try {
            // Format the totalCost with commas and without decimals
            numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
            numberFormatter.setMaximumFractionDigits(0); // No decimals
            numberFormatter.setMinimumFractionDigits(0); // Ensure no trailing zeros
        } catch (Exception e) {
            LOGGER.error("Error in initializing Number formatter: ", e);
        }
    }

    /***
     * Suppressing the timeBased filters on the basis of the filterGroup if timeBound
     * @param preAppliedFiltersCityWise
     */
    private Map<String, List<Filter>> filterTimeBoundPreAppliedFiltersCityWise(Map<String, List<Filter>> preAppliedFiltersCityWise) {
        Map<String, List<Filter>> filteredPreAppliedFiltersCityWise = new HashMap<>();
        if (MapUtils.isNotEmpty(preAppliedFiltersCityWise)) {
            preAppliedFiltersCityWise.forEach((k, v) -> {
                if (CollectionUtils.isNotEmpty(v)) {
                    List<Filter> filteredList = new ArrayList<>();
                    for (Filter filter : v) {
                        if (filter != null && filter.getFilterGroup() != null && !filter.getFilterGroup().getTimeBound()) {
                            filteredList.add(filter);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(filteredList)) {
                        filteredPreAppliedFiltersCityWise.put(k, filteredList);
                    }
                }
            });
        }
        return filteredPreAppliedFiltersCityWise;
    }

    public List<Filter> getPreAppliedFiltersForLocationId(SearchHotelsCriteria searchCriteria, LinkedHashMap<String, String> expDataMap) {
        return searchCriteria != null && MapUtils.isNotEmpty(expDataMap) && isExperimentTrue(expDataMap,ExperimentKeys.PRE_APPLIED_FILTERS_ENABLE.getKey()) &&
                StringUtils.isNotEmpty(searchCriteria.getLocationId()) && searchCriteria.isPreAppliedFilter() &&
                MapUtils.isNotEmpty(preAppliedFiltersCityWise) ? preAppliedFiltersCityWise.get(searchCriteria.getLocationId()) : null;
    }

    public void buildSlot(PriceByHotelsRequestBody priceByHotelsRequestBody, SearchCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            Slot slot = new Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            priceByHotelsRequestBody.setSlot(slot);
        }
    }

    public void buildSlot(HotelDetailsMobRequestBody hotelDetailsMobRequestBody, StaticDetailCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            Slot slot = new Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            hotelDetailsMobRequestBody.setSlot(slot);
        }
    }

    public  static String removeSpecialChar(String text){
        if(StringUtils.isNotBlank(text))
            return text.trim().replaceAll("([^A-Za-z0-9])", "_").replaceAll("\\s", "_");
        return null;
    }

    public boolean isCallToBookRequest(GroupBookingRequest groupBookingRequest, UserSessionData userSessionData) {
        return groupBookingRequest!=null && groupBookingRequest.getRequestDetails()!=null
                && groupBookingRequest.getRequestDetails().isRequestCallBack() && userSessionData!=null && userSessionData.getRequestCallBackCount()!=null;
    }

    public static String getcompleteURL(String intialURL, Map<String, String[]> paramsMap, String correlationKey, String transactionKey) {
        if (StringUtils.isBlank(intialURL))
            return intialURL;
        try {
            StringBuilder sb = new StringBuilder(intialURL);
            if (!intialURL.contains(Constants.QUESTION))
                sb.append(Constants.QUESTION);
            else
                sb.append(Constants.AMP);
            sb.append(Constants.CORRELATIONKEY);
            sb.append(Constants.EQUI);
            sb.append(correlationKey);
            sb.append(Constants.AMP);
            sb.append(Constants.TRANSACTION_KEY);
            sb.append(Constants.EQUI);
            sb.append(transactionKey);
            boolean isRegion = false;
            if (MapUtils.isNotEmpty(paramsMap)) {
                for (Map.Entry<String, String[]> entry : paramsMap.entrySet()) {
                    sb.append(Constants.AMP);
                    sb.append(entry.getKey());
                    sb.append(Constants.EQUI);
                    sb.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
                    if (Constants.REGION.equalsIgnoreCase(entry.getKey()))
                        isRegion = true;
                }
            }
            if (!isRegion) {
                sb.append(Constants.AMP);
                sb.append(Constants.REGION);
                sb.append(Constants.EQUI);
                sb.append(Constants.DEFAULT_SITE_DOMAIN);

            }

            return sb.toString();
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Url modification failed. - {}" + e.getMessage());
            return intialURL;
        }
    }

    public static String getcompleteURL(String intialURL, Map<String, String[]> paramsMap, String coRelationKey) {
        if (StringUtils.isBlank(intialURL))
            return intialURL;
        try {
            StringBuilder sb = new StringBuilder(intialURL);
            if (!intialURL.contains(Constants.QUESTION))
                sb.append(Constants.QUESTION);
            else
                sb.append(Constants.AMP);
            sb.append(Constants.CORRELATIONKEY);
            sb.append(Constants.EQUI);
            sb.append(coRelationKey);
            boolean isRegion = false;
            if (MapUtils.isNotEmpty(paramsMap)) {
                for (Map.Entry<String, String[]> entry : paramsMap.entrySet()) {
                    sb.append(Constants.AMP);
                    sb.append(entry.getKey());
                    sb.append(Constants.EQUI);
                    sb.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
                    if (Constants.REGION.equalsIgnoreCase(entry.getKey()))
                        isRegion = true;
                }
            }
            if (!isRegion) {
                sb.append(Constants.AMP);
                sb.append(Constants.REGION);
                sb.append(Constants.EQUI);
                sb.append(Constants.DEFAULT_SITE_DOMAIN);

            }
            return sb.toString();
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Url modification failed. - {}" + e.getMessage());
            return intialURL;
        }
    }

    public static String getCompleteUrl(String url, Map<String, String[]> parameterMap) {
        if (StringUtils.isEmpty(url) || MapUtils.isEmpty(parameterMap)) return url;
        try {
            StringBuilder sb = new StringBuilder(url);
            sb.append(url.contains(Constants.QUESTION) ? Constants.AMP : Constants.QUESTION);

            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                if (entry.getKey().equalsIgnoreCase("profile") || entry.getKey().equals("Region")) {
                    continue;
                }
                sb.append(entry.getKey());
                sb.append(Constants.EQUI);
                sb.append(URLEncoder.encode(entry.getValue()[0], "UTF-8"));
                sb.append(Constants.AMP);
            }


            if (sb.toString().endsWith(Constants.AMP)) {
                sb.setLength(sb.length() - Constants.AMP.length());
            }
            return sb.toString();
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("Url modification failed {}", e.getMessage());
            return url;
        }
    }

    public static Map<String, String> getHeaderMap(Map<String, String> headers) {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Content-Type", "application/json");
        headerMap.put("Accept-Encoding", "gzip");
        headerMap.put("srcRequest", "ClientGateway");
        if (StringUtils.isNotEmpty(headers.get("mmt-auth")))
            headerMap.put("mmt-auth", headers.get("mmt-auth"));
        String akmaiHeader = headers.get(Constants.HEADER_AKAMAI);
        if (StringUtils.isEmpty(akmaiHeader))
            akmaiHeader = headers.get(Constants.HEADER_AKAMAI.toLowerCase());

        if (StringUtils.isNotEmpty(akmaiHeader)) {
            headerMap.put(Constants.HEADER_AKAMAI.toLowerCase(), akmaiHeader);
        }
        return headerMap;
    }

    public static Double round(double value, int precision) {
        int scale = (int) Math.pow(10, precision);
        return (double) Math.round(value * scale) / scale;
    }

    public static String getHotelierCurrency(PersistedMultiRoomData persistedMultiRoomData) {
        String supplierCurrency = "INR";
        if (null != persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails()
                && StringUtils.isNotBlank(persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0)
                .getSupplierDetails().getHotelierCurrencyCode()))
            supplierCurrency = persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getSupplierDetails()
                    .getHotelierCurrencyCode();
        return supplierCurrency;
    }

    public static String getAskedCurrency(PersistedMultiRoomData persistedMultiRoomData) {
        String askedCurrency = "INR";
        if (null != persistedMultiRoomData.getAvailReqBody() && StringUtils.isNotBlank(persistedMultiRoomData.getAvailReqBody().getCurrency()))
            askedCurrency = persistedMultiRoomData.getAvailReqBody().getCurrency();
        return askedCurrency;
    }

    public static String getTextBasedUponCurrency(String priceText, String currencySymbol, Double amount) {
        if (StringUtils.isNotEmpty(priceText)) {
            priceText = priceText.replace(Constants.CURRENCY_SYMBOL, currencySymbol);
            priceText = priceText.replace(Constants.AMOUNT, amount.toString());
            return priceText;
        }
        return Constants.EMPTY_STRING;
    }

    public static double getHotelierConversionFactor(PersistedMultiRoomData persistedMultiRoomData) {
        return null != persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getDisplayFare() ?
                persistedMultiRoomData.getHotelList().get(0).getTariffInfoList().get(0).getDisplayFare().getConversionFactor()
                : 1.0;
    }

    /**
     * Total adult count
     * @param roomStayCandidates
     * @return
     */
    public Integer getTotalAdultsFromRequest(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        int adultCount = 0;
        if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
            for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                adultCount += (roomStayCandidate!=null && roomStayCandidate.getAdultCount()!=null?roomStayCandidate.getAdultCount():0);
            }
        }
        return adultCount;
    }

    /**
     * * Total child count
     * @param roomStayCandidates
     * @return
     */
    public Integer getTotalChildrenFromRequest(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        int childCount = 0;
        if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
            for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                if (roomStayCandidate!=null && CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                    childCount += roomStayCandidate.getChildAges().size();
                }
            }
        }
        return childCount;
    }
    public Pair<Integer, Integer> getTotalAdultsAndChildFromBKGModRequest(List<ModifiedRoomStayCandidate> roomStayCandidates) {
        int adultCount = 0;
        int childCount = 0;
        try{
            if(CollectionUtils.isNotEmpty(roomStayCandidates)){
                for(ModifiedRoomStayCandidate roomStayCandidate : roomStayCandidates){
                    if(CollectionUtils.isNotEmpty(roomStayCandidate.getGuestCountList())){
                        for(ModifiedGuestCount modifiedGuestCount : roomStayCandidate.getGuestCountList()){
                            adultCount += modifiedGuestCount.getAdultCount();
                            childCount += (CollectionUtils.isNotEmpty(modifiedGuestCount.getChildAges())?modifiedGuestCount.getChildAges().size():0);
                        }
                    }
                }
            }
        }catch (Exception ex){
            LOGGER.debug("Error While build Adult & Child Count from BKG Request!",ex);
        }
        return new ImmutablePair<>(adultCount,childCount);
    }


    public static String buildRoomStayQualifierFromRoomStayCandidates(@Nullable List<RoomStayCandidate> roomStayCandidates, boolean tildeRequiredInRSQ) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }

        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            if (CollectionUtils.isEmpty(roomStayCandidate.getGuestCounts()))
                continue;
            for (GuestCount guestCount: roomStayCandidate.getGuestCounts()) {
                if (guestCount == null)
                    continue;
                int adultCount = Integer.parseInt(guestCount.getCount());
                int childCount = 0;
                if (CollectionUtils.isNotEmpty(guestCount.getAges()))
                    childCount = guestCount.getAges().size();
                builder.append(adultCount);
                builder.append(Constants.RSQ_SPLITTER);
                builder.append(childCount);
                builder.append(Constants.RSQ_SPLITTER);
                if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                    for (int age : guestCount.getAges()) {
                        builder.append("1");
                        builder.append(Constants.RSQ_SPLITTER);
                    }
                }
                if (tildeRequiredInRSQ)
                    builder.append(Constants.RSQ_ROOM_SPLITTER);
            }
        }

        String roomStayQualifier = builder.toString();
        if (roomStayQualifier.length() > 0) {
            return roomStayQualifier.substring(0, roomStayQualifier.length() - 1);
        }
        return roomStayQualifier;
    }

    // HTL-42803: TO-DO remove boolean isBnplOneVariant node once BNPLVariant Enum changes are completely live.
    public BookedCancellationPolicy transformCancellationPolicy(List<CancelPenalty> cancelPenalty, boolean bnplApplicable, boolean isBnplOneVariant, BNPLVariant bnplVariant, String confirmationPolicyType, String cancellationPolicyNrNonInstantText, Integer ap, Optional<MpFareHoldStatus> mpFareHoldStatus, String partialRefundText, boolean isThankyouV2) {
        BookedCancellationPolicy bookedCancellationPolicy = new BookedCancellationPolicy();
        boolean isFC = false;
        boolean isPC = false;
        boolean isCancelRulesRequired = true;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cancelPenalty) && null != cancelPenalty.get(0).getCancellationType()) {
            if(CancelPenalty.CancellationType.FREE_CANCELLATON.equals(cancelPenalty.get(0).getCancellationType())){
                isFC = true;
            }else if(CancelPenalty.CancellationType.PARTIAL_REFUNDABLE.equals(cancelPenalty.get(0).getCancellationType())){
                isPC = true;
            }
        }
        bookedCancellationPolicy.setIconUrl((isFC || isPC)?greenTickIcon:redCrossIconUrl);
        bookedCancellationPolicy.setIconUrlV2((isFC || isPC) ? inclusionIconDoubleTickGreen : inclusionIconRedCross);
        if (isFC) {
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setText(cancelPenalty.get(0).getFreeCancellationText());
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);
            if (bnplApplicable) {
                if (!Utility.isGccOrKsa()) {
                    if (isBnplOneVariant || BNPLVariant.BNPL_AT_1 == bnplVariant) {
                        LOGGER.debug("Reading bnplVariant: {} enum.", bnplVariant);
                        bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT));
                    } else if (BNPLVariant.BNPL_AT_0 == bnplVariant) {
                        LOGGER.debug("Reading bnplVariant: {} enum.", bnplVariant);
                        bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT));
                    }
                } else {
                    bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT));
                }
            }
            else if(mpFareHoldStatus.isPresent() && mpFareHoldStatus.get().getExpiry() >0){
                final MpFareHoldStatus fareHoldStatus= mpFareHoldStatus.get();
                if (fareHoldStatus.getBookingAmount() == 1) {
                    LOGGER.debug("Fare Hold version: {}", fareHoldStatus.getBookingAmount());
                    bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_NEW_VARIANT));
                } else if (fareHoldStatus.getBookingAmount() == 0) {
                    LOGGER.debug("Fare Hold version: {}", fareHoldStatus.getBookingAmount());
                    bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCEL_POLICY_BNPL_SUBTEXT_ZERO_VARIANT));
                }
            }
        } else if(isPC){
            bookedCancellationPolicy.setIconType(IconType.DOUBLETICK);
            bookedCancellationPolicy.setText(partialRefundText);
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.FC);
        }else {
            String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
            if ((ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller) || ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller)
                    || ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller))
                    && ap!=null && ap < apLimitForInclusionIcons) {
                bookedCancellationPolicy.setIconType(IconType.DEFAULT);
            } else {
                bookedCancellationPolicy.setIconType(IconType.BIGCROSS);
            }
            String amendablePolicyText = null;
            if (CollectionUtils.isNotEmpty(cancelPenalty) && cancelPenalty.get(0) != null && cancelPenalty.get(0).getAmendmentPolicies() != null &&
                    DATE.equalsIgnoreCase(cancelPenalty.get(0).getAmendmentPolicies().getName())) {
                LOGGER.debug("PolicyName: {} and MetaData: {}", cancelPenalty.get(0).getAmendmentPolicies().getName(), cancelPenalty.get(0).getAmendmentPolicies().getMetaData());
                amendablePolicyText = getAmendableTextForNRPolicy(cancelPenalty.get(0).getAmendmentPolicies(), controller);
            }
            String nonRefundableText, nonRefundableTextDateChange;
            if(isThankyouV2) {
                nonRefundableText = polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT_V2);
                nonRefundableTextDateChange = polyglotService.getTranslatedData(NON_REFUNDABLE_TEXT_DATE_CHANGE_V2);
            }else{
                nonRefundableText = polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT);
                nonRefundableTextDateChange = polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_TEXT_DATE_CHANGE);
            }
            if (ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller) && StringUtils.isNotEmpty(amendablePolicyText)) {
                isCancelRulesRequired = false;

                String text = nonRefundableText + BUT_SEPARATOR + amendablePolicyText.toLowerCase();
                bookedCancellationPolicy.setText(text);
            } else if (ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller) && StringUtils.isNotEmpty(amendablePolicyText)) {
                String text = nonRefundableTextDateChange;
                bookedCancellationPolicy.setText(text);
            } else {
                bookedCancellationPolicy.setText(nonRefundableText);
            }
            if (Constants.CTRIP_NONINS.equalsIgnoreCase(confirmationPolicyType)) {
                bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.CANCELLATION_POLICY_NR_NON_INSTANT_TEXT));
            } else {
                if (StringUtils.isNotEmpty(amendablePolicyText)) {
                    bookedCancellationPolicy.setSubText(getAmendableSubtext(amendablePolicyText));
                } else {
                    bookedCancellationPolicy.setSubText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT));
                }
            }
            bookedCancellationPolicy.setType(BookedCancellationPolicyType.NR);
        }

        if (isCancelRulesRequired && CollectionUtils.isNotEmpty(cancelPenalty) && cancelPenalty.get(0).getCancelRules() != null)
            bookedCancellationPolicy.setCancelRules(cancelPenalty.get(0).getCancelRules());

        return bookedCancellationPolicy;
    }

    public String getAmendableSubtext(String amendablePolicyText) {
        String amendSubText = "<font color=\"#4A4A4A\">• " + polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT)
                + "<br/>• " + amendablePolicyText + "</font>";
        return amendSubText;
    }

    public String getAmendableTextForNRPolicy(AmendmentPolicies amendmentPolicies, String controller) {
        try {
            String text = polyglotService.getTranslatedData(AMENDABLE_TY_TEXT);
            if (ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller)) {
                text = polyglotService.getTranslatedData(AMENDABLE_DETAIL_TEXT);
            }
            if (ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller)) {
                text = polyglotService.getTranslatedData(AMENDABLE_REVIEW_TEXT);
            }
            text = text.replace("{amendType}", amendmentPolicies.getName());
            String durationText = "";
            durationText = durationText + amendmentPolicies.getMetaData() + " hrs";
            text = StringUtils.isNotEmpty(durationText) ? text.replace("{duration}", durationText) : null;
            return text;
        } catch (Exception ex) {
            LOGGER.warn("Error in processing amendablePolicy meta-info: {}", ex.getMessage());
        }
        return null;
    }

    public List<BookedInclusion> transformInclusions(List<MealPlan> mealPlan, List<Inclusion> inclusionList,
                                                     Map<String, String> mealPlanMap, String supplierCode, Integer ap,
                                                     ExtraGuestDetail extraGuestDetail, Map<String, String> experimentDataMap,
                                                     boolean isAltAcco, String freeChildText, String hotelChainCode,
                                                     Map<String, String> htlAttributes, List<Integer> childAges,
                                                     String countryCode, boolean isMealAvailableAtProperty, IconType iconType, boolean isMealTransformSubTextRequired,
                                                     boolean showExtraAdultChildInclusion) {
        List<BookedInclusion> inclusions = new ArrayList<>();

        boolean isMealPlanPresent = false;
        int count = 0;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(inclusionList)) {
            for (Inclusion inclusion : inclusionList) {
                if (StringUtils.isEmpty(inclusion.getValue()))
                    continue;
                count++;
                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setCode(inclusion.getCode());
                String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
                if (ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
                    bookedInclusion.setSubText(inclusion.getValue());
                bookedInclusion.setText(inclusion.getCode());
                bookedInclusion.setIconType(IconType.DEFAULT);
                bookedInclusion.setTrailingCtaType(inclusion.getTrailingCtaType());
                bookedInclusion.setTrailingCtaText(inclusion.getTrailingCtaText());
                bookedInclusion.setIconUrl(getDotIconUrl(experimentDataMap, inclusion.getIconUrl()));
                bookedInclusion.setSegmentIdentifier(inclusion.getSegmentIdentifier());
                if (count < 2 && !isMealPlanPresent && CollectionUtils.isNotEmpty(mealPlan)) {
                    if (Constants.MEAL_PLAN_CODE_BREAKFAST.equalsIgnoreCase(mealPlan.get(0).getCode()))
                        isMealPlanPresent = true;
                    else if (!Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                            && !Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                            &&  !Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode()))
                        isMealPlanPresent = true;
                }
                if (((ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller) || ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
                        && ("Packages".equalsIgnoreCase(inclusion.getCategory()) || "Packages1".equalsIgnoreCase(inclusion.getCategory())
                        || "Packages2".equalsIgnoreCase(inclusion.getCategory()) || "Packages3".equalsIgnoreCase(inclusion.getCategory())
                        || "MMTBLACK".equalsIgnoreCase(inclusion.getCategory()))) || (BLACK_SEGMENT_IDENTIFIER.equalsIgnoreCase(inclusion.getSegmentIdentifier()) && MapUtils.isNotEmpty(experimentDataMap) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.BLACK_REVAMP.getKey())))) {
                    bookedInclusion.setIconUrl(inclusion.getImageURL());
                }

                if(LOS.equalsIgnoreCase(inclusion.getInclusionType())){
                    bookedInclusion.setIconUrl(losIconUrl);
                    bookedInclusion.setInclusionCode(Constants.LONGSTAY);
                }
                inclusions.add(bookedInclusion);
            }
        }
        if (isExperimentTrue(experimentDataMap, ExperimentKeys.MEAL_CLARITY.getKey()) && isMealAvailableAtProperty) {
            // When MEAL_CLARITY is true and isMealAvailableAtProperty, Do nothing, Meal inclusion will come from HES

        } else if (!isExperimentValid(experimentDataMap, Constants.FOOD_DINING_REVAMP, 0) && isMealAvailableAtProperty) {
            buildMealsATProperty(mealPlan, inclusions, true, iconType, isMealTransformSubTextRequired);

        } else if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mealPlan)
                && !(MapUtils.isNotEmpty(experimentDataMap) && StringUtils.isNotBlank(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)) && Constants.TRUE.equalsIgnoreCase(experimentDataMap.get(Constants.NO_MEAL_INCLUSION_REMOVE)))) {
            String controller = MDC.get(MDCHelper.MDCKeys.CONTROLLER.getStringValue());
            if (Constants.MEAL_PLAN_CODE_ROOM_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                    || Constants.MEAL_PLAN_CODE_BED_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                    || Constants.MEAL_PLAN_CODE_ACC_ONLY.equalsIgnoreCase(mealPlan.get(0).getCode())
                    && mealPlanMap.containsKey(mealPlan.get(0).getCode())) {
                BookedInclusion noMeanInclusion = new BookedInclusion();
                if(mealPlanMap != null)
                    noMeanInclusion.setText(polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())));
                noMeanInclusion.setCode(noMeanInclusion.getText());
                if ((ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller) || ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller)
                        || ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller))
                        && ap!=null && ap < apLimitForInclusionIcons) {
                    noMeanInclusion.setIconUrl(isReorderInclusions(experimentDataMap)?dotIconUrl:freeChildInclusionIcon);
                    noMeanInclusion.setIconType(IconType.DEFAULT);
                } else {
                    if(isReorderInclusions(experimentDataMap)) {
                        noMeanInclusion.setIconUrl(redCrossIconUrl);
                    }
                    noMeanInclusion.setIconType(IconType.CROSS);
                }
                if (ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
                    noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
                noMeanInclusion.setIconUrl(getDotIconUrl(experimentDataMap, noMeanInclusion.getIconUrl()));
                inclusions.add(0, noMeanInclusion);

            } else if (!isMealPlanPresent && StringUtils.isNotBlank(supplierCode) && !Constants.SUPPLIER_INGO.equalsIgnoreCase(supplierCode)) {
                BookedInclusion noMeanInclusion = new BookedInclusion();
                noMeanInclusion.setText(mealPlanMap.containsKey(mealPlan.get(0).getCode()) ? polyglotService.getTranslatedData(mealPlanMap.get(mealPlan.get(0).getCode())) : mealPlan.get(0).getValue());
                noMeanInclusion.setCode(noMeanInclusion.getText());
                noMeanInclusion.setIconType(IconType.DEFAULT);
                if (ControllerConstants.REVIEW_ROOM_INFOS.equalsIgnoreCase(controller))
                    noMeanInclusion.setIconUrl("https://promos.makemytrip.com/Hotels_product/Hotel_SR/Android/drawable-hdpi/meal.png");
                noMeanInclusion.setIconUrl(getDotIconUrl(experimentDataMap, noMeanInclusion.getIconUrl()));
                inclusions.add(0, noMeanInclusion);
            }
        }

        if (!isAltAcco && extraGuestDetail != null && StringUtils.isNotEmpty(extraGuestDetail.getReviewPageExtraBedText())) {
            BookedInclusion bookedInclusion = new BookedInclusion();
            bookedInclusion.setText(extraGuestDetail.getReviewPageExtraBedText());
            bookedInclusion.setCode(extraGuestDetail.getReviewPageExtraBedText());
            bookedInclusion.setIconType(IconType.DEFAULT);
            bookedInclusion.setIconUrl(Constants.INCLUSIONS_DEFAULT_DOT_ICON_URL);
            inclusions.add(bookedInclusion);
        }
        if (StringUtils.isNotEmpty(freeChildText)) {
            BookedInclusion freeChildInclusion = getFreeChildInclusion(freeChildText,isReorderInclusions(experimentDataMap)?dotIconUrl:freeChildInclusionIcon);
            inclusions.add(0, freeChildInclusion);
        }
        BookedInclusion ihgPaidInclusion = getIHGPaidChildInclusionIfApplicable(
                hotelChainCode, htlAttributes, childAges, countryCode);
        if (ihgPaidInclusion != null) {
            inclusions.add(0, ihgPaidInclusion);
        }

        List<String> mealPlanCodesList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mealPlan)) {
            mealPlanCodesList = mealPlan.stream().map(MealPlan::getCode).collect(Collectors.toList());
        }
        boolean isExtraAdultChildInclusionEnabled = isExtraAdultChildInclusionExperimentEnabled(experimentDataMap) && showExtraAdultChildInclusion;
        boolean isDomesticHotel = Constants.DH_COUNTRY_CODE.equalsIgnoreCase(countryCode);
        if (isExtraAdultChildInclusionEnabled && showExtraAdultChildInclusion(mealPlanCodesList, supplierCode, extraAdultChildInclusionConfig, isDomesticHotel)) {
            BookedInclusion extraAdultChildInclusion = buildExtraAdultChildInclusion(extraAdultChildInclusionConfig);
            if (null != extraAdultChildInclusion) {
                inclusions.add(0, extraAdultChildInclusion);
            }
        }
        return inclusions;
    }

    public BookedInclusion buildExtraAdultChildInclusion(ExtraAdultChildInclusionConfig extraAdultChildInclusionConfig) {
        String inclusionText = polyglotService.getTranslatedData(ConstantsTranslation.INCLUDED_FOR);

        if (StringUtils.isBlank(inclusionText)) {
            return null;
        }

        return BookedInclusion.builder()
                .code(Constants.EXTRA_ADULT_CHILD)
                .iconType(IconType.DEFAULT)
                .text(inclusionText)
                .iconUrl(extraAdultChildInclusionConfig.getIconUrl())
                .onOffer(false)
                .bookable(false)
                .packageBenefit(false)
                .showOnSelectRoom(false)
                .styleClasses(extraAdultChildInclusionConfig.getStyleClasses())
                .build();
    }

    public void buildMealsATProperty(List<MealPlan> mealPlan, List<BookedInclusion> inclusions, Boolean appendAtLast, IconType iconType, Boolean isMealTransformSubTextRequired) {
        if(CollectionUtils.isNotEmpty(mealPlan) &&
                Objects.nonNull(mealPlan.get(0)) &&
                Objects.nonNull(mealPlan.get(0).getTrailingCtaText()) &&
                Objects.nonNull(mealPlan.get(0).getTrailingCtaBottomSheet())) {
            com.mmt.hotels.model.response.pricing.TrailingCtaBottomSheet trailingIconBottomSheetHES = mealPlan.get(0).getTrailingCtaBottomSheet();
            BookedInclusion mealsAtProperty = new BookedInclusion();
            //Have to send value to UI in case of code
            mealsAtProperty.setCode(mealPlan.get(0).getValue());
            mealsAtProperty.setIconType(iconType);
            if (isMealTransformSubTextRequired) {
                mealsAtProperty.setSubText(trailingIconBottomSheetHES.getSubHeading());
            }
            mealsAtProperty.setText(mealPlan.get(0).getValue());
            mealsAtProperty.setIconUrl(FOOD_DINING_TRAILING_ICON_URL); //Have to add on consul
            mealsAtProperty.setTrailingCtaText(mealPlan.get(0).getTrailingCtaText());

            com.mmt.hotels.clientgateway.response.TrailingCtaBottomSheet trailingIconBottomSheet = new com.mmt.hotels.clientgateway.response.TrailingCtaBottomSheet();
            trailingIconBottomSheet.setHeading(trailingIconBottomSheetHES.getHeading());
            trailingIconBottomSheet.setSubHeading(trailingIconBottomSheetHES.getSubHeading());
            List<com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature> sectionFeatureList = new ArrayList<>();
            for(SectionFeature sectionFeatureHes : trailingIconBottomSheetHES.getSectionFeatures()) {
                if (Objects.nonNull(sectionFeatureHes)) {
                    com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature sectionFeatureCG = new com.mmt.hotels.clientgateway.response.searchHotels.SectionFeature();
                    sectionFeatureCG.setIconUrl(sectionFeatureHes.getIconUrl());
                    sectionFeatureCG.setText(sectionFeatureHes.getText());
                    sectionFeatureList.add(sectionFeatureCG);
                }
            }
            trailingIconBottomSheet.setSectionFeatures(sectionFeatureList);
            mealsAtProperty.setTrailingCtaBottomSheet(trailingIconBottomSheet);
            if(appendAtLast){
                inclusions.add(mealsAtProperty);
            } else {
                inclusions.add(0, mealsAtProperty);
            }
        }
    }

    public String getMMTAuth(Map<String, String> httpHeaderMap, String bookingDevice) {
        String mmtAuth = null;
        if ("ANDROID".equalsIgnoreCase(bookingDevice)) {
            String androidAuth = httpHeaderMap.get("backup_auth");
            if (!StringUtils.isEmpty(androidAuth) && androidAuth.indexOf("mmtAuth") > -1)
                mmtAuth = androidAuth.substring(androidAuth.indexOf("mmtAuth") + 9, androidAuth.length() - 1);
        }
        if(StringUtils.isBlank(mmtAuth)) {
            mmtAuth= httpHeaderMap.get("mmt-auth");
        }

        return mmtAuth;
    }

    public UserSessionData addUserServiceDataToRequest(GroupBookingRequest groupBookingRequest, Map<String, String> httpHeaderMap, String correlationKey, boolean isCallBackReq) throws ClientGatewayException {
        String mmtAuth = httpHeaderMap.get("mmt-auth");
        if(groupBookingRequest!=null && groupBookingRequest.getDeviceDetails()!=null && StringUtils.isNotEmpty(groupBookingRequest.getDeviceDetails().getBookingDevice())) {
            mmtAuth = getMMTAuth(httpHeaderMap, groupBookingRequest.getDeviceDetails().getBookingDevice());
        }
        UserServiceResponse userServiceResponse = userServiceExecutor.getUserServiceResponse(mmtAuth, null , null, null,  correlationKey, groupBookingRequest.getRequestDetails().getIdContext(), httpHeaderMap.get("region"), null, httpHeaderMap);
        String hotelName = null;
        if (MapUtils.isNotEmpty(groupBookingRequest.getInputFields()) && groupBookingRequest.getInputFields().get("hotelId") != null
                && groupBookingRequest.getInputFields().get("hotelId").getFieldValue() != null) {
            RoomDataRequest roomDataRequest = new RoomDataRequest();
            roomDataRequest.setHotelId(groupBookingRequest.getInputFields().get("hotelId").getFieldValue().get(0));
            roomDataRequest.setCorrelationKey(correlationKey);
            RoomDataResponse roomDataResponse = searchRoomsExecutor.getRoomData(roomDataRequest, httpHeaderMap, correlationKey);
            if (roomDataResponse != null && StringUtils.isNotEmpty(roomDataResponse.getHotelName())) {
                hotelName = roomDataResponse.getHotelName();
            } else {
                hotelName = MapUtils.isNotEmpty(requestToCallHotelIdMap) ? requestToCallHotelIdMap.get(groupBookingRequest.getInputFields().get("hotelId").getFieldValue().get(0)): null;
            }
        }
        if(userServiceResponse == null || userServiceResponse.getResult() == null || userServiceResponse.getResult().getExtendedUser() == null) {
            UserServiceResponse userServiceResponseLoggedout = LoggedoutUserServiceResponse(groupBookingRequest);
            if (userServiceResponseLoggedout == null || userServiceResponseLoggedout.getResult() == null) {
                throw new ClientGatewayException(DependencyLayer.USERSERVICE, ErrorType.AUTHENTICATION, AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());
            } else {
                LOGGER.debug("User is loggedout, proceeding with Loggedout user data.");
                userServiceResponse = userServiceResponseLoggedout;
            }
        }
        Optional<Field> optionalHotelIdField = Optional.ofNullable(groupBookingRequest)
                .map(GroupBookingRequest::getInputFields)
                .map(inputFields -> inputFields.get("hotelId"));

        String hotelId = optionalHotelIdField
                .map(Field::getFieldValue)
                .filter(fieldValues -> !fieldValues.isEmpty())
                .map(fieldValues -> fieldValues.get(0))
                .orElse(null);
        Optional<UserServiceResponse> optionalUserServiceResponse = Optional.ofNullable(userServiceResponse);

        Optional<UserName> optionalName = optionalUserServiceResponse
                .map(UserServiceResponse::getResult)
                .map(UserServiceResult::getExtendedUser)
                .map(ExtendedUser::getPersonalDetails)
                .map(UserPersonalDetail::getName);

        String firstName = optionalName.map(UserName::getFirstName).orElse(null);
        String middleName = optionalName.map(UserName::getMiddleName).orElse(null);
        String lastName = optionalName.map(UserName::getLastName).orElse(null);

        StringBuilder fullName = firstName != null ? new StringBuilder(firstName) : new StringBuilder();
        if (middleName != null) {
            fullName.append(SPACE).append(middleName);
        }
        if(lastName != null) {
            fullName.append(SPACE).append(lastName);
        }
        groupBookingRequest.getInputFields().put("fullName", buildFullNameField(fullName.toString()));
        groupBookingRequest.getInputFields().put("hotelName", buildHotelNameField(hotelName));
        groupBookingRequest.getInputFields().put("detailDeepLinkUrl", buildDetailDeepLinkUrlField(groupBookingRequest.getDetailDeepLinkUrl()));
        if(groupBookingRequest!=null && groupBookingRequest.getSearchCriteria()!=null && StringUtils.isNotEmpty(groupBookingRequest.getSearchCriteria().getCurrency())) {
            groupBookingRequest.getInputFields().put("currency", buildCurrencyField(groupBookingRequest.getSearchCriteria().getCurrency()));
        }
        return updateRequestCallBackData(groupBookingRequest.getCorrelationKey(),hotelId, userServiceResponse, isCallBackReq);
    }

    private UserServiceResponse LoggedoutUserServiceResponse(GroupBookingRequest groupBookingRequest) {
        UserServiceResponse userServiceResponseLoggedOut = null;
        if (groupBookingRequest.getDeviceDetails() != null && StringUtils.isNotEmpty(groupBookingRequest.getDeviceDetails().getDeviceId())
                && groupBookingRequest.getInputFields() != null && groupBookingRequest.getInputFields().get("contactNo") != null
                && CollectionUtils.isNotEmpty(groupBookingRequest.getInputFields().get("contactNo").getFieldValue())
                && StringUtils.isNotEmpty(groupBookingRequest.getInputFields().get("contactNo").getFieldValue().get(0))) {
            String id = groupBookingRequest.getDeviceDetails().getDeviceId();
            userServiceResponseLoggedOut = new UserServiceResponse();
            UserServiceResult userServiceResult = new UserServiceResult();
            ExtendedUser loggoutUser = new ExtendedUser();
            loggoutUser.setUuid(id);
            loggoutUser.setProfileType("Personal");
            UserPersonalDetail personalDetails = new UserPersonalDetail();
            UserName loggedoutUserName = new UserName();
            loggedoutUserName.setFirstName("Loggedout");
            loggedoutUserName.setLastName("User");
            personalDetails.setName(loggedoutUserName);
            loggoutUser.setPersonalDetails(personalDetails);
            String dummyEmail = groupBookingRequest.getInputFields().get("contactNo").getFieldValue().get(0) + "@loggedoutuser.com";
            List<String> emailList = new ArrayList<>();
            emailList.add(dummyEmail);
            if (groupBookingRequest.getInputFields().get("email") != null) {
                groupBookingRequest.getInputFields().get("email").setFieldValue(emailList);
            }
            loggoutUser.setPrimaryEmailId(dummyEmail);
            userServiceResult.setExtendedUser(loggoutUser);
            userServiceResponseLoggedOut.setResult(userServiceResult);
        }
        return userServiceResponseLoggedOut;
    }

    private UserSessionData updateRequestCallBackData(String correlationKey , String hotelId, UserServiceResponse userServiceResponse, boolean isCallBackReq) throws ClientGatewayException {

        Optional<UserServiceResponse> optionalUserServiceResponse = Optional.ofNullable(userServiceResponse);

        String uuid = optionalUserServiceResponse
                .map(UserServiceResponse::getResult)
                .map(UserServiceResult::getExtendedUser)
                .map(ExtendedUser::getUuid)
                .orElse(null);

        return isCallBackReq ? hesRestExecutor.updateRequestCallBackData(correlationKey, uuid): null;
    }

   private Field buildFullNameField(String fullName) {
        Field field = new Field();
        field.setFieldName("fullName");
        field.setFieldValue(Arrays.asList(fullName));
        return field;
    }

    private Field buildHotelNameField(String hotelName) {
        Field field = new Field();
        field.setFieldName("hotelName");
        field.setFieldValue(Arrays.asList(hotelName));
        return field;
    }

    private Field buildDetailDeepLinkUrlField(String detailDeepLinkUrl) {
        Field field = new Field();
        field.setFieldName(Constants.detailDeepLinkUrl);
        field.setFieldValue(StringUtils.isNotEmpty(detailDeepLinkUrl) ? Arrays.asList(detailDeepLinkUrl): new ArrayList<>());
        return field;
    }

    private Field buildCurrencyField(String currency) {
        Field field = new Field();
        field.setFieldName("currency");
        field.setFieldValue(Arrays.asList(currency));
        return field;
    }

    public String getColorBasedOnTag(String tag) {
        switch (tag) {
            case PRIVATE_TAG:
                return "#007E7D";
            case SHARED_TAG:
                return "#CF8100";
            default:
                return "#000000"; // Default color if no match found
        }
    }

    public BottomSheet buildRequestToCallBackData(String pageContext, String hotelName, String device) {
        BottomSheet requestToCallBackData = new BottomSheet();
        Map<String, String> pageCallBackConfig = (MapUtils.isNotEmpty(requestToCallBackDataMap) && requestToCallBackDataMap.containsKey(pageContext)) ? requestToCallBackDataMap.get(pageContext) : null;
        requestToCallBackData.setInfoText(MapUtils.isNotEmpty(pageCallBackConfig) ? polyglotService.getTranslatedData(pageCallBackConfig.get("infoText")) : null);
        requestToCallBackData.setIconUrl(MapUtils.isNotEmpty(pageCallBackConfig) ? pageCallBackConfig.get("iconUrl") : null);
        if (CLIENT_DESKTOP.equalsIgnoreCase(device)) {
            requestToCallBackData.setIconUrl(MapUtils.isNotEmpty(pageCallBackConfig) ? pageCallBackConfig.get("iconUrlDT") : null);
        }
        requestToCallBackData.setCta(MapUtils.isNotEmpty(pageCallBackConfig) ? polyglotService.getTranslatedData(pageCallBackConfig.get("cta")) : null);
        requestToCallBackData.setBottomSheetData(buildBottomSheetData(pageCallBackConfig, hotelName, device));
        requestToCallBackData.setVersion(1);
        requestToCallBackData.setCallBackType("CallToBook");
        return requestToCallBackData;
    }

    public BottomSheet buildRequestToCallBackDataForHighValue (String pageContext, String hotelName, String device, boolean isCallToBookV2Applicable) {
        BottomSheet requestToCallBackData = null;
        if(MapUtils.isNotEmpty(requestToCallBackDataConfigForHighValue)){
            BottomSheet requestToCallBackDataConfig = requestToCallBackDataConfigForHighValue.get(pageContext);
            if(requestToCallBackDataConfig != null){
                requestToCallBackData = new BottomSheet();
                requestToCallBackData.setInfoText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getInfoText()));
                requestToCallBackData.setCta(polyglotService.getTranslatedData(requestToCallBackDataConfig.getCta()));
                requestToCallBackData.setDesc(polyglotService.getTranslatedData(requestToCallBackDataConfig.getDesc()));
                requestToCallBackData.setIconUrl(requestToCallBackDataConfig.getIconUrl());
                requestToCallBackData.setBgGradient(requestToCallBackDataConfig.getBgGradient());
                requestToCallBackData.setVersion(2);
                if(requestToCallBackDataConfig.getBottomSheetData()!=null){
                    BottomSheetData bottomSheetData = new BottomSheetData();
                    bottomSheetData.setHeading(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getHeading()));
                    bottomSheetData.setInfoText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getInfoText()));
                    bottomSheetData.setCta(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getCta()));
                    bottomSheetData.setFooterText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getFooterText()));
                    bottomSheetData.setIconUrl(requestToCallBackDataConfig.getBottomSheetData().getIconUrl());
                    requestToCallBackData.setBottomSheetData(bottomSheetData);
                }
            }
        }
        requestToCallBackData.setCallBackType("HighValue");
        return requestToCallBackData;
    }

    public String buildSourceTraffic(String source, com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails) {
        if (source == null) {
            return null;
        }

        if(deviceDetails != null && (CLIENT_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice()) ||
                BKG_DEVICE_PWA.equalsIgnoreCase(deviceDetails.getBookingDevice()))){

            String lowerCaseSource = source.toLowerCase();

            if (lowerCaseSource.contains(TrafficSourceConstants.CMP_GOOGLE_FINDER_NEW.toLowerCase())) {
                return TrafficSourceConstants.CMP_GOOGLE_FINDER_NEW.toLowerCase();
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_GOOGLE_FINDER_US.toLowerCase())) {
                return TrafficSourceConstants.CMP_GOOGLE_FINDER_US.toLowerCase();
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_GOOGLE_FINDER_AE.toLowerCase())) {
                return TrafficSourceConstants.CMP_GOOGLE_FINDER_AE.toLowerCase();
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_GOOGLE_FINDER.toLowerCase())) {
                return TrafficSourceConstants.CMP_GOOGLE_FINDER.toLowerCase();
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_TRIVAGO.toLowerCase())) {
                return TrafficSourceConstants.CMP_TRIVAGO.toLowerCase();
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_TRIPADV.toLowerCase())) {
                return TrafficSourceConstants.CMP_TRIPADV.toLowerCase();
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_IXIGO.toLowerCase())) {
                return TrafficSourceConstants.CMP_IXIGO.toLowerCase();
            } else if (source.contains(TrafficSourceConstants.CMP_TAFI)) {
                return TrafficSourceConstants.CMP_TAFI;
            } else if (source.contains(TrafficSourceConstants.SBI_YONO)) {
                return TrafficSourceConstants.SBI_YONO;
            } else if (source.contains(TrafficSourceConstants.CMP_HOTTL)) {
                return TrafficSourceConstants.CMP_HOTTL;
            } else if (lowerCaseSource.contains(TrafficSourceConstants.SEO)) {
                return TrafficSourceConstants.SEO;
            } else if (lowerCaseSource.contains(TrafficSourceConstants.SEM)) {
                return TrafficSourceConstants.SEM;
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_HDFCSBUY.toLowerCase())) {
                return TrafficSourceConstants.CMP_HDFCSBUY.toLowerCase();
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_WEGO.toLowerCase())) {
                return TrafficSourceConstants.CMP_WEGO;
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_SMARTBIZ.toLowerCase())) {
                return TrafficSourceConstants.CMP_SMARTBIZ;
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_GCC_DIRECTFEED.toLowerCase())) {
                return TrafficSourceConstants.CMP_GCC_DIRECTFEED;
            } else if (source.contains(TrafficSourceConstants.CMP_AMEX)) {
                return TrafficSourceConstants.CMP_AMEX;
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_SKYSCANNER)) {
                return TrafficSourceConstants.CMP_SKYSCANNER;
            } else if (lowerCaseSource.contains(TrafficSourceConstants.CMP_OPAQUE)) {
                return TrafficSourceConstants.CMP_OPAQUE;
            }
            return source;
        } else {
            if (source.toLowerCase().contains(TRAFFIC_SOURCE_SEO.toLowerCase())) {
                return TrafficSource.SEO.getName();
            }
            if (source.toLowerCase().contains(TRAFFIC_SOURCE_SEM.toLowerCase())) {
                return TrafficSource.SEM.getName();
            }
            return source;
        }
    }

    public BottomSheet buildRequestToCallBackDataForB2C(String pageContext) {
        BottomSheet requestToCallBackData = null;
        if (MapUtils.isNotEmpty(requestToCallBackDataConfigB2C)) {
            BottomSheet requestToCallBackDataConfig = requestToCallBackDataConfigB2C.get(pageContext);
            if (requestToCallBackDataConfig != null) {
                requestToCallBackData = new BottomSheet();
                requestToCallBackData.setInfoText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getInfoText()));
                requestToCallBackData.setCta(polyglotService.getTranslatedData(requestToCallBackDataConfig.getCta()));
                requestToCallBackData.setDesc(polyglotService.getTranslatedData(requestToCallBackDataConfig.getDesc()));
                requestToCallBackData.setIconUrl(requestToCallBackDataConfig.getIconUrl());
                requestToCallBackData.setBgGradient(requestToCallBackDataConfig.getBgGradient());
                requestToCallBackData.setVersion(2);
                requestToCallBackData.setCallBackType("ListAllProp");
                if (requestToCallBackDataConfig.getBottomSheetData() != null) {
                    BottomSheetData bottomSheetData = new BottomSheetData();
                    bottomSheetData.setHeading(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getHeading()));
                    bottomSheetData.setInfoText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getInfoText()));
                    bottomSheetData.setCta(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getCta()));
                    bottomSheetData.setFooterText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getFooterText()));
                    bottomSheetData.setIconUrl(requestToCallBackDataConfig.getBottomSheetData().getIconUrl());
                    requestToCallBackData.setBottomSheetData(bottomSheetData);
                }
            }
        }
        return requestToCallBackData;
    }

    public BottomSheet buildRequestToCallBackDataV2(String pageContext) {
        BottomSheet requestToCallBackData = null;
        if (MapUtils.isNotEmpty(requestToCallBackDataConfigV2)) {
            BottomSheet requestToCallBackDataConfig = requestToCallBackDataConfigV2.get(pageContext);
            if (requestToCallBackDataConfig != null) {
                requestToCallBackData = new BottomSheet();
                requestToCallBackData.setInfoText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getInfoText()));
                requestToCallBackData.setCta(polyglotService.getTranslatedData(requestToCallBackDataConfig.getCta()));
                requestToCallBackData.setDesc(polyglotService.getTranslatedData(requestToCallBackDataConfig.getDesc()));
                requestToCallBackData.setIconUrl(requestToCallBackDataConfig.getIconUrl());
                requestToCallBackData.setBgGradient(requestToCallBackDataConfig.getBgGradient());
                requestToCallBackData.setVersion(2);
                requestToCallBackData.setCallBackType("CallToBook");
                if (requestToCallBackDataConfig.getBottomSheetData() != null) {
                    BottomSheetData bottomSheetData = new BottomSheetData();
                    bottomSheetData.setHeading(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getHeading()));
                    bottomSheetData.setInfoText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getInfoText()));
                    bottomSheetData.setCta(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getCta()));
                    bottomSheetData.setFooterText(polyglotService.getTranslatedData(requestToCallBackDataConfig.getBottomSheetData().getFooterText()));
                    bottomSheetData.setIconUrl(requestToCallBackDataConfig.getBottomSheetData().getIconUrl());
                    requestToCallBackData.setBottomSheetData(bottomSheetData);
                }
            }
        }
        return requestToCallBackData;
    }

    private BottomSheetData buildBottomSheetData(Map<String, String> pageCallBackConfig, String hotelName, String device) {
        BottomSheetData bottomSheetData = new BottomSheetData();
        bottomSheetData.setHeading(MapUtils.isNotEmpty(pageCallBackConfig) ? polyglotService.getTranslatedData(pageCallBackConfig.get("bottomSheetData_heading")) : null);
        if (StringUtils.isNotEmpty(hotelName)) {
            bottomSheetData.setInfoText(MapUtils.isNotEmpty(pageCallBackConfig) ? polyglotService.getTranslatedData(pageCallBackConfig.get("bottomSheetData_infoText")).replace("{hotel}", hotelName) : null);
        }
        bottomSheetData.setImgUrl(MapUtils.isNotEmpty(pageCallBackConfig)?pageCallBackConfig.get("bottomSheetData_imageUrl"):null);
        if(CLIENT_DESKTOP.equalsIgnoreCase(device)) {
            bottomSheetData.setImgUrl(MapUtils.isNotEmpty(pageCallBackConfig)?pageCallBackConfig.get("bottomSheetData_imageUrlDT"):null);
        }
        bottomSheetData.setCta(MapUtils.isNotEmpty(pageCallBackConfig)?polyglotService.getTranslatedData(pageCallBackConfig.get("bottomSheetData_cta")):null);
        return bottomSheetData;
    }

    public String getDotIconUrl(Map<String, String> experimentDataMap, String iconUrl) {
        if(isReorderInclusions(experimentDataMap)) {
            if(StringUtils.isEmpty(iconUrl)) {
                iconUrl = dotIconUrl;
            }
            return iconUrl;
        }
        return freeChildInclusionIcon;
    }

    public LocationDetail buildLocationDetail(String id, String name, String type, String countryId, String countryName) {
        LocationDetail locationDetail = new LocationDetail(id, name, type, countryId, countryName);
        return locationDetail;
    }

    public static String appendQueryParamsInUrl(String url, Map<String, String> queryParamValues){
        if(MapUtils.isEmpty(queryParamValues))
            return url;
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(url);
        for(String paramKey : queryParamValues.keySet()){
            if(StringUtils.isBlank(paramKey) || StringUtils.isBlank(queryParamValues.get(paramKey)))
                continue;
            uriComponentsBuilder.replaceQueryParam(paramKey, queryParamValues.get(paramKey));
        }
        return uriComponentsBuilder.build().toUri().toString();
    }

    public static void updatePayAtHotelText(
            com.mmt.hotels.clientgateway.response.TotalPricing totalPricing,
            String payMode,
            String pahText,
            String countryCode
    ) {
        if (Utility.isPahOnlyPaymode(payMode)) {
            String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
            if (isRegionGccOrKsa(region) || !DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
                totalPricing.setPayAtHotelText(pahText.replace("{currency}", EMPTY_STRING));
            }
        }
    }

    public static boolean isPahOnlyPaymode(String payMode) {
        return PaymentMode.PAH_WITH_CC.name().equalsIgnoreCase(payMode);
    }

    public static boolean isPahWithCCPaymode(String payMode) {
        return PaymentMode.PAH_WITH_CC.name().equalsIgnoreCase(payMode);
    }

    public static boolean isPasPayMode(String payMode) {
        return PaymentMode.PAS.name().equalsIgnoreCase(payMode);
    }

    public static Map<String, String> getRequestParam(Map<String, String[]> paramsMap) {
        Map<String, String> requestParam = new HashMap<String, String>();
        try {
            for (Map.Entry<String, String[]> entry : paramsMap.entrySet()) {
                requestParam.put(entry.getKey(), entry.getValue()[0]);
            }
        } catch (Exception e) {
            LOGGER.error("Error in getRequestParam {}", e);
        }
        return requestParam;
    }

    public static String getDeviceInfo(DeviceDetails deviceDetails) {
        return deviceDetails != null && deviceDetails.getOsType() != null
                ? deviceDetails.getOsType().getValue().toUpperCase() : "";
    }

//    public String getUpdatedPropertyType(String propertyType) {
//        if(Constants.PROPERTY_TYPE_HOMESTAY.equalsIgnoreCase(propertyType)){
//            return polyglotService.getTranslatedData(ConstantsTranslation.HOMESTAY_DISPLAY_TEXT);
//        }
//        return propertyType;
//    }

    public String buildStringTypeWithVowel(String propertyType) {
        if(StringUtils.isEmpty(propertyType)) {
            return EMPTY_STRING;
        }
        return vowels.contains(propertyType.substring(0,1).toLowerCase()) ? "an " + propertyType:"a " +propertyType;
    }

    public Tuple<String, String> getGuestRoomKeyValue(Map<String, Integer> roomBedCountMap, String propertyType, String countryCode, boolean isHighSellingAltAcco, boolean isAltAcco, String bedInfoText, String listingType, boolean serviceApartment, int propertyCount, Map<String, String> expDataMap, boolean canShowRoomInfoWithoutGuests){
        String guestRoomKey = null;
        String guestRoomValue = null;
        if(Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(listingType)){
            guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS);
            guestRoomValue = new StringBuilder().append(polyglotService.getTranslatedData(ConstantsTranslation.ENTIRE)).append(Constants.SPACE).append(propertyType).append(propertyCount>1?"s":"").toString();
            guestRoomValue = modifyStayType(serviceApartment, guestRoomValue, propertyCount);
        }else{
            int bedCount = roomBedCountMap.get(Constants.SELLABLE_BED_TYPE);
            int roomCount = roomBedCountMap.get(Constants.SELLABLE_ROOM_TYPE);
            String roomGuestValue = null;
            String bedGuestValue = null;
            if(bedCount==1)
                bedGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.BED_TEXT).replace("{num}", String.valueOf(bedCount));
            else if(bedCount > 1)
                bedGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.BEDS_TEXT).replace("{num}", String.valueOf(bedCount));
            if(roomCount == 1)
                roomGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.ROOM_TEXT).replace("{num}", String.valueOf(roomCount));
            else if(roomCount > 1)
                roomGuestValue = polyglotService.getTranslatedData(ConstantsTranslation.ROOMS_TEXT).replace("{num}", String.valueOf(roomCount));
            if(bedCount > 0 && roomCount > 0){
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_ROOMS);
                guestRoomValue =roomGuestValue + Constants.COMMA + Constants.SPACE +bedGuestValue;
            }else if(bedCount == 0){
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_ROOMS);
                guestRoomValue = roomGuestValue;
            }else if(roomCount == 0){
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS_AND_BEDS);
                guestRoomValue = bedGuestValue;
            }
            if(canShowRoomInfoWithoutGuests && MapUtils.isNotEmpty(expDataMap) && Constants.TRUE.equalsIgnoreCase(expDataMap.getOrDefault(ExperimentKeys.ALT_ACCO_PROPERTY_CONFIG_ENHANCEMENT.getKey(),Constants.FALSE)) && DOM_COUNTRY.equalsIgnoreCase(countryCode) && isAltAcco && !isHighSellingAltAcco && StringUtils.isNotEmpty(bedInfoText)){
                guestRoomKey = polyglotService.getTranslatedData(ConstantsTranslation.GUESTS);
            }
        }
        return new Tuple<String,String>(guestRoomKey,guestRoomValue);
    }

    private String modifyStayType(boolean serviceApartment, String stayType, int propertyCount) {
        if (serviceApartment && StringUtils.isNotEmpty(stayType)) {
            if (ROOM_IN_APARTMENT.equalsIgnoreCase(stayType)) return ROOM_IN_SERVICE_APARTMENT;
            if (ENTIRE_APARTMENT.equalsIgnoreCase(stayType) || ENTIRE_APARTMENTS.equalsIgnoreCase(stayType)) return propertyCount>1?ENTIRE_SERVICE_APARTMENTS:ENTIRE_SERVICE_APARTMENT;
        }
        return stayType;
    }

    public static void populateCommIdsInUserDetails(UserDetail userDetail,String correlationKey, ScramblerClient scramblerClient) throws ScramblerClientException {
        if(!StringUtils.isEmpty(userDetail.getEmailID())){
            userDetail.setEmailCommId(scramblerClient.encode(userDetail.getEmailID(), HashType.F));
        }
        PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
        if (!StringUtils.isEmpty(userDetail.getMobileNo())) {
            try {
                Phonenumber.PhoneNumber phoneNumber = phoneNumberUtil.parse("+" + userDetail.getMobileNo(), "");
                Integer isdCode = phoneNumber.getCountryCode();
                String mobileNum = Long.toString(phoneNumber.getNationalNumber());
                userDetail.setPhoneCommId(scramblerClient.encode(mobileNum, isdCode, HashType.N));
            }catch(Exception ex){
                LOGGER.error("Phone number from client in user detail is faulty, corr :" + correlationKey , ex);
            }
        }
    }

    public boolean isPropertyHotelOrResort(List<HotelRates> hotelRates){
        if (CollectionUtils.isEmpty(hotelRates))
            return false;
        String titleHotel = polyglotService.getTranslatedData(HOTEL_TITLE);
        String titleResort = polyglotService.getTranslatedData(RESORT_TITLE);
        titleHotel = StringUtils.isNotEmpty(titleHotel) ? titleHotel : "hotel";
        titleResort = StringUtils.isNotEmpty(titleResort) ? titleResort : "resort";

        return titleHotel.equalsIgnoreCase(hotelRates.get(0).getPropertyType()) || titleResort.equalsIgnoreCase(hotelRates.get(0).getPropertyType());
    }

    public String getComboName(String mealPlanCode) {
        if (StringUtils.isNotBlank(mealPlanCode)) {
            switch (mealPlanCode){
                case Constants.MEAL_PLAN_CODE_ACC_ONLY:
                    return polyglotService.getTranslatedData(ACCOMODATION_ONLY);
                case Constants.MEAL_PLAN_CODE_BED_ONLY:
                    return polyglotService.getTranslatedData(BED_ONLY);
                case Constants.MEAL_PLAN_CODE_ROOM_ONLY:
                    return polyglotService.getTranslatedData(ROOM_ONLY_TEXT);
                case Constants.MEAL_PLAN_CODE_BREAKFAST:
                    return polyglotService.getTranslatedData(WITH_BREAKFAST_TEXT);
                case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH:
                    return polyglotService.getTranslatedData(WITH_BREAKFAST_AND_LUNCH);
                case Constants.MEAL_PLAN_CODE_BREAKFAST_DINNER:
                    return polyglotService.getTranslatedData(WITH_BREAKFAST_AND_DINNER);
                case Constants.MEAL_PLAN_CODE_BREAKFAST_LUNCH_OR_DINNER:
                    return polyglotService.getTranslatedData(WITH_B_AND_LD);
                case Constants.MEAL_PLAN_CODE_ALL_MEALS:
                case Constants.MEAL_PLAN_CODE_ALL_MEALS_AI:
                    return polyglotService.getTranslatedData(WITH_B_L_D);
                default:
                    return null;
            }
        }
        return null;
    }

    public static String checkMealPlan(String mealCode){
        String mealName="RO";
        if ("EP".equalsIgnoreCase(mealCode) || "BD".equalsIgnoreCase(mealCode) || "AO".equalsIgnoreCase(mealCode)) {
            mealName="RO";
        } else if ("AP".equalsIgnoreCase(mealCode) || "AI".equalsIgnoreCase(mealCode)) {
            mealName="AP";
        } else if ("CP".equalsIgnoreCase(mealCode)) {
            mealName="CP";
        } else if ("MAP".equalsIgnoreCase(mealCode) || "SMAP".equalsIgnoreCase(mealCode) || "TMAP".equalsIgnoreCase(mealCode)) {
            mealName="MAP";
        }
        return mealName;
    }

    public String getRatePlanName(List<MealPlan> mealPlanList, BookedCancellationPolicy cancellationPolicy, String sellableType, String listingType, String expData) {

        String ratePlanName = StringUtils.EMPTY;

        String mealCode = CollectionUtils.isNotEmpty(mealPlanList) ? mealPlanList.get(0).getCode() : StringUtils.EMPTY;

        String cancellationPolicyString = Constants.CANCELLATION_TYPE_NR;
        if (cancellationPolicy!=null && cancellationPolicy.getType()!=null) {
            if (Constants.CANCELLATION_TYPE_FC.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_FC;
            else if (Constants.CANCELLATION_TYPE_NR.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_NR;
            else if (Constants.CANCELLATION_TYPE_FCZPN.equalsIgnoreCase(cancellationPolicy.getType().name()))
                cancellationPolicyString = Constants.CANCELLATION_TYPE_FCZPN;
        }

        if (Constants.SELLABLE_ENTIRE_TYPE.equalsIgnoreCase(listingType))
            sellableType = Constants.SELLABLE_ENTIRE_TYPE;
        else
            sellableType = StringUtils.isBlank(sellableType) ? (Constants.SELLABLE_ROOM_TYPE).toUpperCase() : sellableType.toUpperCase();

        Map<String, String> expDataMap = getExpDataMap(expData);

        // this new variable is required because now we can have 2 values for ratePlanNameConfiguration and the relevant one will be added here
        Map<String, Map<String, Map<String, String>>> ratePlanNameMapTemp;
        // we have created a different configuration for name and it is only used when we get RATE_PLAN_REDESIGN as true form pokus
        if(isRatePlanRedesign(expDataMap)){
            ratePlanNameMapTemp = ratePlanNameMapRedesign;
        }else {
            ratePlanNameMapTemp = ratePlanNameMap;
        }
        if(MapUtils.isNotEmpty(ratePlanNameMapTemp)){
            if (!ratePlanNameMapTemp.containsKey(mealCode)) {
                if (ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(Constants.DEFAULT).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            } else {
                if (ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).containsKey(sellableType))
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(sellableType);
                else
                    ratePlanName = ratePlanNameMapTemp.get(mealCode).get(cancellationPolicyString).get(Constants.SELLABLE_ROOM_TYPE);
            }
        }
        LOGGER.debug(ratePlanName);
        return getTranslationFromPolyglot(ratePlanName);
    }

    public String getTranslationFromPolyglot(String text) {
        if (StringUtils.isBlank(text))
            return text;
        StringBuilder translatedText = new StringBuilder(text);
        try {
            while (StringUtils.isNotBlank(translatedText) && translatedText.indexOf("{")!=-1) {
                int start = translatedText.indexOf("{");
                int end = translatedText.indexOf("}");
                String constant = translatedText.substring(start+1,end);
                translatedText.replace(start,end+1,polyglotService.getTranslatedData(constant));
            }
        } catch (StringIndexOutOfBoundsException e) {
            LOGGER.warn("Error occurred when translating RatePlan Name.");
        }
        return translatedText.toString();
    }

    public com.mmt.hotels.clientgateway.response.FullPayment buildFullPayment(FullPayment fullPayment) {
        com.mmt.hotels.clientgateway.response.FullPayment fullPaymentCg = new com.mmt.hotels.clientgateway.response.FullPayment();
        fullPaymentCg.setFullPaymentText(fullPayment.getFullPaymentText());
        fullPaymentCg.setFullPaymentSubText(fullPayment.getFullPaymentSubText());
        if(fullPayment.getFinalPrice()>0.0) {
            fullPaymentCg.setFinalPrice((int) Math.round(fullPayment.getFinalPrice()));
        }
        return fullPaymentCg;
    }

    public boolean isRatePlanRedesign(Map<String,String> experimentDataMap){
        //this functions helps us to know if we need to show the new ratePlan design based on 2 exp keys srrp(client) and ratePlanRedesign(Pokus)
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.EXP_SRRP.getKey()) && EXP_TRUE_VALUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.EXP_SRRP.getKey())) && experimentDataMap.containsKey(ExperimentKeys.EXP_RATE_PLAN_REDESIGN.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.EXP_RATE_PLAN_REDESIGN.getKey()));
    }

    public boolean isReorderInclusions(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.EXP_REORDER_INCLUSIONS.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.EXP_REORDER_INCLUSIONS.getKey()));
    }

    public boolean isExtraAdultChildInclusionExperimentEnabled(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.EXP_EXTRA_ADULT_CHILD_INCLUSION.getKey()) && Constants.TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.EXP_EXTRA_ADULT_CHILD_INCLUSION.getKey()));
    }

    public boolean showExtraAdultChildInclusion(List<String> mealPlanList, String supplierCode, ExtraAdultChildInclusionConfig extraAdultChildInclusionConfig, boolean isDomesticHotel) {
        if (!extraAdultChildInclusionConfig.isEnabled()) {
            return false;
        }

        List<String> disabledMealPlanList = extraAdultChildInclusionConfig.getDisabledMealPlanList();
        for (String mealPlan: mealPlanList) {
            if (CollectionUtils.isNotEmpty(disabledMealPlanList) && disabledMealPlanList.contains(mealPlan)) {
                return false;
            }
        }

        if (isDomesticHotel) {
            if (extraAdultChildInclusionConfig.isDhSupplierCodeCheckEnabled()) {
                if (StringUtils.isBlank(supplierCode)) {
                    return false;
                }
                return extraAdultChildInclusionConfig.getEnabledDHSupplierCodeList().stream().anyMatch(supplierCode::contains);
            }
        }
        else {
            if (extraAdultChildInclusionConfig.isIhSupplierCodeCheckEnabled()) {
                if (StringUtils.isBlank(supplierCode)) {
                    return false;
                }
                return extraAdultChildInclusionConfig.getEnabledIHSupplierCodeList().stream().anyMatch(supplierCode::contains);
            }
        }

        return true;
    }

    public boolean isMealRackRate(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.MEAL_RACK_RATE.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.MEAL_RACK_RATE.getKey()));
    }

    public boolean isReviewPageAPI(String controller) {
        if (StringUtils.isBlank(controller))
            return false;
        if (ControllerConstants.REVIEW_AVAIL_ROOMS.equalsIgnoreCase(controller) ||
                ControllerConstants.REVIEW_VAILDATE_COUPON.equalsIgnoreCase(controller) ||
                ControllerConstants.REVIEW_TOTAL_PRICING.equalsIgnoreCase(controller)) {
            return true;
        }
        return false;
    }

    public boolean isDetailPageAPI(String controller) {
        if (StringUtils.isBlank(controller))
            return false;
        if (ControllerConstants.DETAIL_SEARCH_ROOMS.equalsIgnoreCase(controller) ||
                ControllerConstants.DETAIL_UPDATE_PRICE.equalsIgnoreCase(controller)
                || ControllerConstants.DETAIL_SEARCH_SLOTS.equalsIgnoreCase(controller)) {
            return true;
        }
        return false;
    }

    public String getUrlFromConfig(String key) {
        if (StringUtils.isNotEmpty(key)) {
            switch (key) {
                case VEG_ICON:
                    return vegIconUrl;
                default:
                    break;
            }
        }
        return null;
    }

    public boolean shouldDisplayOfferDiscountBreakup(Map<String, String> expDataMap) {
        if(MapUtils.isEmpty(expDataMap) || !expDataMap.containsKey("LSOF")){
            return false;
        }
        if ("T".equalsIgnoreCase(expDataMap.get("LSOF"))) {
            return true;
        }
        return false;
    }

    public boolean buildToolTip(String funnelSource) {
        if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource))
            return true;
        return false;
    }

    public boolean isExperimentOn(Map<String, String> expData, String expKey) {
        if (MapUtils.isNotEmpty(expData)
                && StringUtils.isNotBlank(expData.get(expKey))
                && ("t".equalsIgnoreCase(expData.get(expKey)) || "true".equalsIgnoreCase(expData.get(expKey)))) {
            return true;
        }
        return false;
    }

    public boolean isExperimentTrue(Map<String, String> expData, String expKey) {
        if (MapUtils.isNotEmpty(expData)
                && StringUtils.isNotBlank(expData.get(expKey))
                && "true".equalsIgnoreCase(expData.get(expKey))) {
            return true;
        }
        return false;
    }

    public static boolean isNotGCCMyPartnerMyBiz(CommonResponseTransformer commonResponseTransformer, ExtendedUser extendedUser){
        return !isGccOrKsa()
                && !isMyBizRequest()
                && !isMyPartnerRequest(extendedUser);
    }

    public static boolean isMyBizRequest(){
        return CORP_ID_CONTEXT.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
    }

    public static boolean isMyPartnerRequest(ExtendedUser extendedUser){
        if(extendedUser == null)
            return false;
        return isMyPartnerRequest(extendedUser.getProfileType(), extendedUser.getAffiliateId());
    }

    /*
     * myPartner change log :
     *   We are using this utility to check myPartner requests at all the places where we have introduced myPartner changes
     *   in the codebase.
     *
     *   Since we did not want to use an autowired bean utility [we will be having this check at a lot of places in the codebase], we are using
     *   a static function which checks static constants.
     * */
    public  static boolean isMyPartnerRequest(String profileType, String subProfileType){
        if(StringUtils.isNotBlank(profileType) &&
                StringUtils.isNotBlank(subProfileType))
            return (profileType.equalsIgnoreCase(PROFILE_TYPE) && subProfileType.equalsIgnoreCase(SUB_PROFILE_TYPE));
        return false;
    }
    public  static boolean isMyPartnerRequest(CommonModifierResponse commonModifierResponse){
        if(commonModifierResponse == null || commonModifierResponse.getExtendedUser() == null)
            return false;
        return isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
    }

    public boolean isExperimentValid(Map<String, String> expData, String expKey, int expValue) {
        if (MapUtils.isNotEmpty(expData) && StringUtils.isNotBlank(expData.get(expKey))) {
            try{
                int expVal = Integer.parseInt(expData.get(expKey));
                return (expVal == expValue);
            }catch(Exception e){
                LOGGER.error("Error while parsing experiment value", e);
            }
        }
        return false;
    }

    public static String formatDateForPixelUrl(String inputDate) {
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd/MM/yy");
            LocalDate date = LocalDate.parse(inputDate, inputFormatter);
            return outputFormatter.format(date);
        } catch(Exception e) {
            LOGGER.warn("Error while parsing dates");
        }
        return EMPTY_STRING;
    }

    public void forexCouponsNodeAddition(BestCoupon coupon, Coupon forexCoupon, boolean isDeviceDesktop, double cabPrice, int ancillaryVariant){
        if(coupon == null || forexCoupon == null)
            return;
        forexCoupon.setCouponType(COUPON_TYPE_BENEFIT);
        forexCoupon.setCouponTypeText(ancillaryVariant == 1 ? OFF_TEXT : CASHBACK);
        forexCoupon.setTncText(polyglotService.getTranslatedData(FOREX_OFFER_TNC_TEXT));
        if(cabPrice > 0.0) forexCoupon.setTncText(polyglotService.getTranslatedData(MORE_DETAILS_TEXT));
        forexCoupon.setTitle(coupon.getForexDealMessage());
        String dtTitle = polyglotService.getTranslatedData(FOREX_DEAL_TITLE_REVIEW_PAGE_DT);
        if(isDeviceDesktop && StringUtils.isNotEmpty(dtTitle) && StringUtils.isNotEmpty(forexCoupon.getTitle())){
            forexCoupon.setTitle(forexCoupon.getTitle() +
                    MessageFormat.format(dtTitle, String.valueOf((int) coupon.getForexCashbackAmount())));
        }
        forexCoupon.setCouponAmount(coupon.getForexCashbackAmount()); // forex cashback discount as instant discount is 0 here only cashback
        if(coupon.getForexCouponDetails() != null) {
            forexCoupon.setPersuasionText(coupon.getForexCouponDetails().getPersuasion_message());
        }
    }

    public static Tuple<String,String> getCheckinAndCheckoutForDailyUse(String roomName) {
        if (StringUtils.isNotBlank(roomName)) {
            try {
                Matcher matcher = HH_AA_TIME_REGEX.matcher(roomName);
                List<String> regexMatches = new ArrayList<String>();
                while (matcher.find()) {
                    regexMatches.add(matcher.group());
                }
                if (regexMatches.size() > 1) {
                    regexMatches = regexMatches.stream().map(item -> item.replaceAll("\\s+", "").replaceAll("\\.","")).collect(Collectors.toList());
                    String checkinTime = regexMatches.get(0);
                    int split = checkinTime.length()-2;
                    checkinTime = checkinTime.substring(0,split)+" "+checkinTime.substring(split).toUpperCase();
                    String checkoutTime = regexMatches.get(1);
                    split = checkoutTime.length()-2;
                    checkoutTime = checkoutTime.substring(0,split)+" "+checkoutTime.substring(split).toUpperCase();
                    return new Tuple<>(checkinTime,checkoutTime);
                }
            } catch (Exception e){
                LOGGER.error("Error while parsing the checkin/checkout time for Daily use room");
            }
        }
        return null;
    }

    public boolean isValidAppVersion(String appVersion,String allowedMinAppVersion) {
        String[] allowedAppVersion=allowedMinAppVersion.split("\\.");
        String[] currentAppVersion=appVersion.split("\\.");

        for(int i=0; i < allowedAppVersion.length; i++){
            if(currentAppVersion==null || currentAppVersion.length==i){
                return false;
            }else if(Integer.valueOf(currentAppVersion[i])<Integer.valueOf(allowedAppVersion[i])){
                return false;
            }else if(Integer.valueOf(currentAppVersion[i])>Integer.valueOf(allowedAppVersion[i])){
                return true;
            }
        }
        return true;
    }

    public Map<String, String[]> addFunnelSourceToParameterMap(PriceByHotelsRequestBody priceByHotelsRequestBody, Map<String, String[]> parameterMap) {
        Map<String, String[]> resultMap = new HashMap<>();
        if (MapUtils.isNotEmpty(parameterMap)) {
            resultMap.putAll(parameterMap);
        }
        if (priceByHotelsRequestBody != null && StringUtils.isNotEmpty(priceByHotelsRequestBody.getFunnelSource())) {
            resultMap.put(Constants.FILTER_COND_FUNNEL, new String[]{priceByHotelsRequestBody.getFunnelSource()});
        }
        return resultMap;
    }

    public void buildSlot(SearchWrapperInputRequest searchWrapperInputRequest, SearchHotelsCriteria searchCriteria) {
        if (searchCriteria.getSlot() != null) {
            Slot slot = new Slot();
            slot.setDuration(searchCriteria.getSlot().getDuration());
            slot.setTimeSlot(searchCriteria.getSlot().getTimeSlot());
            searchWrapperInputRequest.setSlot(slot);
        }
    }

    public boolean isLuxeHotel(Set<String> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return false;
        }
        return categories.contains(Constants.LUXURY_HOTELS);
    }

    public CountDownLatch getCountDownLatch() {
        return new CountDownLatch(3);
    }

    public void logRequestResponse(Object reqResponse, String logText) {
        try {
            LOGGER.warn(logText, MaskingUtil.maskSensitiveDataAndLog(objectMapperUtil.getJsonFromObject(reqResponse, DependencyLayer.CLIENTGATEWAY)));
        } catch (Exception e) {
            LOGGER.error("Error while printing request response logs", e);
        }

    }

    public void setPaginatedToMDC(SearchHotelsCriteria searchCriteria) {
        if (StringUtils.isNotBlank(searchCriteria.getLastHotelId())) {
            MDC.put(MDCHelper.MDCKeys.PAGINATED.getStringValue(), String.valueOf(true));
        } else {
            MDC.put(MDCHelper.MDCKeys.PAGINATED.getStringValue(), String.valueOf(false));
        }
    }

    public void setLoggingParametersToMDC(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates, String checkIn, String checkOut) {
        setLengthOfStay(checkIn, checkOut);
        setAdvancePurchase(checkIn);
        setAdultCount(roomStayCandidates);
        setChildCount(roomStayCandidates);
    }

    private void setLengthOfStay(String checkIn, String checkOut) {
        try {
            if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
                int lengthOfStay = dateUtil.getDaysDiff(checkIn, checkOut);
                if (lengthOfStay > 0 && lengthOfStay <= 5) {
                    MDC.put(MDCHelper.MDCKeys.LENGTH_OF_STAY.getStringValue(), String.valueOf(lengthOfStay));
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setLengthOfStay", ex);
        }
    }

    private void setAdvancePurchase(String checkIn) {
        try {
            if (StringUtils.isNotBlank(checkIn)) {
                int advancePurchase = dateUtil.getDaysDiff(checkIn);
                if (advancePurchase > 0 && advancePurchase <= 5) {
                    MDC.put(MDCHelper.MDCKeys.ADVANCE_PURCHASE.getStringValue(), String.valueOf(advancePurchase));
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setAdvancePurchase", ex);
        }
    }

    private void setAdultCount(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        try {
            int adultCount = 0;
            if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
                for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                    adultCount += roomStayCandidate.getAdultCount();
                }
            }
            if (adultCount > 0 && adultCount <= 5) {
                MDC.put(MDCHelper.MDCKeys.ADULT_COUNT.getStringValue(), String.valueOf(adultCount));
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setAdultCount", ex);
        }
    }

    private void setChildCount(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        try {
            int childCount = 0;
            if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
                for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
                    if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                        childCount += roomStayCandidate.getChildAges().size();
                    }
                }
            }
            if (childCount > 0 && childCount <= 5) {
                MDC.put(MDCHelper.MDCKeys.CHILD_COUNT.getStringValue(), String.valueOf(childCount));
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setChildCount", ex);
        }
    }

    public Map<String, String[]> addSrcReqToParameterMap(Map<String, String[]> parameterMap) {
        Map<String, String[]> resultMap = new HashMap<>();
        if (MapUtils.isNotEmpty(parameterMap)) {
            resultMap.putAll(parameterMap);
        }
        resultMap.put("srcReq", new String[]{"CG"});
        return resultMap;
    }

    public  Map<String, String> getExpDataMap(String expData) {
        Map<String, String> expDataMap = null;
        if (StringUtils.isNotBlank(expData)) {
            try {
                Type type = new TypeToken<Map<String, String>>() {
                }.getType();
                expData = expData.replaceAll("^\"|\"$", "");
                expDataMap = gson.fromJson(expData, type);
            } catch (Exception e) {
                LOGGER.error("error while converting expDataMap", e);
            }
        }
        return expDataMap;
    }

    /**
     * This method takes Experiment Data as String
     * @param expData
     * @return
     */
    public boolean isTcsV2FlowEnabled(String expData) {
        Map<String, String> expDataMap = getExpDataMap(expData);
        String countryCode = MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue());
        String siteDomain = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        return MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(NEW_TCS_FLOW_POKUS) && TRUE.equalsIgnoreCase(expDataMap.get(NEW_TCS_FLOW_POKUS)) && isIHFunnel(countryCode, siteDomain);
    }

    /**
     * This method takes Experiment Data Map
     * @param expDataMap
     * @return
     */
    public boolean isTCSV2FlowEnabled(Map<String, String> expDataMap) {
        String countryCode = MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue());
        String siteDomain = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        return MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(NEW_TCS_FLOW_POKUS) && TRUE.equalsIgnoreCase(expDataMap.get(NEW_TCS_FLOW_POKUS)) && isIHFunnel(countryCode, siteDomain);
    }

    public MmtExclusive buildMmtExclusiveNode(List<Inclusion> inclusionsList, Map<String, String> experimentDataMap){
        MmtExclusive mmtExclusive = new MmtExclusive();
        mmtExclusive.setImageUrl(Constants.MMT_EXCLUSIVE_IMAGE_URL);
        List<String> mmtExclusiveInclusionList = new ArrayList<>();

        if(MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey("gcclpg")
                && (experimentDataMap.get("gcclpg").equals("1"))){
            mmtExclusiveInclusionList.add(polyglotService.getTranslatedData(LOWEST_PRICE_GUARANTEE_TITLE));
        }
        else{
            mmtExclusiveInclusionList.add(polyglotService.getTranslatedData(ConstantsTranslation.MMT_EXCLUSIVE));
            boolean checkList = CollectionUtils.isNotEmpty(inclusionsList) && inclusionsList.get(0) !=null && inclusionsList.get(0).getCode() != null
                && !Constants.NULL_STRING.equalsIgnoreCase(inclusionsList.get(0).getCode())  ? mmtExclusiveInclusionList.add(inclusionsList.get(0).getCode()) : false;
            checkList = CollectionUtils.isNotEmpty(inclusionsList) && inclusionsList.size() >1 && inclusionsList.get(1) !=null && inclusionsList.get(1).getCode() != null
                && !Constants.NULL_STRING.equalsIgnoreCase(inclusionsList.get(1).getCode())  ? mmtExclusiveInclusionList.add(inclusionsList.get(1).getCode()) : false;
        }
        mmtExclusive.setInclusions(mmtExclusiveInclusionList);
        return mmtExclusive;
    }

    public boolean isExpPdoPrnt(String expData) {
        if (org.codehaus.plexus.util.StringUtils.isNotBlank(expData)) {
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();
            expData = expData.replaceAll("^\"|\"$", "");
            Map<String, String> expDataMap = new Gson().fromJson(expData, type);
            return expDataMap != null && expDataMap.containsKey("PDO") && org.codehaus.plexus.util.StringUtils.equalsIgnoreCase(expDataMap.get("PDO"), "PRNT");
        }
        return false;
    }

    public static boolean isGroupBookingFunnel(String funnelSource) {
        return StringUtils.equalsIgnoreCase(funnelSource, FUNNEL_SOURCE_GROUP_BOOKING);
    }

    public static boolean isCorpBudgetHotelFunnel(String funnelSource) {
        return StringUtils.equalsIgnoreCase(funnelSource, FUNNEL_SOURCE_CORPBUDGET);
    }

    public String calculateTimeSlot_Meridiem(com.mmt.hotels.model.response.dayuse.Slot slot) {
        if(slot == null)
            return null;
        String startMeridien = null;
        String endMeridien = null;
        String translatedText = null;
        if(slot.getTimeSlot() != null && slot.getDuration() != null) {
            Integer checkOutTime = Integer.parseInt(slot.getTimeSlot()) + slot.getDuration();
            Integer checkinTime = Integer.parseInt(slot.getTimeSlot());
            startMeridien = checkinTime < 12 ? AM : PM;
            endMeridien = checkOutTime < 12 ? AM : (checkOutTime >= 24 && checkOutTime % 12 >= 0) ? AM : PM;

            if (startMeridien.equals(AM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12==0? 12:checkinTime % 12 , (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(AM) && endMeridien.equals(PM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(PM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            }
        }
        return translatedText;
    }

    public String calculateTimeSlot_Meridiem(String timeSlot, Integer duration ) {
        String startMeridien = null;
        String endMeridien = null;
        String translatedText = null;
        if(StringUtils.isNotEmpty(timeSlot) && duration != null) {
            Integer checkOutTime = Integer.parseInt(timeSlot) + duration;
            Integer checkinTime = Integer.parseInt(timeSlot);
            startMeridien = checkinTime < 12 ? AM : PM;
            endMeridien = checkOutTime < 12 ? AM : (checkOutTime >= 24 && checkOutTime % 12 >= 0) ? AM : PM;

            if (startMeridien.equals(AM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12==0? 12:checkinTime % 12 , (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(AM) && endMeridien.equals(PM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_AM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else if (startMeridien.equals(PM) && endMeridien.equals(AM)) {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_AM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            } else {
                translatedText = polyglotService.getTranslatedData(TIMESLOT_PM_PM);
                translatedText = MessageFormat.format(translatedText, checkinTime % 12 == 0? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12));
            }
        }
        return translatedText;
    }


    //Method overloading for Thankyou page
    public String calculateTimeSlot_Meridiem(Slot slot) {
        if(slot == null)
            return null;
        String startMeridien = null;
        String endMeridien = null;
        //String translatedText = null;
        StringBuilder translatedText = new StringBuilder();
        if(slot.getTimeSlot() != null && slot.getDuration() != null) {
            Integer checkOutTime = slot.getTimeSlot() + slot.getDuration();
            Integer checkinTime = slot.getTimeSlot();
            startMeridien = checkinTime < 12 ? AM : PM;
            endMeridien = checkOutTime < 12 ? AM : (checkOutTime >= 24 && checkOutTime % 12 >= 0) ? AM : PM;

            if (startMeridien.equals(AM) && endMeridien.equals(AM)) {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_AM_AM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            } else if (startMeridien.equals(AM) && endMeridien.equals(PM)) {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_AM_PM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            } else if (startMeridien.equals(PM) && endMeridien.equals(AM)) {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_PM_AM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            } else {
                translatedText.append(MessageFormat.format(polyglotService.getTranslatedData(TIMESLOT_PM_PM), checkinTime % 12==0 ? 12:checkinTime % 12, (checkOutTime % 12 == 0 ? 12 : checkOutTime % 12)));
            }
            translatedText.append(DAYUSE_PARENTHESES_OPEN).append(slot.getDuration()).append(DAYUSE_SPACE).append(polyglotService.getTranslatedData(STAY_TIME_HOURS)).append(DAYUSE_PARENTHESES_CLOSE);
        }
        return translatedText.toString();
    }

    public void transformLateCheckout(Map<String, DayUsePersuasion> dayUseFunnelPersuasions, List<DayUsePersuasion> dayUsePersuasionList) {
        if(MapUtils.isNotEmpty(dayUseFunnelPersuasions) && dayUseFunnelPersuasions.containsKey(CHECKOUT_MSG)){
            DayUsePersuasion dayUsePersuasion = null;
            dayUsePersuasion = dayUseFunnelPersuasions.get(CHECKOUT_MSG);
            if(StringUtils.isNotBlank(dayUsePersuasion.getText())) {
                String lateCheckoutHTML = dayUsePersuasion.getText();
                String translatedText = polyglotService.getTranslatedData(DAYUSE_LATE_CHECKOUT);
                translatedText = MessageFormat.format(lateCheckoutHTML, translatedText);
                dayUsePersuasion.setText(translatedText);
                dayUsePersuasionList.add(dayUsePersuasion);
            }
        }
    }

    public static boolean isSeoPersuasionAllowed(ListingSearchRequest searchHotelsRequest, String sectionName) {
        if (searchHotelsRequest == null || searchHotelsRequest.getRequestDetails() == null ||
                searchHotelsRequest.getRequestDetails().getTrafficSource() == null || (SectionsType.NEARBY_HOTELS.name().equalsIgnoreCase(sectionName)) || (SectionsType.FILTER_REMOVAL.name().equalsIgnoreCase(sectionName)) ||
                (NEARBY_HOTELS_SECTION.equalsIgnoreCase(sectionName) && Constants.TRAFFIC_SOURCE_SEO.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource())) ||
                !Constants.TRAFFIC_SOURCE_SEO.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getTrafficSource().getSource()) ||
                searchHotelsRequest.getRequestDetails().isIgnoreSEO() ||
                !(DOM_COUNTRY.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getSiteDomain()))|| !(("eng").equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue())))) {
            return true;
        }
        return false;
    }

    /*
      Utility method to check if the request is from GCC funnel
    */
    public static boolean isRegionGccOrKsa(String region) {
        return AE.equalsIgnoreCase(region) || KSA.equalsIgnoreCase(region);
    }
    public static boolean isGccOrKsa() {
        return isGCC() || isKSA();
    }
    public static boolean isGCC() {
        return AE.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
    }
    public static boolean isKSA() {
        return KSA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()));
    }
    public static boolean isHotelGCCExclusive(Set<String> categories){
        if(categories != null && !categories.isEmpty())
            return isGccOrKsa() && categories.contains("MMT Exclusive");
        return false;
    }

    /*
      Utility method to check if the request is from Listing page
     */
    public static boolean isListingPage(ListingSearchRequest searchHotelsRequest) {
        return searchHotelsRequest!=null && searchHotelsRequest.getRequestDetails()!=null && Constants.PAGE_CONTEXT_LISTING.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getPageContext());
    }

    /* Utility method to check if showBnplCard is true/false */
    public static boolean isShowBnplCard(FeatureFlags featureFlags) {
        return null != featureFlags && featureFlags.isShowBnplCard();
    }

    // Below method will return polyglot key for groupPriceText or savingPerc based on client
    public static String getGroupPriceTextOrSavingPercKey(String key, String client) {
        StringBuilder sb = new StringBuilder(key);
        if (StringUtils.equalsIgnoreCase(client, DeviceConstant.DESKTOP.name())) {
            return sb.append(UNDERSCORE).append(DeviceConstant.DESKTOP.name()).toString();
        }
        return sb.toString();
    }

    public boolean isDistributeRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidateList, Map<String, String> expData) {
        if(isExperimentOn(expData, BEDROOM_COUNT_AVAILABLE)) {
            return CollectionUtils.isNotEmpty(roomStayCandidateList) && roomStayCandidateList.size() == 1;
        }
        return CollectionUtils.isNotEmpty(roomStayCandidateList) && roomStayCandidateList.size() == 1 && roomStayCandidateList.get(0).getRooms() != null;
    }

    public boolean isDistributeRoomStayCandidates(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidateList) {
        return CollectionUtils.isNotEmpty(roomStayCandidateList) && roomStayCandidateList.size() == 1 && roomStayCandidateList.get(0).getRooms() != null;
    }

    public boolean getRoomRecommndationToggleValue(List<Filter> filterCriteria) {
        if(CollectionUtils.isEmpty(filterCriteria)) {
            return false;
        }
        return filterCriteria.stream().anyMatch(filter -> EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filter.getFilterGroup().name()));
    }

    public List<com.mmt.hotels.model.request.RoomStayCandidate> buildRoomStayDistribution(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates, Map<String, String> expData) {
        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList = new ArrayList<>();
        // logic to distribute the adults and child will be done here
        if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
            Integer rooms = roomStayCandidates.get(0).getRooms();
            if(MapUtils.isNotEmpty(expData) && expData.containsKey(BEDROOM_COUNT_AVAILABLE)) {
                rooms = roomStayCandidates.get(0).getRooms() == null?1:roomStayCandidates.get(0).getRooms();
            }
            Integer adults = roomStayCandidates.get(0).getAdultCount();
            List<String> adultsDistribution = new ArrayList<>();

            distributeAdults(rooms, adults, adultsDistribution);

            //make roomStay Array for number of Rooms
            buildGuestCounts(adultsDistribution, roomStayCandidateList);
            if (CollectionUtils.isNotEmpty(roomStayCandidates.get(0).getChildAges())) {
                divideChildrenInRooms(roomStayCandidates.get(0).getChildAges(), rooms, roomStayCandidateList);
            }
        }

        return roomStayCandidateList;
    }

    public List<com.mmt.hotels.model.request.RoomStayCandidate> buildRoomStayDistribution(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList = new ArrayList<>();
        // logic to distribute the adults and child will be done here
        if (CollectionUtils.isNotEmpty(roomStayCandidates)) {
            Integer rooms = roomStayCandidates.get(0).getRooms();
            Integer adults = roomStayCandidates.get(0).getAdultCount();
            List<String> adultsDistribution = new ArrayList<>();

            distributeAdults(rooms, adults, adultsDistribution);

            //make roomStay Array for number of Rooms
            buildGuestCounts(adultsDistribution, roomStayCandidateList);
            if (CollectionUtils.isNotEmpty(roomStayCandidates.get(0).getChildAges())) {
                divideChildrenInRooms(roomStayCandidates.get(0).getChildAges(), rooms, roomStayCandidateList);
            }
        }

        return roomStayCandidateList;
    }

    public String buildRscValue(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates){
        if(CollectionUtils.isEmpty(roomStayCandidates)){
            return "";
        }
        String rscValue = "";
        Integer rooms = roomStayCandidates.get(0).getRooms();
        if(rooms!=null){
            rscValue += String.valueOf(rooms) + E;
        }
        Integer adultCount = roomStayCandidates.get(0).getAdultCount();
        if(adultCount!=null){
            rscValue += String.valueOf(adultCount) + E;
        }
        int childCount = CollectionUtils.isNotEmpty(roomStayCandidates.get(0).getChildAges())?roomStayCandidates.get(0).getChildAges().size():0;
        if(childCount!=0){
            rscValue += String.valueOf(childCount) + E;
            List<Integer> childAges = roomStayCandidates.get(0).getChildAges();
            for(Integer age:childAges){
                if(age!=null)
                    rscValue += String.valueOf(age) + E;
            }
        }
        return rscValue;
    }

    private void buildGuestCounts(List<String> adultsDistribution, List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList) {
        if (adultsDistribution.size() < 1) {
            return;
        }

        for (String adultPerRoom : adultsDistribution) {
            com.mmt.hotels.model.request.RoomStayCandidate roomStayCandidate = new com.mmt.hotels.model.request.RoomStayCandidate();
            List<GuestCount> guestCountList = new ArrayList<>();
            GuestCount guestCount = new GuestCount();
            guestCount.setCount(adultPerRoom);
            guestCount.setAgeQualifyingCode(ageQualifyingCode);
            guestCountList.add(guestCount);
            roomStayCandidate.setGuestCounts(guestCountList);
            roomStayCandidateList.add(roomStayCandidate);
        }
    }

    private void divideChildrenInRooms(List<Integer> childAges, int roomCount, List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList) {

        int childrenCount = childAges.size();
        if (roomCount < 1 || childrenCount < 1) {
            return;
        }

        int childCountForRoom = childrenCount / roomCount;
        int lastChildIndex = 0;

        for (int roomNumber = 0; roomNumber < roomStayCandidateList.size(); roomNumber++) {
            addChildInRooms(roomNumber, childAges.subList(lastChildIndex, lastChildIndex + childCountForRoom), roomStayCandidateList, roomStayCandidateList.size());
            lastChildIndex += childCountForRoom;
        }


        for (int roomNumber = roomStayCandidateList.size() - 1; roomNumber >=0 && lastChildIndex < childrenCount; roomNumber--) {

            List<Integer> tempChildAges = new ArrayList<>(roomStayCandidateList.get(roomNumber).getGuestCounts().get(0).getAges());
            tempChildAges.add(childAges.get(lastChildIndex));
            if ( roomNumber>= 0 && roomNumber< roomStayCandidateList.size()) {
                roomStayCandidateList.get(roomNumber).getGuestCounts().get(0).setAges(tempChildAges);
            }
            lastChildIndex++;
        }
    }

    private void addChildInRooms(int roomNumber, List<Integer> childAgeList, List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidateList, int totalRooms) {
        if (totalRooms - roomNumber - 1 >= 0 && totalRooms - roomNumber - 1 < roomStayCandidateList.size()) {
            roomStayCandidateList.get(totalRooms - roomNumber - 1).getGuestCounts().get(0).setAges(childAgeList);
        }
    }

    private void distributeAdults(Integer rooms, Integer adults, List<String> adultsDistribution) {
        if (rooms == null || adults == null) {
            return;
        }
        int index = 0;
        // adult distribution logic
        while (index < rooms) {
            Integer adultCountForRoom = (int) ceil((Double.valueOf(adults) / (rooms - index)));
            adultsDistribution.add(String.valueOf(adultCountForRoom));
            adults = adults - adultCountForRoom;
            index++;
        }
    }
    /**
     * utility method to append pageContext request parameter to a base url
     * @param url base url
     * @param pageContext page context string to be appended
     * @return appended pageContext String to url
     */
    public static String appendPageContextToURL(String url, String pageContext) {
        if (StringUtils.isEmpty(url)) {
            LOGGER.debug("URL is empty");
            return url;
        }
        if (StringUtils.isEmpty(pageContext)) {
            LOGGER.debug("pageContext is empty");
            return url;
        }
        StringBuilder sb = new StringBuilder(url);
        sb.append("&"+Constants.PAGE_CONTEXT+"="+pageContext);
        return sb.toString();
    }

    public void addSleepingInfoArrangementIntoRoomDetails(List<RoomDetails> roomDetails, LinkedHashMap<String, List<SleepingInfoArrangement>> spaceIdToSleepingInfoArr) {
        if (CollectionUtils.isEmpty(roomDetails) || MapUtils.isEmpty(spaceIdToSleepingInfoArr))
            return;
        for (RoomDetails roomDetail : roomDetails) {
            if (roomDetail.getPrivateSpaces() == null) {
                continue;
            }
            for (Space space : roomDetail.getPrivateSpaces().getSpaces()) {
                if (spaceIdToSleepingInfoArr.containsKey(space.getSpaceId())) {
                    for (SleepingInfoArrangement sleepingInfoArrangement : spaceIdToSleepingInfoArr.get(space.getSpaceId())) {
                        sleepingInfoArrangement.setDescriptionText((StringUtils.isNotBlank(sleepingInfoArrangement.getDescriptionText()) ? sleepingInfoArrangement.getDescriptionText() : EMPTY_STRING) +
                                ((StringUtils.isNotBlank(sleepingInfoArrangement.getDescriptionText()) && StringUtils.isNotBlank(space.getDescriptionText())) ? COMMA_SPACE + space.getDescriptionText() : (StringUtils.isNotBlank(space.getDescriptionText())?space.getDescriptionText():EMPTY_STRING)));
                        sleepingInfoArrangement.setOpenCardText((StringUtils.isNotBlank(sleepingInfoArrangement.getOpenCardText()) ? sleepingInfoArrangement.getOpenCardText() : EMPTY_STRING) +
                                ((StringUtils.isNotBlank(sleepingInfoArrangement.getOpenCardText()) && StringUtils.isNotBlank(space.getOpenCardText())) ? (StringUtils.isBlank(space.getDescriptionText())?SPACE:COMMA_SPACE) + space.getOpenCardText() : (StringUtils.isNotBlank(space.getOpenCardText())?space.getOpenCardText():EMPTY_STRING)));
                    }
                    if (CollectionUtils.isNotEmpty(spaceIdToSleepingInfoArr.get(space.getSpaceId()))) {
                        space.setDescriptionText(spaceIdToSleepingInfoArr.get(space.getSpaceId()).get(0).getDescriptionText());
                        space.setOpenCardText(spaceIdToSleepingInfoArr.get(space.getSpaceId()).get(0).getOpenCardText());
                    }
                    space.setSleepingInfoArrangement(spaceIdToSleepingInfoArr.get(space.getSpaceId()));
                }
            }
            roomDetail.setBedInfoText(buildBedInfoText(roomDetail.getPrivateSpaces().getSpaces()));
        }
    }

    public String buildBedInfoText(List<Space> spaces) {
        if (CollectionUtils.isEmpty(spaces))
            return null;
        LinkedHashMap<String, Integer> bedInfoMap = new LinkedHashMap<>();
        for (Space space : spaces) {
            if (BEDROOM.equalsIgnoreCase(space.getSpaceType()) || LIVING_ROOM.equalsIgnoreCase(space.getSpaceType())) {
                if (CollectionUtils.isNotEmpty(space.getSleepingInfoArrangement())) {
                    for (SleepingInfoArrangement sleepingInfoArrangement : space.getSleepingInfoArrangement()) {
                        if (MapUtils.isNotEmpty(sleepingInfoArrangement.getBedInfos())) {
                            for (String bedType : sleepingInfoArrangement.getBedInfos().keySet()) {
                                if (!bedInfoMap.containsKey(bedType))
                                    bedInfoMap.put(bedType, 0);
                                bedInfoMap.put(bedType, bedInfoMap.get(bedType) + sleepingInfoArrangement.getBedInfos().get(bedType));
                            }
                        }
                    }
                }
            }
        }

        return createBedInfoTextFromBedInfoMap(bedInfoMap);
    }

    public String createBedInfoTextFromBedInfoMap(LinkedHashMap<String, Integer> bedInfoMap) {
        if (MapUtils.isEmpty(bedInfoMap))
            return null;

        StringBuilder bedInfoText = new StringBuilder(EMPTY_STRING);
        int commaCountRequired = bedInfoMap.size() - 1;
        HashSet<String> visitedBedType = new HashSet<>();
        for (String bedType : bedTypePriorityOrder) {
            if (StringUtils.isNotBlank(bedType) && bedInfoMap.containsKey(bedType)) {
                visitedBedType.add(bedType);
                Pair<String,Integer> p = evaluateBedInfoText(bedInfoMap,bedType,commaCountRequired);
                bedInfoText.append(p.getKey());
                commaCountRequired = p.getValue();
            }
        }

        // Now Add those that are remaining Which are Not matched with the bedTypePriorityOrder List
        for (String bedType : bedInfoMap.keySet()) {
            if(visitedBedType.contains(bedType))
                continue;
            Pair<String,Integer> p = evaluateBedInfoText(bedInfoMap,bedType,commaCountRequired);
            bedInfoText.append(p.getKey());
            commaCountRequired = p.getValue();
        }

        return bedInfoText.toString();
    }

    private Pair<String, Integer> evaluateBedInfoText(LinkedHashMap<String, Integer> bedInfoMap, String bedType, int commaCountRequired){
        String bedInfoText=EMPTY_STRING;
        int bedTypeCount = (bedInfoMap.get(bedType) != null ? bedInfoMap.get(bedType) : 0);
        if (bedTypeCount == 0){
            commaCountRequired--;
        } else {
            if (bedTypeCount > 1) {
                if (bedType.endsWith("s"))
                    bedInfoText = bedTypeCount + SPACE + bedType + "es";
                else
                    bedInfoText = bedTypeCount + SPACE + bedType + "s";
            } else
                bedInfoText = bedTypeCount + SPACE + bedType;

            if (commaCountRequired > 0) {
                bedInfoText += COMMA_SPACE;
                commaCountRequired--;
            }
        }
        return new ImmutablePair<>(bedInfoText,commaCountRequired);

    }

    public CityOverviewHesRequest buildCityOverviewHesRequest(CityOverviewRequest cityOverviewRequest, String correlationKey, String profileType){
        CityOverviewHesRequest cityOverviewHesRequest = new CityOverviewHesRequest();
        if(cityOverviewRequest != null && cityOverviewRequest.getRequestDetails() != null && cityOverviewRequest.getSearchCriteria() != null){
            cityOverviewHesRequest.setCityId(cityOverviewRequest.getSearchCriteria().getLocationId());
            cityOverviewHesRequest.setCountryCode(cityOverviewRequest.getSearchCriteria().getCountryCode());
            cityOverviewHesRequest.setCorrelationKey(correlationKey);
            cityOverviewHesRequest.setFunnel(cityOverviewRequest.getRequestDetails().getFunnelSource());
            cityOverviewHesRequest.setProfileType(profileType);
            cityOverviewHesRequest.setCurrency(cityOverviewRequest.getSearchCriteria().getCurrency());
            cityOverviewHesRequest.setExperimentData(cityOverviewRequest.getExpData());
        }
        return cityOverviewHesRequest;
    }

    public Integer getLOS(String checkIn, String checkOut) {
        int los = 0;
        if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
            los = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
        }
        return los;
    }
    public static boolean isBookingDeviceDesktop(com.mmt.hotels.clientgateway.request.DeviceDetails deviceDetails) {
        return deviceDetails != null && Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice());
    }

    /**
     * @param date
     * @param time
     * @return comma separated date and time
     * <p>
     * date: 20 May | time: 11:59 PM | result: 20 May, 11:59 PM
     */
    public String concatenateDateAndTime(String date, String time) {
        String resultDateTime = "";
        if (StringUtils.isNotEmpty(date) && StringUtils.isNotEmpty(time)) {
            resultDateTime = resultDateTime + date + COMMA_SPACE + time;
        }
        return resultDateTime;
    }

    public String buildDescriptionText(String description,String amount, String askedCurrency) {
        String currencySymbol = Currency.getCurrencyEnum(StringUtils.isNotBlank(askedCurrency) ? askedCurrency : DEFAULT_CUR_INR).getCurrencySymbol();
        description=description.replace("{AMOUNT}", amount);
        description=description.replace("{CURRENCY_SYMBOL}",currencySymbol);
        return description;
    }

    public static boolean isNotGCCDayUse(FilterCountRequest request) {
        if (request == null)
            return false;
        boolean isGroupFunnel = request.getRequestDetails()!=null ? FUNNEL_SOURCE_GROUP_BOOKING.equalsIgnoreCase(request.getRequestDetails().getFunnelSource()) : false;
        boolean isShortStays = request.getRequestDetails()!=null ? FUNNEL_SOURCE_SHORTSTAYS.equalsIgnoreCase(request.getRequestDetails().getFunnelSource()) : false;
        boolean isDayuse = request.getRequestDetails()!=null ? FUNNEL_DAYUSE.equalsIgnoreCase(request.getRequestDetails().getFunnelSource()) : false;
        return !isDayuse && !isGroupFunnel && !isShortStays;
    }

    public static <K, V> Map<K, V> updateKeys(Map<K, V> map) {
        if(map==null){
            return null;
        }
        Map<K, V> modifiedMap = new HashMap<>();
        for (Map.Entry<K, V> entry : map.entrySet()) {
            String key = (String) entry.getKey();
            key = key.replace("__", " ");
            modifiedMap.put((K) key, entry.getValue());
        }
        return modifiedMap;
    }


    public boolean isIHMyPartnerOrB2CFunnel(CommonModifierResponse commonModifierResponse, String siteDomain, String countryCode) {
        return isIHFunnel(countryCode, siteDomain) && (isB2CFunnel() || isMyPartner(commonModifierResponse));
    }

    public boolean isMyPartner(CommonModifierResponse commonModifierResponse) {
        boolean isMyPartnerRequest = (commonModifierResponse != null) && (commonModifierResponse.getExtendedUser() != null)
                && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
        return isMyPartnerRequest;
    }
    public boolean isMyPartnerExclusiveDealAllowed(final PersistedMultiRoomData persistedMultiRoomData) {
        return persistedMultiRoomData != null && persistedMultiRoomData.getAvailReqBody() != null
                && isExperimentTrue(persistedMultiRoomData.getExpData(), MYPARTNER_EXCLUSIVE_DEAL)
                && Utility.isMyPartnerRequest(persistedMultiRoomData.getAvailReqBody().getProfileType(), persistedMultiRoomData.getAvailReqBody().getSubProfileType())
                && persistedMultiRoomData.isExclusiveFlyerRateAvailable();
    }
    public boolean isIHFunnel(String countryCode, String siteDomain) {
        return countryCode != null && !countryCode.equalsIgnoreCase(DOM_COUNTRY) && !isRegionGccOrKsa(siteDomain);
    }
    public boolean isB2CFunnel(String idContext) {
        return B2C.equalsIgnoreCase(idContext) || MOB_ID_CONTEXT.equalsIgnoreCase(idContext) || isB2CFunnel();
    }
    public boolean isB2CFunnel() {
        return B2C.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()));
    }

    public BookedInclusion getFreeChildInclusion(String freeChildText, String dotIconUrl) {
        BookedInclusion freeChildInclusion = null;
        if (StringUtils.isNotEmpty(freeChildText)) {
            freeChildInclusion = new BookedInclusion();
            freeChildInclusion.setType(Constants.KIDS);
            freeChildInclusion.setCategory(Constants.KIDS);
            freeChildInclusion.setIconType(IconType.DEFAULT);
            freeChildInclusion.setIconUrl(dotIconUrl);
            freeChildInclusion.setCode(freeChildText);
            freeChildInclusion.setText(freeChildText);
            freeChildInclusion.setBookable(true);
        }
        return freeChildInclusion;

    }

    public static String isDomOrIntl(SearchHotelsCriteria searchHotelsCriteria) {
        return searchHotelsCriteria != null && Constants.DOM_COUNTRY.equalsIgnoreCase(searchHotelsCriteria.getCountryCode()) ? Constants.DOMESTIC : Constants.INTERNATIONAL;
    }

    public boolean isOHSExpEnable(String propertyType, LinkedHashMap<String, String> expDataMap) {
        return (MapUtils.isNotEmpty(expDataMap) && Constants.PROPERTY_TYPE_HOSTEL.equalsIgnoreCase(propertyType) && Constants.TRUE.equalsIgnoreCase(expDataMap.get(Constants.OPTIMIZE_HOSTEL_SELECTION_EXP)));
    }

    public int getLengthOfStay(String checkIn, String checkOut) {
        try {
            if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
                return dateUtil.getDaysDiff(checkIn, checkOut);
            }
        } catch (Exception ex) {
            LOGGER.error("Error in setLengthOfStay", ex);
        }
        return 0;
    }

    public void updateCancellationPolicyText(boolean enableThemification, RatePlan ratePlan) {
        if (enableThemification && ratePlan != null && ratePlan.getCancellationPolicy() != null) {
            ratePlan.getCancellationPolicy().setText(null);
            String subText = StringUtils.isNotEmpty(ratePlan.getCancellationPolicy().getSubText()) ?
                    FONT_COLOR_FC_SUB_TEXT + OPEN_BOLD_TAG + ratePlan.getCancellationPolicy().getSubText() + FULLSTOP_SPACE + CLOSE_BOLD_TAG + CLOSE_FONT_TAG : null;
            ratePlan.getCancellationPolicy().setSubText(subText);
        }
    }

    public boolean isFreeCancellation(List<CancelPenalty> cancelPenalty) {
        return CollectionUtils.isNotEmpty(cancelPenalty) &&
                CancelPenalty.CancellationType.FREE_CANCELLATON.equals(cancelPenalty.stream().findFirst().get().getCancellationType());
    }

    /***
     * This function is used to build RequestIdentifier basis of requestDetail which can be further use in tracking purposed, for ex -> PDT
     * @param requestDetails
     * @return
     */
    public RequestIdentifier buildRequestIdentifier(RequestDetails requestDetails) {
        if(requestDetails==null){
            return null;
        }
        RequestIdentifier requestIdentifier = new RequestIdentifier();
        requestIdentifier.setJourneyId(requestDetails.getJourneyId());
        requestIdentifier.setRequestId(requestDetails.getRequestId());
        requestIdentifier.setSessionId(requestDetails.getSessionId());
        if(null != requestDetails.getCheckDuplicateBooking()) {
            requestIdentifier.setCheckDuplicateBooking(requestDetails.getCheckDuplicateBooking());
        }
        return requestIdentifier;
    }

    public void updateNRCancellationPolicyInclusion(boolean showReviewNRInclusion, String cancellationPolicyType, AvailRoomsResponse availRoomsResponse) {
        if (showReviewNRInclusion && CANCELLATION_TYPE_NR.equalsIgnoreCase(cancellationPolicyType) && CollectionUtils.isNotEmpty(availRoomsResponse.getRateplanlist())) {
            for (RatePlan ratePlan : availRoomsResponse.getRateplanlist()) {
                if (ratePlan.getInclusionsList() == null) {
                    ratePlan.setInclusionsList(new ArrayList<>());
                }
                BookedInclusion bookedInclusion = new BookedInclusion();
                bookedInclusion.setIconType(IconType.BIGCROSS);
                bookedInclusion.setText(polyglotService.getTranslatedData(ConstantsTranslation.NON_REFUNDABLE_SUBTEXT));
                ratePlan.getInclusionsList().add(bookedInclusion);
            }
        }
    }

    public static boolean isAppRequest() {
        return ANDROID.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue())) || DEVICE_IOS.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
    }

    public static boolean isPWARequest() {
        return Constants.DEVICE_OS_PWA.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
    }

    public static boolean isDesktopRequest() {
        return Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.CLIENT.getStringValue()));
    }

    public String concatenateWithSeparator(String separator, Collection<String> categories) {
        if (CollectionUtils.isNotEmpty(categories)) {
            return String.join(separator, categories);
        }
        return null;
    }

    public BGLinearGradient buildBgLinearGradientforForex(){
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        bgLinearGradient.setDirection(DIRECTION_DIAGONAL);
        bgLinearGradient.setEnd(GRADIENT_END_FOREX);
        bgLinearGradient.setStart(FFFFFF);
        bgLinearGradient.setCenter(FFFFFF);
        return bgLinearGradient;
    }

    public BGLinearGradient buildSubTextBgGradientforForex(){
        BGLinearGradient subTextBgGradient = new BGLinearGradient();
        subTextBgGradient.setStart(SUB_GRADIENT_START);
        subTextBgGradient.setEnd(SUB_GRADIENT_END);
        subTextBgGradient.setDirection(DIRECTION_DIAGONAL);
        return subTextBgGradient;
    }

    public BGLinearGradient buildBgLinearGradientForHotelCloud(){
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        bgLinearGradient.setStart(FFFFFF);
        bgLinearGradient.setEnd(GRADIENT_END_HOTEL_CLOUD);
        bgLinearGradient.setDirection(DIRECTION_DIAGONAL);
        bgLinearGradient.setCenter(FFFFFF);
        return bgLinearGradient;
    }

    public BGLinearGradient buildBgLinearGradientForPriceDrop(){
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        bgLinearGradient.setStart(F1FFFF);
        bgLinearGradient.setCenter(FFFFFF);
        bgLinearGradient.setEnd(FFFFFF);
        bgLinearGradient.setDirection(DIRECTION_VERTICAL);
        return bgLinearGradient;
    }

    public BGLinearGradient buildBgLinearGradientForPriceSurge(){
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        bgLinearGradient.setStart(FFF7EB);
        bgLinearGradient.setCenter(FFFFFF);
        bgLinearGradient.setEnd(FFFFFF);
        bgLinearGradient.setDirection(DIRECTION_VERTICAL);
        return bgLinearGradient;
    }

    public BGLinearGradient buildBgLinearGradientForPriceTypical(){
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        bgLinearGradient.setStart(EFF6FF);
        bgLinearGradient.setCenter(FFFFFF);
        bgLinearGradient.setEnd(FFFFFF);
        bgLinearGradient.setDirection(DIRECTION_VERTICAL);
        return bgLinearGradient;
    }

    public String getPriceColorForPriceDrop(PriceVariationType type) {
        String color = null;
        if (type == PriceVariationType.DROP) {
            color = "#007E7D";
        } else if( type == PriceVariationType.SURGE) {
            color = "#CF8100";
        }
        return color;
    }

    public String logLuckyUserData(CommonModifierResponse commonModifierResponse, String luckyUserContext, String requestType) {
        boolean isXPercentSellOn = false;
        String uuid = null;
        Set<String> hydraSegments = null;
        if (commonModifierResponse != null) {
            isXPercentSellOn = MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && commonModifierResponse.getExpDataMap().containsKey(X_PERCENT_SELL_ON) && TRUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(X_PERCENT_SELL_ON));
            uuid = commonModifierResponse.getExtendedUser() != null ? commonModifierResponse.getExtendedUser().getUuid() : null;
            hydraSegments = commonModifierResponse.getHydraResponse() != null ? commonModifierResponse.getHydraResponse().getHydraMatchedSegment() : null;
        }
        String isXPercentSellExpText = isXPercentSellOn ? X_PERCENT_SELL_ON_TEXT : X_PERCENT_SELL_OFF_TEXT;
        String luckyUserContextWithExp = isXPercentSellExpText + (StringUtils.isNotEmpty(luckyUserContext) ? "|" + luckyUserContext : "");
        LOGGER.warn("X-Percent Tracking: xPercentSellOn={} | UUID={} | HydraSegments={} | LuckyUserContext={} | RequestType={}", isXPercentSellOn, uuid, hydraSegments, luckyUserContext, requestType);
        return luckyUserContextWithExp;
    }

    public boolean checkIfFilterValueExistsInAppliedFilterMap(List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria) {
        if (CollectionUtils.isNotEmpty(filterCriteria)) {
            for (com.mmt.hotels.clientgateway.request.Filter filterCG : filterCriteria) {
                if(filterCG.getFilterGroup() != null) {
                    if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(filterCG.getFilterGroup().name()) || (BEDROOM_COUNT.equalsIgnoreCase(filterCG.getFilterGroup().name()))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public String updateExpDataForBedRoomCountFilter(List<com.mmt.hotels.clientgateway.request.Filter> filterCriteria, String expData) {
        // Convert expData to a map
        Map<String, String> expDataMap = getExpDataMap(expData);
        if(MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(AA_BEDROOM_INPUT_EXP)  && null != expDataMap.get(AA_BEDROOM_INPUT_EXP)){
            if(expDataMap.get(AA_BEDROOM_INPUT_EXP).equalsIgnoreCase("false")){
                expDataMap.put(MULTI_ROOM_EXP, FLEXIBLE_ROOM_VALUE);
            }
        }
        if (MapUtils.isEmpty(expDataMap) || !expDataMap.containsKey(MULTI_ROOM_EXP) || filterCriteria == null) {
            return expData; // No need to process further if map is empty or key is absent
        }

        for (com.mmt.hotels.clientgateway.request.Filter filter : filterCriteria) {
            if (filter.getFilterGroup() != null && BEDROOM_COUNT.equalsIgnoreCase(filter.getFilterGroup().name())) {
                // Update the map with the new value
                expDataMap.put(MULTI_ROOM_EXP, FLEXIBLE_ROOM_VALUE);

                // Convert the map back to JSON and return
                try {
                    return objectMapperUtil.getJsonFromObject(expDataMap, DependencyLayer.CLIENTGATEWAY);
                } catch (Exception e) {
                    LOGGER.error("Error in updating expData for bedroomCount filter", e);
                    return expData;
                }
            }
        }
        return expData; // Return unchanged expData if no update is performed
    }

    //Modifying No. of rooms count only when filterValue of bedRoomCount filter is less than or equal to total no. of pax search for Listing Page.
    public void modifyRoomStayCandidateRequestForHomestayFunnel(Filter filter, SearchHotelsCriteria searchHotelsCriteria){
        if(filter != null && searchHotelsCriteria != null && CollectionUtils.isNotEmpty(searchHotelsCriteria.getRoomStayCandidates())){
            if((searchHotelsCriteria.getRoomStayCandidates().get(0).getAdultCount() + (CollectionUtils.isNotEmpty(searchHotelsCriteria.getRoomStayCandidates().get(0).getChildAges()) ?
                    searchHotelsCriteria.getRoomStayCandidates().get(0).getChildAges().size() : 0)) >= Integer.valueOf(filter.getFilterValue())){
                searchHotelsCriteria.getRoomStayCandidates().get(0).setRooms(Integer.valueOf(filter.getFilterValue()));
            }
        }
    }

    //Modifying No. of rooms count only when filterValue of bedRoomCount filter is less than or equal to total no. of pax search for Detail Page.
    public void modifyRoomStayCandidateRequestForHomestayFunnelDetail(Filter filter, SearchRoomsCriteria searchHotelsCriteria){
        if(filter != null && searchHotelsCriteria != null && CollectionUtils.isNotEmpty(searchHotelsCriteria.getRoomStayCandidates())){
            if((searchHotelsCriteria.getRoomStayCandidates().get(0).getAdultCount() + (CollectionUtils.isNotEmpty(searchHotelsCriteria.getRoomStayCandidates().get(0).getChildAges()) ?
                    searchHotelsCriteria.getRoomStayCandidates().get(0).getChildAges().size() : 0)) >= Integer.valueOf(filter.getFilterValue())){
                searchHotelsCriteria.getRoomStayCandidates().get(0).setRooms(Integer.valueOf(filter.getFilterValue()));
            }
        }
    }

    public SupportDetails buildSupportDetails(int totalTicketValue, String page, String funnelType, boolean aboApplicable){
        HighValueCallSupportDetails highValueCallSupportDetails = mobConfigPropsConsul.getSupportDetails();
        if (highValueCallSupportDetails != null && CollectionUtils.isNotEmpty(highValueCallSupportDetails.getPages())
                && highValueCallSupportDetails.getPages().contains(page) && highValueCallSupportDetails.getRange() != null
                && highValueCallSupportDetails.getRange().containsKey(funnelType)) {
            FunnelRange funnelRange = highValueCallSupportDetails.getRange().get(funnelType);
            if (funnelRange == null || (!PAGE_CONTEXT_LISTING.equals(page) && !(totalTicketValue >= funnelRange.getMinRange()
                    && totalTicketValue <= funnelRange.getMaxRange()))) {
                return null;
            }
            SupportDetails supportDetails = new SupportDetails();
            supportDetails.setOptions(!aboApplicable ? highValueCallSupportDetails.getOptions() : Arrays.asList("callToBook"));
            supportDetails.setFormUrl(highValueCallSupportDetails.getFormUrl());
            return supportDetails;
        }
        return null;
    }
    public int getTotalTicketValue(com.mmt.hotels.clientgateway.response.TotalPricing totalPricing, boolean isDetailPage){
        int totalTicketValue = 0;
        if(totalPricing != null && CollectionUtils.isNotEmpty(totalPricing.getDetails())){
            List<com.mmt.hotels.clientgateway.response.PricingDetails> pricingDetails = totalPricing.getDetails();
            for(com.mmt.hotels.clientgateway.response.PricingDetails pricingDetail : pricingDetails){
                if(TOTAL_AMOUNT_KEY.equalsIgnoreCase(pricingDetail.getKey()) || (isDetailPage && TAXES_KEY.equalsIgnoreCase(pricingDetail.getKey()))){
                    totalTicketValue+=pricingDetail.getAmount();
                }
            }
        }
        return totalTicketValue;
    }

    public String getHighValueCallFunnelType(String propertyType, String funnelSource, String countryCode) {
        if(STAY_TYPE_HOMESTAY.equalsIgnoreCase(funnelSource) || (DOM_COUNTRY.equalsIgnoreCase(countryCode)
                && !HOTEL.equalsIgnoreCase(propertyType) && !RESORT.equalsIgnoreCase(propertyType))){
            return CODE_AltAco;
        }else if(DOM_COUNTRY.equalsIgnoreCase(countryCode) && (HOTEL.equalsIgnoreCase(propertyType) || RESORT.equalsIgnoreCase(propertyType))){
            return DOM_HOTEL;
        }else if(!DOM_COUNTRY.equalsIgnoreCase(countryCode)){
            return INTEL_HOTEL;
        }else{
            return DOM_HOTEL;
        }
    }

    public boolean isRTBDayNightEnabled(Map<String, String> expDataMap) {
        return MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(RTB_DAY_NIGHT_EXP) && TRUE.equalsIgnoreCase(expDataMap.get(RTB_DAY_NIGHT_EXP));
    }

    public boolean isImageExperimentEnable(Map<String, String> expDataMap) {
        return MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(IMAGES_EXP_ENABLE) && TRUE.equalsIgnoreCase(expDataMap.get(IMAGES_EXP_ENABLE));
    }

    public static String fetchAgencyUUIDFromCorp(final String corporateData) {
        if (StringUtils.isNotBlank(corporateData)) {
            CorporateData corporateDataObject = gson.fromJson(corporateData, CorporateData.class);
            return Objects.nonNull(corporateDataObject) ? corporateDataObject.getAgencyUUID() : null;
        }
        return null;
    }

    public boolean isNoCostEmiApplicableExperimentEnabled(Map<String, String> expDataMap) {
        return MapUtils.isNotEmpty(expDataMap) && TRUE.equalsIgnoreCase(expDataMap.get(NO_COST_EMI_APPLICABLE));
    }

    /**
     * This method is used to convert a number to its corresponding lakh or crore value. For example 140000 should be converted to 1.4 lakh and
     * 54300000 should be converted to 5.43 crore. Input is an integer value
     * @param value integer value to be converted
     * @return String value of the input integer
     */

    public static String convertToLakhOrCrore(int value) {
        if (value < LAKH) return String.valueOf(value);
        if (value < CRORE) return Utility.round((double) value / LAKH, 2) + LAKH_PREFIX;
        return Utility.round((double) value / CRORE, 2) + CRORE_PREFIX;
    }

    public BookedInclusion prepareBookedInclusionFromInclusion(Inclusion inclusion) {
        BookedInclusion bookedInclusion = new BookedInclusion();
        bookedInclusion.setCode(inclusion.getCode());
        bookedInclusion.setText(inclusion.getValue());
        bookedInclusion.setIconType(IconType.DEFAULT);
        bookedInclusion.setIconUrl(inclusion.getIconUrl());
        return bookedInclusion;
    }

    public String getUpgradeType(BlackBenefits blackBenefits) {
        if (blackBenefits != null) {
            if (blackBenefits.isRoomUpgrade() && blackBenefits.isMealUpgrade()) {
                return UpgradeType.ROOM_AND_MEAL.name();
            } else if (blackBenefits.isRoomUpgrade()) {
                return UpgradeType.ROOM.name();
            } else if (blackBenefits.isMealUpgrade()) {
                return UpgradeType.MEAL.name();
            }
        }
        return null;
    }

    public boolean checkCondition(String value, Set<String> consulConfig) {
        if (StringUtils.isNotEmpty(value)) {
            value = value.toUpperCase();
        }
        return CollectionUtils.isNotEmpty(consulConfig) && (consulConfig.contains("*") || consulConfig.contains(value));
    }

    public boolean hasCommonElements(List<String> list, Set<String> set) {
        if(CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(set)){
            return false;
        }
        for (String element : list) {
            if (set.contains(element)) {
                return true;
            }
        }
        return false;
    }
    public void buildRoomNameCollapseDesc(AvailRoomsResponse availRoomsResponse) {
        if (availRoomsResponse != null && CollectionUtils.isNotEmpty(availRoomsResponse.getRateplanlist())) {
            for (RatePlan ratePlan : availRoomsResponse.getRateplanlist()) {
                StringBuilder mealNcancelText = new StringBuilder();
                if (CollectionUtils.isNotEmpty(ratePlan.getInclusionsList())) {
                    for (BookedInclusion inclusion : ratePlan.getInclusionsList()) {
                        if (Arrays.stream(inclusion.getText().split("\\s+")).anyMatch(inclusionList::contains)) {
                            mealNcancelText.replace(0, mealNcancelText.length(),inclusion.getText()); // we have to keep last inclusion which contains meal info
                        }
                    }
                }
                if (ratePlan.getCancellationPolicy() != null &&
                        StringUtils.isNotEmpty(ratePlan.getCancellationPolicy().getText())) {
                    if(StringUtils.isNotEmpty(mealNcancelText))
                        mealNcancelText.append(" | ");
                    mealNcancelText.append(ratePlan.getCancellationPolicy().getText());
                }
                ratePlan.setRoomNameCollapseDesc(mealNcancelText.toString());
            }
        }
    }

    public void addFilterInAppliedFilterList(FilterGroup filterGroup, String filterValue, int index, List<Filter> appliedFilterList){
        Filter filter = new Filter();
        filter.setFilterGroup(filterGroup);
        filter.setFilterValue(filterValue);
        if(CollectionUtils.isNotEmpty(appliedFilterList)) {
            if (index >= 0) {
                appliedFilterList.add(index, filter);
            } else {
                appliedFilterList.add(filter);
            }
        }
    }

    public boolean showBookAtZeroPersuasion(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.SHOW_BOOK_AT_0_PERSUASION.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.SHOW_BOOK_AT_0_PERSUASION.getKey()));
    }

    public boolean isFoodAndDiningEnhancement(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.FOOD_AND_DINING_ENHANCEMENT.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.FOOD_AND_DINING_ENHANCEMENT.getKey()));
    }

    public boolean showReviewOffersCategory(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.REVIEW_OFFERS_CATEGORY.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.REVIEW_OFFERS_CATEGORY.getKey()));
    }

    public boolean isAmenitiesV2Enabled(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.AMENITIES_V2_ENABLED.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.AMENITIES_V2_ENABLED.getKey()));
    }

    public boolean isPriceVariationV2Enabled(Map<String, String> experimentDataMap) {
        return MapUtils.isNotEmpty(experimentDataMap) && experimentDataMap.containsKey(ExperimentKeys.PRICE_VARIATION_V2.getKey()) && TRUE.equalsIgnoreCase(experimentDataMap.get(ExperimentKeys.PRICE_VARIATION_V2.getKey()));
    }

    public String buildPartialRefundDateText(CancellationTimeline cancellationTimeline){
        String partialRefundDateText = "";
        if(cancellationTimeline == null)
            return partialRefundDateText;

        String checkInDate = cancellationTimeline.getCheckInDate();
        String refundDate = "", refundDateTime = "";

        if(CollectionUtils.isNotEmpty(cancellationTimeline.getCancellationPolicyTimelineList()) && cancellationTimeline.getCancellationPolicyTimelineList().get(0) != null) {
        refundDate = cancellationTimeline.getCancellationPolicyTimelineList().get(0).getEndDate();
        refundDateTime = cancellationTimeline.getCancellationPolicyTimelineList().get(0).getEndDateTime();
        }

        boolean isCheckInAndRefundDateSame = false;
        if(StringUtils.isNotEmpty(checkInDate) && StringUtils.isNotEmpty(refundDate)) {
            isCheckInAndRefundDateSame = checkInDate.equalsIgnoreCase(refundDate);
        }

        if(isCheckInAndRefundDateSame) {
            partialRefundDateText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_CHECKIN_TEXT);
        } else {
            String refundText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_WITH_REFUNDDATE_TEXT);
            if(StringUtils.isNotEmpty(refundDate) && StringUtils.isNotEmpty(refundDateTime) && StringUtils.isNotEmpty(refundText)) {
                partialRefundDateText = MessageFormat.format(refundText, refundDate, refundDateTime);
            }else{
                partialRefundDateText = polyglotService.getTranslatedData(ConstantsTranslation.PARTIAL_REFUNDABLE_TEXT);
            }
        }

        return partialRefundDateText;
    }

    public boolean checkAppVersion(String deviceType, String currentVersion) {
        if (StringUtils.isNotEmpty(deviceType) && StringUtils.isNotEmpty(currentVersion)) {
            String allowedAppVersion = "";
            if (ANDROID.equalsIgnoreCase(deviceType)) allowedAppVersion = allowedAppVersionAndroid;
            if (DEVICE_IOS.equalsIgnoreCase(deviceType)) allowedAppVersion = allowedAppVersionIOS;
            return compareVersions(currentVersion, allowedAppVersion);
        }
        return false;
    }

    public boolean checkAppVersionForCurrency(String deviceType, String currentVersion) {
        if (StringUtils.isNotEmpty(deviceType) && StringUtils.isNotEmpty(currentVersion)) {
            String allowedAppVersion = "";
            if (ANDROID.equalsIgnoreCase(deviceType)) allowedAppVersion = allowedAppVersionAndroidCurrency;
            if (DEVICE_IOS.equalsIgnoreCase(deviceType)) allowedAppVersion = allowedAppVersionIOSCurrency;
            if (CLIENT_DESKTOP.equalsIgnoreCase(deviceType) || DEVICE_OS_PWA.equalsIgnoreCase(deviceType)) return true;
            return compareVersions(currentVersion, allowedAppVersion);
        }
        return false;
    }

    public boolean compareVersions(String currentVersion, String allowedVersion) {
        if (org.apache.commons.lang.StringUtils.isNotEmpty(currentVersion) && org.apache.commons.lang.StringUtils.isNotEmpty(allowedVersion)) {
            try {
                String[] allowedVersionParts = allowedVersion.split("\\.");
                String[] currentVersionParts = currentVersion.split("\\.");
                int length = Math.max(allowedVersionParts.length, currentVersionParts.length);
                for (int i = 0; i < length; i++) {
                    int allowedVersionPart = i < allowedVersionParts.length ? Integer.parseInt(allowedVersionParts[i]) : 0;
                    int currentVersionPart = i < currentVersionParts.length ? Integer.parseInt(currentVersionParts[i]) : 0;
                    if (allowedVersionPart < currentVersionPart) {
                        return true;
                    } else if (allowedVersionPart > currentVersionPart) {
                        return false;
                    }
                }
                // If versions are equal
                return true;
            } catch (Exception e) {
                LOGGER.debug("Exception while comparing app versions: ", e.getMessage());
            }
        }
        return false;
    }

    public boolean isBusinessIdentificationEnableFromUserService(ExtendedUser extendedUser) {
        return extendedUser!=null && CollectionUtils.isNotEmpty(extendedUser.getAltVerifiedDetails());
    }

    /**
     * This method checks if the business identification feature is applicable or not.
     * It checks if the affiliate ID present in the list of businessIdentificationAffiliates.
     * If the affiliate ID is present and the business identification feature is enabled from the user service, it returns true.
     * Otherwise, it returns false.
     *
     * @param affiliateId affiliate ID of a particular user
     * @return boolean Returns true if the business identification feature is applicable, otherwise returns false.
     */
    public boolean isBusinessIdentificationApplicable(String affiliateId, Set<String> allowedSegments) {
        return allowedSegments != null && StringUtils.isNotEmpty(affiliateId) && !allowedSegments.isEmpty() && businessIdentificationAffiliates != null && businessIdentificationSegments != null && !businessIdentificationAffiliates.isEmpty() && !businessIdentificationSegments.isEmpty() && businessIdentificationAffiliates.contains(affiliateId) && checkSegments(allowedSegments, businessIdentificationSegments);
    }

    private boolean checkSegments(Set<String> allowedSegments, Set<String> configSegments) {
        if(allowedSegments == null || configSegments == null || allowedSegments.isEmpty() || configSegments.isEmpty())
            return false;
        // Iterate over each string in allowedSegments
        for (String segment : allowedSegments) {
            // Check if the segment is present in businessIdentificationSegments
            if (configSegments.contains(segment)) {
                return true;
            }
        }
        // If no segment from allowedSegments is found in businessIdentificationSegments, return false
        return false;
    }

    public String convertNumericValueToCommaSeparatedString(int number, Locale formatter) {
        NumberFormat numberFormat = NumberFormat.getNumberInstance(formatter);
        return numberFormat.format(number);
    }

    //Rate Plan level check for FlexiCancelAddOn and AddOnDetails
    public boolean isFlexiCancelAddOnAvailable(com.mmt.hotels.model.response.pricing.RatePlan ratePlan) {
        return ratePlan.getFlexiCancelAddOn() != null && MapUtils.isNotEmpty(ratePlan.getFlexiCancelAddOn().getAddOnPolicies()) &&
                ratePlan.getFlexiCancelAddOn().getAddOnPolicies().containsKey(FLEXI_CANCEL) &&
                ratePlan.getFlexiCancelAddOn().getAddOnPolicies().get(FLEXI_CANCEL) != null;
    }

    public boolean isAddOnDetailsAvailable(com.mmt.hotels.model.response.pricing.RatePlan ratePlan) {
        return ratePlan.getFlexiCancelDetails() != null && MapUtils.isNotEmpty(ratePlan.getFlexiCancelDetails().getAddOnDetails()) &&
                ratePlan.getFlexiCancelDetails().getAddOnDetails().containsKey(FLEXI_CANCEL) &&
                ratePlan.getFlexiCancelDetails().getAddOnDetails().get(FLEXI_CANCEL) != null;
    }

    public com.mmt.hotels.clientgateway.response.Selected getSelected(Selected selected) {
        com.mmt.hotels.clientgateway.response.Selected selectedResponse = null;
        if (selected != null) {
            selectedResponse = new com.mmt.hotels.clientgateway.response.Selected();
            BeanUtils.copyProperties(selected, selectedResponse);
        }
        return selectedResponse;
    }

    public ListPersonalizationResponse buildIhCashbackCard(HotelRates hotelRates, Set<String> hydraMatchedSegments, int ancillaryVariant){
        ListPersonalizationResponse listPersonalizationResponse = new ListPersonalizationResponse();
        com.mmt.hotels.clientgateway.response.moblanding.CardData cardData = null;
        try{
            if(hotelRates != null && MapUtils.isNotEmpty(ihCashbackCardConfig)){
                if(hotelRates.getMmtIhCabCashback() > 0.0 && hotelRates.getCabPrice() > 0.0 && ihCashbackCardConfig.containsKey(CABCASHBACKCARD)){
                    cardData = new CardData();
                    com.mmt.hotels.clientgateway.response.moblanding.CardData originalCardData = ihCashbackCardConfig.get(CABCASHBACKCARD);
                    cardData = deepCopyCardData(originalCardData); // Create a deep copy of the original cardData
                    int cabPrice = Math.toIntExact(Math.round(hotelRates.getMmtIhCabCashback() < hotelRates.getCabPrice() ? hotelRates.getMmtIhCabCashback() : hotelRates.getCabPrice()));
                    int cabDiscount = Math.toIntExact(Math.round(hotelRates.getMmtIhCabDiscount()));
                    int finalCabPrice = 0;
                    if(ancillaryVariant == 1){
                        finalCabPrice = cabPrice + cabDiscount;
                    } else {
                        finalCabPrice = cabPrice;
                    }
                    if(cardData != null && cardData.getCardInfo() != null && StringUtils.isNotEmpty(cardData.getCardInfo().getSubText())){
                        cardData.getCardInfo().setSubText(cardData.getCardInfo().getSubText().replace("{cab_price}",String.valueOf(finalCabPrice)).replace("{currency}",hotelRates.getCurrencyCode()));
                        if(StringUtils.isNotEmpty(hotelRates.getCabDetailDeepLink()) && CollectionUtils.isNotEmpty(cardData.getCardInfo().getCardAction())){
                            cardData.getCardInfo().getCardAction().get(0).setWebViewUrl(hotelRates.getCabDetailDeepLink());
                        }
                    }
                } else if(hotelRates.getMmtIhForexCashback() > 0.0 && ihCashbackCardConfig.containsKey(FOREXCASHBACKCARD)){
                    cardData = new CardData();
                    com.mmt.hotels.clientgateway.response.moblanding.CardData originalCardData = ihCashbackCardConfig.get(FOREXCASHBACKCARD);
                    int forexCashback = Math.toIntExact(Math.round(hotelRates.getMmtIhForexCashback()));
                    int forexDiscount = Math.toIntExact(Math.round(hotelRates.getMmtIhForexDiscount()));
                    cardData = deepCopyCardData(originalCardData); // Create a deep copy of the original cardData
                    int finalForexAmount = 0;
                    if(ancillaryVariant == 1){
                        finalForexAmount = forexCashback + forexDiscount;
                    } else{
                        finalForexAmount = forexCashback;
                    }
                    if(cardData != null && cardData.getCardInfo() != null && StringUtils.isNotEmpty(cardData.getCardInfo().getSubText())){
                        cardData.getCardInfo().setSubText(cardData.getCardInfo().getSubText().replace("{forex_price}",String.valueOf(finalForexAmount)).replace("{currency}",hotelRates.getCurrencyCode()));
                    }
                }
                if (cardData != null) {
                    listPersonalizationResponse.setCardData(new ArrayList<>());
                    listPersonalizationResponse.getCardData().add(cardData);
                }
                if(hydraMatchedSegments != null && checkSegments(hydraMatchedSegments,intlFlyerHydraSegmentIds) && CollectionUtils.isNotEmpty(listPersonalizationResponse.getCardData()) && listPersonalizationResponse.getCardData().get(0).getCardInfo() != null){
                    listPersonalizationResponse.getCardData().get(0).getCardInfo().setIconTags(buildIntlFlyerIconTag());
                }
            }
        } catch (Exception ex){
            LOGGER.error("Unable to build Crossell Cashback Card");
        }
        return listPersonalizationResponse;
    }

    public IconTag buildIntlFlyerIconTag(){
        IconTag iconTag = new IconTag();
        iconTag.setText(polyglotService.getTranslatedData(FLYER_EXCLUSIVE_DEAL_TEXT));
        com.mmt.hotels.model.persuasion.response.BgGradient bgGradient = new com.mmt.hotels.model.persuasion.response.BgGradient();
        bgGradient.setStart(FLYER_EXCLUSIVE_ICON_COLOR);
        bgGradient.setEnd(FLYER_EXCLUSIVE_ICON_COLOR);
        iconTag.setBgGradient(bgGradient);
        iconTag.setBorderColor(FLYER_EXCLUSIVE_ICON_BORDER_COLOR);
        return iconTag;
    }


    public void constraintLengthForAmenityPersuasions(PersuasionObject hotelPersuasion){
        if (hotelPersuasion.getData()!=null) {
            List<PersuasionData> dataList = hotelPersuasion.getData();
            int totalTextLimit=HN_TEXT_LIMIT_AMENITIES;
            int textLength = 0;
            int index=0;
            for(PersuasionData persuasionData :dataList){
                if (persuasionData.getText() != null) {
                    textLength += persuasionData.getText().length();
                    if (textLength > totalTextLimit) {
                        dataList = dataList.subList(0, index);
                        hotelPersuasion.setData(dataList);
                        break;
                    }
                }
                index++;
                totalTextLimit -=HN_TEXT_DEC_RATE;
            }
        }
    }

    public boolean showHighlightsForRoomAmenities(String countryCode, String funnelSource) {
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        return FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(funnelSource) && !DOM_COUNTRY.equalsIgnoreCase(countryCode) && !isRegionGccOrKsa(region);
    }

    public static CardData deepCopyCardData(CardData original) {
        if (original == null) {
            return null;
        }

        CardData copy = new CardData();
        copy.setSequence(original.getSequence());

        CardInfo originalCardInfo = original.getCardInfo();
        if (originalCardInfo != null) {
            CardInfo cardInfoCopy = new CardInfo();
            cardInfoCopy.setIndex(originalCardInfo.getIndex());
            cardInfoCopy.setSubType(originalCardInfo.getSubType());
            cardInfoCopy.setId(originalCardInfo.getId());
            cardInfoCopy.setTitleText(originalCardInfo.getTitleText());
            cardInfoCopy.setSubText(originalCardInfo.getSubText());
            cardInfoCopy.setIconURL(originalCardInfo.getIconURL());
            cardInfoCopy.setBgLinearGradient(deepCopyBGLinearGradient(originalCardInfo.getBgLinearGradient()));
            cardInfoCopy.setTemplateId(originalCardInfo.getTemplateId());
            cardInfoCopy.setHasTooltip(originalCardInfo.isHasTooltip());
            cardInfoCopy.setClaimed(originalCardInfo.isClaimed());
            // Deep copy the list of CardActions
            List<CardAction> originalCardActions = originalCardInfo.getCardAction();
            if (CollectionUtils.isNotEmpty(originalCardActions)) {
                List<CardAction> cardActionCopy = new ArrayList<>();
                for (CardAction originalAction : originalCardActions) {
                    if (originalAction != null) {
                        cardActionCopy.add(deepCopyCardAction(originalAction));
                    }
                }
                cardInfoCopy.setCardAction(cardActionCopy);
            }
            copy.setCardInfo(cardInfoCopy);
        }

        return copy;
    }

    private static CardAction deepCopyCardAction(CardAction originalAction) {
        if (originalAction == null) {
            return null;
        }
        CardAction copy = new CardAction();
        copy.setWebViewUrl(originalAction.getWebViewUrl());
        copy.setTitle(originalAction.getTitle());
        return copy;
    }

    private static BGLinearGradient deepCopyBGLinearGradient(BGLinearGradient originalBgLinearGradient) {
        if (originalBgLinearGradient == null) {
            return null;
        }

        BGLinearGradient copy = new BGLinearGradient();
        copy.setStart(originalBgLinearGradient.getStart());
        copy.setEnd(originalBgLinearGradient.getEnd());
        copy.setDirection(originalBgLinearGradient.getEnd());
        return copy;
    }


    public Double getTotalAmount(com.mmt.hotels.clientgateway.response.TotalPricing totalPricing) {
        if (totalPricing != null) {
            List<com.mmt.hotels.clientgateway.response.PricingDetails> detailsList = totalPricing.getDetails();
            if (detailsList != null) {
                for (com.mmt.hotels.clientgateway.response.PricingDetails detail : detailsList) {
                    if ("TOTAL_AMOUNT".equals(detail.getKey())) {
                        return detail.getAmount();
                    }
                }
            }
        }
        return null;
    }

    public Map<String, Persuasion> buildHomestayPersuasion(boolean isAltAcco, String sbpp, int roomCount, int totalAdults, double displayAmount, int noOfNightStays, String currency) {
        Map<String, Persuasion> persuasionMap = new HashMap<>();

        if (isAltAcco && StringUtils.isNotEmpty(sbpp)) {
            String sbppValue = sbpp;
            if ("1".equalsIgnoreCase(sbppValue)) {
                int isMulOne = roomCount*noOfNightStays;
                if (isMulOne > 1) {
                    displayAmount = displayAmount / (roomCount*noOfNightStays);
                    persuasionMap = persuasionUtil.buildHomestayPersuasionForReviewPage(numberFormatter!=null ? numberFormatter.format(displayAmount): String.valueOf(displayAmount), "1", currency);
                }
            } else if ("2".equalsIgnoreCase(sbppValue)) {
                if (totalAdults > 1) {
                    displayAmount = displayAmount / totalAdults;
                    persuasionMap = persuasionUtil.buildHomestayPersuasionForReviewPage(numberFormatter!=null ? numberFormatter.format(displayAmount) : String.valueOf(displayAmount), "2", currency);
                }
            }
        }

        return persuasionMap;
    }

    public SpaceData getSpaceDataV2(com.mmt.hotels.model.response.staticdata.SpaceData hesSpaceData, boolean isPrivateSpace, Set<Space> spacesList) {
        if (hesSpaceData == null)
            return null;
        SpaceData cgSpaceData = new SpaceData();
        int extraBedCount = 0, totalBaseOccupancy = 0, totalMaxOccupancy = 0;
        if (hesSpaceData != null) {
            cgSpaceData.setDescriptive(hesSpaceData.getDescriptive());
            cgSpaceData.setSharedInfo(buildSharedInfo(hesSpaceData.getSharedInfo()));
            List<Space> spaceList = new ArrayList<>();
            for(com.mmt.hotels.model.response.staticdata.Space hesSpace: hesSpaceData.getSpaces()){
                Space cgSpace = new Space();
                cgSpace.setName(hesSpace.getName());
                cgSpace.setSpaceId(hesSpace.getSpaceId());
                cgSpace.setSpaceType(hesSpace.getSpaceType());
                cgSpace.setAreaText(hesSpace.getAreaText());
                cgSpace.setSpaceInclusions(buildSpaceInclusion(hesSpace));
                if(isPrivateSpace){
//                    cgSpace.setSubText(hesSpace.getAreaText());
                    cgSpace.setAreaText(null);
                } else{
                    cgSpace.setDescriptionText(hesSpace.getDescriptionText());
                    String subText = hesSpace.getSubText();
                    if(hesSpace.getSpaceType() != null && (hesSpace.getSpaceType().equalsIgnoreCase(Constants.BEDROOM) || hesSpace.getSpaceType().equalsIgnoreCase(Constants.LIVING_ROOM))) {
                        int finalOccupancy = hesSpace.getFinalOccupancy();
                        int occupancy = max(finalOccupancy, hesSpace.getBaseOccupancy());
                        if (occupancy > 0)
                            subText = (occupancy > 1) ? polyglotService.getTranslatedData(ConstantsTranslation.SPACE_OCCUPANCY_TEXT) : polyglotService.getTranslatedData(ConstantsTranslation.SPACE_SINGLE_OCCUPANCY_TEXT);
                        else
                            subText = null;
                        if (subText != null) {
                            subText = subText.replace(OCCUPANCY_PARAMETER, String.valueOf(occupancy));
                        }
                    }
                    cgSpace.setSubText(subText);
                }
                if(CollectionUtils.isNotEmpty(hesSpace.getMedia())){
                    List<MediaData> mediaDataList = new ArrayList<>();
                    for(com.mmt.hotels.model.response.staticdata.MediaData hesMediaData: hesSpace.getMedia()){
                        MediaData cgMediaData = new MediaData();
                        cgMediaData.setMediaType(hesMediaData.getMediaType());
                        cgMediaData.setUrl(hesMediaData.getUrl());
                        mediaDataList.add(cgMediaData);
                    }
                    cgSpace.setMedia(mediaDataList);
                }
                spaceList.add(cgSpace);
                if(CollectionUtils.isNotEmpty(spaceList)) {
                    spacesList.addAll(spaceList);
                }
            }
            cgSpaceData.setSpaces(spaceList);
        }
        cgSpaceData.setBaseGuests(totalBaseOccupancy);
        cgSpaceData.setExtraBeds(extraBedCount);
        cgSpaceData.setMaxGuests(totalMaxOccupancy);
        return cgSpaceData;
    }

    public void removeUnwantedPersuasions(RoomDetails roomDetails) {
        if(roomDetails == null)
            return;
        roomDetails.setRoomSummary(null);
        try {
            if (roomDetails.getRoomPersuasions() != null) {
                List<String> requiredPlaceHolders = Arrays.asList("PLACEHOLDER_SELECT_M1", "PLACEHOLDER_SELECT_TOP_R1", "priceBottom");

                ObjectNode roomPersuasions = ((ObjectNode)roomDetails.getRoomPersuasions());
                Map<String, Object> liteRoomPersuasions = new LinkedHashMap<>();
                for (String key : requiredPlaceHolders) {
                    if (roomPersuasions.get(key) != null) {
                        liteRoomPersuasions.put(key, roomPersuasions.get(key));
                    }
                }
                if (MapUtils.isNotEmpty(liteRoomPersuasions)) {
                    roomDetails.setRoomPersuasions(liteRoomPersuasions);
                }else{
                    roomDetails.setRoomPersuasions(null);
                }
            }
        }catch (Exception ex){
            LOGGER.error("Error in removing unwanted persuasions.", ex);
        }
    }

    public SharedInfo buildSharedInfo(com.mmt.hotels.model.response.staticdata.SharedInfo hesSharedInfo) {
        if(hesSharedInfo == null)
            return null;
        SharedInfo sharedInfo = new SharedInfo();
        sharedInfo.setIconUrl(hesSharedInfo.getIconUrl());
        sharedInfo.setInfoText(hesSharedInfo.getInfoText());
        return  sharedInfo;
    }

    private List<String> buildSpaceInclusion(com.mmt.hotels.model.response.staticdata.Space hesSpace) {
        StringBuilder responseString = new StringBuilder();
        if (hesSpace != null) {
            if (hesSpace.getSleepingDetails() != null) {
                if (hesSpace.getSleepingDetails().getBedInfo() != null) {
                    for (com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedsInfo : hesSpace.getSleepingDetails().getBedInfo()) {
                        if (StringUtils.isNotEmpty(responseString.toString())) {
                            responseString.append(SPACE).append(AMP).append(SPACE).append(bedsInfo.getBedType());
                        } else {
                            responseString.append(bedsInfo.getBedType());
                        }
                    }
                }
                if (hesSpace.getSleepingDetails().getExtraBedInfo() != null) {
                    for (com.mmt.hotels.model.response.staticdata.SleepingBedInfo bedsInfo : hesSpace.getSleepingDetails().getExtraBedInfo()) {
                        responseString.append(COMMA_SPACE).append(EXTRA).append(SPACE).append(bedsInfo.getBedType()).append(SPACE).append(AVAILABLE.toLowerCase());
                    }
                }
            }
            if (StringUtils.isNotEmpty(responseString.toString()) && StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
                responseString.append(PIPE_SEPARATOR);
            }
            if(StringUtils.isNotEmpty(hesSpace.getDescriptionText())) {
                responseString.append(hesSpace.getDescriptionText());
            }
        }
        return StringUtils.isNotEmpty(responseString.toString()) ? Arrays.asList(responseString.toString().split(PIPE_SEPARATOR_WITH_BACKSLASH)) : null;
    }

    public MultiCurrencyInfo buildMultiCurrencyInfoRequest(com.mmt.hotels.clientgateway.request.MultiCurrencyInfo multiCurrencyInfoCG) {
        MultiCurrencyInfo multiCurrencyInfoHES = new MultiCurrencyInfo();
        multiCurrencyInfoHES.setUserCurrency(multiCurrencyInfoCG.getUserCurrency());
        multiCurrencyInfoHES.setRegionCurrency(multiCurrencyInfoCG.getRegionCurrency());
        return multiCurrencyInfoHES;
    }

    public BookedInclusion buildLosDiscountInclusion(Inclusion losInclusion){
        BookedInclusion bookedInclusion = null;
        if(losInclusion != null){
            bookedInclusion = new BookedInclusion();
            bookedInclusion.setInclusionCode(LONGSTAY);
            bookedInclusion.setIconType(IconType.DEFAULT);
            bookedInclusion.setIconUrl(losIconUrl);
            bookedInclusion.setText(losInclusion.getCode());
            bookedInclusion.setCode(losInclusion.getCode());
            bookedInclusion.setCategory(losInclusion.getCategory());
            bookedInclusion.setSegmentIdentifier(LOS);
        }
        return bookedInclusion;
    }

    public String buildRscValueForReview(List<AvailRoomsSearchCriteria> availRoomsSearchCriteriaList) {
        StringBuilder rscValue = new StringBuilder();
        if (CollectionUtils.isNotEmpty(availRoomsSearchCriteriaList)) {
            int rooms=0, adultCount=0, childCount=0;
            StringBuilder childAges = new StringBuilder();
            for (AvailRoomsSearchCriteria roomsSearchCriteria : availRoomsSearchCriteriaList) {
                if (CollectionUtils.isNotEmpty(roomsSearchCriteria.getRoomStayCandidates())) {
                    for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomsSearchCriteria.getRoomStayCandidates()) {
                        rooms++;
                        adultCount += roomStayCandidate.getAdultCount();
                        List<Integer> childAgesList = roomStayCandidate.getChildAges();
                        if (CollectionUtils.isNotEmpty(childAgesList)) {
                            childCount += childAgesList.size();
                            for (Integer age : childAgesList) {
                                if (age != null) {
                                    childAges.append(age).append(E);
                                }
                            }
                        }
                    }
                }
            }
            if(rooms > 0){
                rscValue.append(rooms).append(E);
            }
            if(adultCount > 0){
                rscValue.append(adultCount).append(E);
            }
            if(childCount > 0){
                rscValue.append(childCount).append(E).append(childAges);
            }
        }
        return rscValue.toString();
    }

    public BookedInclusion getIHGPaidChildInclusionIfApplicable(com.mmt.hotels.model.response.pricing.RatePlan ratePlanHES,
                                                                HotelRates hotelRates) {
        List<Integer> childAges = null;
        if (ratePlanHES != null && ratePlanHES.getAvailDetails() != null
                && null != ratePlanHES.getAvailDetails().getOccupancyDetails()) {
            childAges = ratePlanHES.getAvailDetails().getOccupancyDetails().getChildAges();
        }
        if (CollectionUtils.isEmpty(childAges) || hotelRates == null) {
            return null;
        }
        return getIHGPaidChildInclusionIfApplicable(
                hotelRates.getHotelChainCode(), hotelRates.getHtlAttributes(), childAges, hotelRates.getCountryCode()
        );
    }

    public BookedInclusion getIHGPaidChildInclusionIfApplicable(String hotelChainCode, Map<String, String> htlAttributes,
                                                                List<Integer> childAges, String countryCode) {
        // This inclusion is valid only for IH hotels.
        if (StringUtils.isBlank(countryCode) || DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
            return null;
        }
        if (StringUtils.isNotBlank(hotelChainCode)) {
            boolean isIHGChainHotel = ihgInclusionConfig != null
                    && CollectionUtils.isNotEmpty(ihgInclusionConfig.getChainCodes())
                    && ihgInclusionConfig.getChainCodes().contains(hotelChainCode);
            Integer maxAllowedAge = null;
            if (MapUtils.isNotEmpty(htlAttributes) && StringUtils.isNotBlank(htlAttributes.get(MAX_CHILD_AGE))) {
                try {
                    maxAllowedAge = Integer.parseInt(htlAttributes.get(MAX_CHILD_AGE));
                }
                catch (Exception e) {
                    LOGGER.error("Error while parsing max child age for hotel", e);
                }
            }
            Integer ratePlanMaxAge = CollectionUtils.isNotEmpty(childAges) ? Collections.max(childAges) : null;
            boolean isChildOverAge = false;
            if (ratePlanMaxAge != null && maxAllowedAge != null) {
                isChildOverAge = ratePlanMaxAge > maxAllowedAge;
            }
            if (isIHGChainHotel && StringUtils.isNotBlank(ihgInclusionConfig.getInclusionText()) && isChildOverAge) {
                BookedInclusion paidChildInclusion = new BookedInclusion();
                paidChildInclusion.setText(ihgInclusionConfig.getInclusionText());
                paidChildInclusion.setCode(paidChildInclusion.getText());
                paidChildInclusion.setIconType(IconType.DEFAULT);
                paidChildInclusion.setIconUrl(dotIconUrl);
                return paidChildInclusion;
            }
        }
        return null;
    }

    public boolean isRearchFlow(boolean rearchFlag, String requestId, Map<String, String> expDataMap) {
        if (StringUtils.isNotEmpty(requestId) && (requestId.startsWith("CG_QA") || requestId.startsWith("CG_LIVE"))) {
            if (requestId.contains("OrchV2")) {
                return rearchFlag && TRUE.equalsIgnoreCase(expDataMap.get(EXP_ORCHESTRATOR_V2));
            }
            return false;
        }
        return rearchFlag && TRUE.equalsIgnoreCase(expDataMap.get(EXP_ORCHESTRATOR_V2));
    }

    public boolean isWelcomeMMTCouponApplied(Map<CouponStatus, List<CouponInfo>> couponInfoMap){
       return MapUtils.isNotEmpty(couponInfoMap) && CollectionUtils.isNotEmpty(couponInfoMap.get(CouponStatus.APPLIED))
               && couponInfoMap.get(CouponStatus.APPLIED).stream().anyMatch(couponInfo -> WELCOMEMMT.equalsIgnoreCase(couponInfo.getCouponCode()));
    }

    public UserGlobalInfo buildUserGlobalInfoHES(com.mmt.hotels.clientgateway.request.UserGlobalInfo userGlobalInfo) {
        UserGlobalInfo userGlobalInfoHES = new UserGlobalInfo();
        userGlobalInfoHES.setUserCountry(userGlobalInfo.getUserCountry());
        userGlobalInfoHES.setEntityName(userGlobalInfo.getEntityName());
        return userGlobalInfoHES;
    }

    public ChatbotInfo buildChatbotInfoStaticDetail(ChatbotInfo chatbotInfo, boolean isTravelPlexEnabled) {
        ChatbotInfo chatbotInfoStaticDetail = null;
        if (chatbotInfo != null) {
            chatbotInfoStaticDetail = new ChatbotInfo();
            chatbotDetailsHes(chatbotInfo, chatbotInfoStaticDetail);
            consulStaticDetails(chatbotInfoStaticDetail, isTravelPlexEnabled);
        }
        return chatbotInfoStaticDetail;
    }

    private void chatbotDetailsHes(ChatbotInfo chatbotInfo, ChatbotInfo chatbotInfoStaticDetail) {
        chatbotInfoStaticDetail.setChatBotUrl(chatbotInfo.getChatBotUrl());
        chatbotInfoStaticDetail.setIconUrl(chatbotInfo.getIconUrl());
        chatbotInfoStaticDetail.setType(chatbotInfo.getType());
        chatbotInfoStaticDetail.setTooltipData(chatbotInfo.getTooltipData());
        chatbotInfoStaticDetail.setLobMetaData(chatbotInfo.getLobMetaData());
    }

    private void consulStaticDetails(ChatbotInfo chatbotInfoStaticDetail, boolean isTravelPlexEnabled) {
        if (chatBotInfoConsulV2 != null && isTravelPlexEnabled) {
            chatbotInfoStaticDetail.setBorderColor(chatBotInfoConsulV2.getBorderColor());
            chatbotInfoStaticDetail.setExpandBgColor(chatBotInfoConsulV2.getExpandBgColor());
            chatbotInfoStaticDetail.setPersuasions(chatBotInfoConsulV2.getPersuasions());
            String iconUrl = chatBotInfoConsulV2.getIconUrl();
            if (StringUtils.isNotEmpty(iconUrl)) {
                chatbotInfoStaticDetail.setIconUrl(iconUrl);
            }
            if (chatBotInfoConsulV2.getExpandDelay() != null) {
                chatbotInfoStaticDetail.setExpandDelay(chatBotInfoConsulV2.getExpandDelay());
            }
            if (chatBotInfoConsulV2.getTooltipData() != null) {
                chatbotInfoStaticDetail.setTooltipData(chatBotInfoConsulV2.getTooltipData());
            }
            if (chatBotInfoConsulV2.getPersuasionDelay() != null) {
                chatbotInfoStaticDetail.setPersuasionDelay(chatBotInfoConsulV2.getPersuasionDelay());
            }
            return;
        }
        if (chatbotInfoConsul != null) {
            chatbotInfoStaticDetail.setBorderColor(chatbotInfoConsul.getBorderColor());
            chatbotInfoStaticDetail.setExpandBgColor(chatbotInfoConsul.getExpandBgColor());
            chatbotInfoStaticDetail.setPersuasions(chatbotInfoConsul.getPersuasions());
            if (chatbotInfoConsul.getExpandDelay() != null) {
                chatbotInfoStaticDetail.setExpandDelay(chatbotInfoConsul.getExpandDelay());
            }
            if (chatbotInfoConsul.getPersuasionDelay() != null) {
                chatbotInfoStaticDetail.setPersuasionDelay(chatbotInfoConsul.getPersuasionDelay());
            }
        }
    }

    public void addHooksData(ChatbotInfo chatbotInfoStaticDetail, boolean showChatbotHooks, boolean isTravelPlexEnabled) {
        if(chatbotInfoStaticDetail == null) {
            return;
        }
        if (!showChatbotHooks) {
            return;
        }
        if (chatBotInfoConsulV2 != null && isTravelPlexEnabled) {
            chatbotInfoStaticDetail.setHooks(chatBotInfoConsulV2.getHooks());
            chatbotInfoStaticDetail.setHooksIconUrl(chatBotInfoConsulV2.getHooksIconUrl());
            return;
        }
        if (chatbotInfoConsul != null) {
            chatbotInfoStaticDetail.setHooks(chatbotInfoConsul.getHooks());
            chatbotInfoStaticDetail.setHooksIconUrl(chatbotInfoConsul.getHooksIconUrl());
        }
    }

    public HooksData buildChatbotHook(String name) {
        HooksData hooksData = null;
        if (StringUtils.isNotEmpty(name) && chatbotInfoMediaV2 != null && chatbotInfoMediaV2.containsKey(name.replace(" ", "").toLowerCase())) {
            hooksData = new HooksData();
            hooksData = chatbotInfoMediaV2.get(name.replace(" ", "").toLowerCase());
        }
        return hooksData;
    }

    public static com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo buildRuleTableInfo(RuleTableInfo ruleTableInfoCB){
        RuleTableInfo parsedRuleTableInfoCB = getParsedRuleTableInfo(ruleTableInfoCB);
        if (parsedRuleTableInfoCB == null) {
            return null;
        }
        com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo ruleTableInfo = new com.mmt.hotels.clientgateway.response.staticdetail.RuleTableInfo();
        ruleTableInfo.setKeyTitle(parsedRuleTableInfoCB.getKeyTitle());
        ruleTableInfo.setValueTitle(parsedRuleTableInfoCB.getValueTitle());
        ruleTableInfo.setInfoList(buildInfoList(parsedRuleTableInfoCB.getInfoList()));
        return ruleTableInfo;
    }

    public static RuleTableInfo getParsedRuleTableInfo(RuleTableInfo ruleTableInfo) {
        if (ruleTableInfo == null || CollectionUtils.isEmpty(ruleTableInfo.getInfoList())) {
            return null;
        }
        // keeping only non-null key value pairs from infoList
        List<RuleInfo> ruleInfoList = ruleTableInfo.getInfoList().stream()
                .filter(ruleInfo -> normalizeStringValues(ruleInfo.getKey()) != null && CollectionUtils.isNotEmpty(normalizeStringValues(ruleInfo.getValue())))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleInfoList)) {
            return null;
        }
        RuleTableInfo ruleTableInfoResponse = new RuleTableInfo();
        ruleTableInfoResponse.setKeyTitle(StringUtils.isBlank(ruleTableInfo.getKeyTitle()) ? EMPTY_STRING : ruleTableInfo.getKeyTitle());
        ruleTableInfoResponse.setValueTitle(StringUtils.isBlank(ruleTableInfo.getValueTitle()) ? EMPTY_STRING : ruleTableInfo.getValueTitle());
        ruleTableInfoResponse.setInfoList(ruleInfoList);
        return ruleTableInfoResponse;
    }

    private static List<com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo> buildInfoList(List<RuleInfo> ruleInfoList){
        List<com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo> ruleInfos = new ArrayList<>();
        for(RuleInfo ruleInfo : ruleInfoList) {
            com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo ruleInfoCG = new com.mmt.hotels.clientgateway.response.staticdetail.RuleInfo();
            ruleInfoCG.setKey(ruleInfo.getKey());
            ruleInfoCG.setValue(ruleInfo.getValue());
            ruleInfos.add(ruleInfoCG);
        }
        return ruleInfos;
    }

    public static String normalizeStringValues(String value) {
        if (StringUtils.isBlank(value) || NULL_STRING.equalsIgnoreCase(value)) {
            return null;
        }
        return value;
    }

    public static List<String> normalizeStringValues(List<String> values) {
        return values.stream().map(Utility::normalizeStringValues)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    public LinkedHashSet<DataList> populateDateList(DuplicateBookingDetails duplicateBookingDetails) {
        LinkedHashSet<DataList> dataList = new LinkedHashSet<>();
        DataList data;
        if(StringUtils.isNotEmpty(duplicateBookingDetails.getTravellerName())) {
            data = new DataList();
            data.setTitle(DuplicateBookingKey.DUPLICATE_BOOKING_PRIMARY_TRAVELLER_NAME.getValue());
            data.setValue(duplicateBookingDetails.getTravellerName());
            dataList.add(data);
        }
        if(StringUtils.isNotEmpty(duplicateBookingDetails.getBookingId())) {
            data = new DataList();
            data.setTitle(DuplicateBookingKey.DUPLICATE_BOOKING_BOOKING_ID.getValue());
            data.setValue(duplicateBookingDetails.getBookingId());
            dataList.add(data);
        }
        if(StringUtils.isNotEmpty(duplicateBookingDetails.getHotelName())) {
            data = new DataList();
            data.setTitle(DuplicateBookingKey.DUPLICATE_BOOKING_HOTEL_NAME.getValue());
            data.setValue(duplicateBookingDetails.getHotelName());
            dataList.add(data);
        }
        if(StringUtils.isNotEmpty(duplicateBookingDetails.getCheckInDate())) {
            data = new DataList();
            data.setTitle(DuplicateBookingKey.DUPLICATE_BOOKING_CHECK_IN_DATE.getValue());
            data.setValue(dateUtil.changeDateFormat(duplicateBookingDetails.getCheckInDate(), Constants.YYYY_MM_DD, Constants.dd_MMM_YYYY));
            dataList.add(data);
        }
        if(StringUtils.isNotEmpty(duplicateBookingDetails.getCheckInDate())) {
            data = new DataList();
            data.setTitle(DuplicateBookingKey.DUPLICATE_BOOKING_CHECK_OUT_DATE.getValue());
            data.setValue(dateUtil.changeDateFormat(duplicateBookingDetails.getCheckOutDate(), Constants.YYYY_MM_DD, Constants.dd_MMM_YYYY));
            dataList.add(data);
        }
        if(StringUtils.isNotEmpty(duplicateBookingDetails.getRoomName())) {
            data = new DataList();
            data.setTitle(DuplicateBookingKey.DUPLICATE_BOOKING_ROOM_NAME.getValue());
            data.setValue(duplicateBookingDetails.getRoomName());
            dataList.add(data);
        }
        if(StringUtils.isNotEmpty(duplicateBookingDetails.getTravellerEmailCommId()) && StringUtils.isNotEmpty(decode(duplicateBookingDetails.getTravellerEmailCommId()))) {
            data = new DataList();
            data.setTitle(DuplicateBookingKey.DUPLICATE_BOOKING_EMAIL.getValue());
            data.setValue(decode(duplicateBookingDetails.getTravellerEmailCommId()));
            dataList.add(data);
        }
        return dataList;
    }

    public String decode( String encodedString) {
        try {
            ScramblerClient scramblerClient = ScramblerClient.getInstance();
            return scramblerClient.decode(encodedString);
        } catch (Exception e) {
            LOGGER.error("Error while decoding the string", e);
        }
        return StringUtils.EMPTY;
    }
    public void populateCommonFields(ConsentData consentData) {
        consentData.setHeading(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_HEADING.name()));
        consentData.setTitle(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_TITLE.name()));
        consentData.setDescription(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_DESCRIPTION.name()));
        consentData.setSubHeading(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_SUB_HEADING.name()));
        consentData.setIconUrl(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_ICON_URL_MOBILE.name()));
        consentData.setDescription(polyglotService.getTranslatedData(DuplicateBookingKey.DUPLICATE_BOOKING_DESCRIPTION.name()));
        consentData.setTitleColor(DuplicateBookingKey.DUPLICATE_BOOKING_TITLE_COLOR.getValue());
        consentData.setStrokeColor(DuplicateBookingKey.DUPLICATE_BOOKING_STROKE_COLOR.getValue());
        BGLinearGradient bgLinearGradient = new BGLinearGradient();
        consentData.setBgLinearGradient(bgLinearGradient);

        bgLinearGradient.setStart(DuplicateBookingKey.DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_START.getValue());
        bgLinearGradient.setEnd(DuplicateBookingKey.DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_END.getValue());
        bgLinearGradient.setCenter(DuplicateBookingKey.DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_CENTER.getValue());
        bgLinearGradient.setDirection(DuplicateBookingKey.DUPLICATE_BOOKING_BG_LINEAR_GRADIENT_DIRECTION.getValue());
    }


    public String replaceWithFreeCancellation(String input) {
        String withFreeCancellationRegex = "(?i)with free cancellation";
        String freeCancellationRegex = "(?i)free cancellation";

        Pattern withFreeCancellationPattern = Pattern.compile(withFreeCancellationRegex);
        Pattern freeCancellationPattern = Pattern.compile(freeCancellationRegex);

        Matcher withFreeCancellationMatcher = withFreeCancellationPattern.matcher(input);
        if (withFreeCancellationMatcher.find()) {
            return withFreeCancellationMatcher.replaceAll("<s>with free cancellation</s>");
        }

        Matcher freeCancellationMatcher = freeCancellationPattern.matcher(input);
        if (freeCancellationMatcher.find()) {
            return freeCancellationMatcher.replaceAll("<s>free cancellation</s>");
        }

        return input;
    }
    public HotelPermissions buildHotelPermissions(Hotels permissions) {
		HotelPermissions hotelPermissions = new HotelPermissions();
		if (permissions.getSearchEnabled() != null && !permissions.getSearchEnabled()) {
			hotelPermissions.setSearchEnabled(buildHotelPermissionsInfo(permissions.getSearchEnabled(), Constants.SEARCH_ENABLED));
		}
		if (permissions.getBookingEnabled() != null && !permissions.getBookingEnabled()) {
			hotelPermissions.setBookingEnabled(buildHotelPermissionsInfo(permissions.getBookingEnabled(), Constants.BOOKING_ENABLED));
		}
		if (permissions.getBnplEnabled() != null && !permissions.getBnplEnabled()) {
			hotelPermissions.setBnplEnabled(buildHotelPermissionsInfo(permissions.getBnplEnabled(), Constants.BNPL_ENABLED));
		}
		return hotelPermissions;
	}

	public HotelPermissionsInfo buildHotelPermissionsInfo(Boolean permissionType, String polyglotKey) {
		HotelPermissionsInfo hotelPermissionsInfo = new HotelPermissionsInfo();
		hotelPermissionsInfo.setDisabled(!permissionType); // True if permission is false
		hotelPermissionsInfo.setIcon(mypartnerRestrictedIcon);
		hotelPermissionsInfo.setHeaderText(polyglotService.getTranslatedData(ConstantsTranslation.PERMISSION_HEADER_TEXT));
	
		String subHeaderTextKey;
		switch (polyglotKey.toUpperCase()) {
			case "SEARCH_ENABLED":
				subHeaderTextKey = ConstantsTranslation.PERMISSION_SEARCH_SUBHEADER_TEXT;
				break;
			case "BNPL_ENABLED":
				subHeaderTextKey = ConstantsTranslation.PERMISSION_BNPL_SUBHEADER_TEXT;
				break;
			default:
				subHeaderTextKey = ConstantsTranslation.PERMISSION_BOOKING_SUBHEADER_TEXT;
				break;
		}
		hotelPermissionsInfo.setSubHeaderText(polyglotService.getTranslatedData(subHeaderTextKey));
	
		return hotelPermissionsInfo;
	}

    public BGLinearGradient getBgLinearGradientForPriceVariationType(PriceVariationType priceVariationType) {
        switch (priceVariationType) {
            case DROP:
                return buildBgLinearGradientForPriceDrop();
            case SURGE:
                return buildBgLinearGradientForPriceSurge();
            default:
                return buildBgLinearGradientForPriceTypical();
        }
    }

    /**
     * Sets the canTranslate flag based on supplier code configuration
     */
    public static void setCanTranslateFlag(SelectRoomRatePlan ratePlan, List<String> translateEnabledSupplierCodes, String supplierCode) {
        if (ratePlan == null) {
            return;
        }

        if (CollectionUtils.isNotEmpty(translateEnabledSupplierCodes) && 
            StringUtils.isNotEmpty(supplierCode) &&
            translateEnabledSupplierCodes.contains(supplierCode)) {
            ratePlan.setCanTranslate(true);
        } else {
            ratePlan.setCanTranslate(false);
        }
    }
}