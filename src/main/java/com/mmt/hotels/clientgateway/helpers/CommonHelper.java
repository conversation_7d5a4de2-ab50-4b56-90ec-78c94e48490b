package com.mmt.hotels.clientgateway.helpers;

import com.fasterxml.jackson.core.type.TypeReference;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierRequest;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.ClientExpDataKeys;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.JsonParseException;
import com.mmt.hotels.clientgateway.exception.RestConnectorException;
import com.mmt.hotels.clientgateway.pms.CommonConfigHelper;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.pms.SourceRegionSpecificDataConfig;
import com.mmt.hotels.clientgateway.request.AvailPriceCriteria;
import com.mmt.hotels.clientgateway.request.BaseSearchRequest;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.FeatureFlags;
import com.mmt.hotels.clientgateway.request.Filter;
import com.mmt.hotels.clientgateway.request.ImageCategory;
import com.mmt.hotels.clientgateway.request.ImageDetails;
import com.mmt.hotels.clientgateway.request.ListingSearchRequest;
import com.mmt.hotels.clientgateway.request.ListingSearchRequestV2;
import com.mmt.hotels.clientgateway.request.MultiCurrencyInfo;
import com.mmt.hotels.clientgateway.request.RequestDetails;
import com.mmt.hotels.clientgateway.request.RoomStayCandidate;
import com.mmt.hotels.clientgateway.request.SearchCriteria;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.request.TreelsSearchCriteria;
import com.mmt.hotels.clientgateway.request.UserGlobalInfo;
import com.mmt.hotels.clientgateway.request.modification.ModifiedRoomStayCandidate;
import com.mmt.hotels.clientgateway.request.modification.RatePreviewRequest;
import com.mmt.hotels.clientgateway.request.modification.RateReviewSearchCriteria;
import com.mmt.hotels.clientgateway.request.wishlist.WishListedHotelsDetailRequest;
import com.mmt.hotels.clientgateway.response.corporate.WorkflowInfoResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.restexecutors.CorporateExecutor;
import com.mmt.hotels.clientgateway.restexecutors.HydraExecutor;
import com.mmt.hotels.clientgateway.restexecutors.PokusExperimentExecutor;
import com.mmt.hotels.clientgateway.restexecutors.UserServiceExecutor;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraEntity;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraEntityWrapper;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraUserIdObject;
import com.mmt.hotels.clientgateway.thirdparty.request.HydraUserSegmentRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.PokusContextRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.PokusExperimentRequest;
import com.mmt.hotels.clientgateway.thirdparty.request.PokusUserRequest;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraSegment;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraUserSegmentResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PokusAssignVariantResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.PokusExperimentDetails;
import com.mmt.hotels.clientgateway.thirdparty.response.PokusExperimentResponse;
import com.mmt.hotels.clientgateway.thirdparty.response.UserLoginInfo;
import com.mmt.hotels.clientgateway.thirdparty.response.UserServiceResponse;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import com.mmt.hotels.clientgateway.util.ObjectMapperUtil;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.request.corporate.Employee;
import com.mmt.hotels.model.request.flyfish.FlyFishReviewsRequest;
import com.mmt.hotels.model.request.flyfish.UserInfoDTO;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.response.searchwrapper.SearchWrapperHotelEntity;
import com.mmt.hotels.model.response.txn.CouponInfo;
import com.mmt.hotels.model.response.txn.CouponStatus;
import com.mmt.hotels.model.response.txn.PersistedMultiRoomData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.PredicateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.json.simple.parser.JSONParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
@PropertySource("classpath:searchAPI.properties")
@PropertySource("classpath:defaultAffiliateId.properties")
public class CommonHelper {

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private CommonConfigHelper commonConfigHelper;

	@Autowired
	private UserServiceExecutor userServiceExecutor;

	@Autowired
	private PokusExperimentExecutor pokusExperimentExecutor;

	@Autowired
	private HydraExecutor hydraExecutor;

	@Autowired
	private ObjectMapperUtil objectMapperUtil;

	@Autowired
	private MobConfigHelper mobConfigHelper;
	
    @Autowired
    private MetricErrorLogger metricErrorLogger;
    
    @Autowired
	private Environment env;

	@Autowired
	CommonConfigConsul commonConfigConsul;

	@Autowired
	CorporateExecutor corporateExecutor;

	@Autowired
	private PolyglotService polyglotService;


	@Value("${hotel.cohort.mob.show}")
	private boolean cohortValid;

	@Value("#{'${hotels.compApps.Dom.list}'.split(',')}")
	private List<String> cohertDom;

	@Value("#{'${hotels.compApps.Intl.list}'.split(',')}")
	private List<String> cohertIntl;

	@Value("#{'${hotels.compApps.list}'.split(',')}")
	private List<String> cohertApps;

	@Value("#{'${non.alt.acco.properties}'.split(',')}")
	private List<String> nonAltAccoProperties;

	@Value("#{'${enable.meta.trafficV2}'.split(',')}")
	private List<String> metaTrafficV2;

	@Value("#{'${getRate.traffic.sources}'.split(',')}")
	private List<String> getRateTraffic;

	@Autowired
	private DateUtil dateUtil;
    
    @Autowired
    @Qualifier("userServiceThreadPool")
    private ThreadPoolTaskExecutor userServiceThreadPool;
    
    @Autowired
    @Qualifier("hydraServiceThreadPool")
    private ThreadPoolTaskExecutor hydraServiceThreadPool;

	@Autowired
	Utility utility;

    private static final Gson gson = new Gson();
    
    private static final Logger logger = LoggerFactory.getLogger(CommonHelper.class);

	public ExtendedUser executePokusAndUserDetails(SearchCriteria searchCriteria, BaseSearchRequest baseSearchRequest,
			String correlationKey, Map<String, String> httpHeaderMap, String mmtAuth ,String mcId) {
		CommonModifierRequest commonReq = buildCommonRequest(baseSearchRequest,searchCriteria, mmtAuth, mcId);
		ExtendedUser extendedUser=  executePokusAndUserDetails(commonReq, httpHeaderMap, baseSearchRequest.getRequestDetails() != null ? baseSearchRequest.getRequestDetails().getFunnelSource() : Constants.FUNNEL_SOURCE_HOTELS, baseSearchRequest);
		if (MapUtils.isNotEmpty(commonReq.getManthanExpDataMap())) {
			baseSearchRequest.setManthanExpDataMap(commonReq.getManthanExpDataMap());
		}
		if (MapUtils.isNotEmpty(commonReq.getContentExpDataMap())) {
			baseSearchRequest.setContentExpDataMap(commonReq.getContentExpDataMap());
		}

		if (StringUtils.isNotBlank(commonReq.getExpVariantKeys())) {
			baseSearchRequest.setExpVariantKeys(commonReq.getExpVariantKeys());
		}

		baseSearchRequest.setValidExpList(commonReq.getValidExpList());
		baseSearchRequest.setVariantKeys(commonReq.getVariantKeys());

		return  extendedUser;
    }

    public ExtendedUser executePokusAndUserDetails(CommonModifierRequest commonModifierRequest, Map<String,String> httpHeaderMap, String funnelSource, BaseSearchRequest baseSearchRequest){
		try {

			CountDownLatch countDownLatchAsync = new CountDownLatch(1);
			List<UserServiceResponse> userServiceResponseList = new ArrayList<UserServiceResponse>();
			getUserDetailsAsync( httpHeaderMap, countDownLatchAsync, userServiceResponseList,
					commonModifierRequest, MDC.getCopyOfContextMap());
			PokusExperimentResponse pokusExperimentResponse = getPokusExperimentResponse( httpHeaderMap,commonModifierRequest,funnelSource,userServiceResponseList);
			baseSearchRequest.setExpDataMap(updateExperimentDataMap(pokusExperimentResponse, commonModifierRequest, funnelSource));
			commonModifierRequest.setValidExpList(getValidExpList(pokusExperimentResponse));
			commonModifierRequest.setVariantKeys(getVariantKeys(pokusExperimentResponse));
			countDownLatchAsync.await();
			UserServiceResponse userServiceResponse = null;
			if (CollectionUtils.isNotEmpty(userServiceResponseList))
				userServiceResponse = userServiceResponseList.get(0);
			modifyPokusBySource(userServiceResponse, baseSearchRequest);
			if(userServiceResponse!=null && userServiceResponse.getResult()!=null){
				return userServiceResponse.getResult().getExtendedUser();
			}
		} catch (Exception e) {
			if (e instanceof ClientGatewayException)
				metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "executePokusAndUserDetails");
			else
				metricErrorLogger.logGeneralException(e, "executePokusAndUserDetails", DependencyLayer.CLIENTGATEWAY,
						ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
		}
		return null;
	}
	/**
	 * This method modifies the experiment data based on the user's traffic source.
	 * If the user is not from the MyPartner traffic source, it removes the MyPartner experiment keys from the experiment data map.
	 * If the user is from the MyPartner traffic source, it overrides the MyPartner experiments in the experiment data map.
	 *
	 * @param userServiceResponse The response from the UserService, which contains user details including the affiliateId.
	 * @param baseSearchRequest The base search request, which contains the experiment data map that needs to be modified.
	 */
	public void modifyPokusBySource(UserServiceResponse userServiceResponse, BaseSearchRequest baseSearchRequest) {
		// Removing MyPartner experiment keys if user is not from MyPartner traffic source
		if (Objects.isNull(userServiceResponse) || Objects.isNull(userServiceResponse.getResult())
				|| Objects.isNull(userServiceResponse.getResult().getExtendedUser())
				|| !MYPARTNER.equalsIgnoreCase(userServiceResponse.getResult().getExtendedUser().getAffiliateId())) {
			baseSearchRequest.getExpDataMap().remove(MP_EXPRESS_CHECKOUT_EXP_KEY);
			baseSearchRequest.getExpDataMap().remove(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY);
			baseSearchRequest.getExpDataMap().remove(MP_GST_SAVING_EXP_KEY);
			baseSearchRequest.getExpDataMap().remove(MP_CUSTOMER_GST_EXP_KEY);
			baseSearchRequest.getExpDataMap().remove(EXP_MYPARTNER_LISTING_HN);
			logger.debug("Removing MyPartner experiment keys !!");
		}
		// Overriding MyPartner experiments if user is from MyPartner traffic source
		else {
			baseSearchRequest.getExpDataMap().put(MP_EXPRESS_CHECKOUT_EXP_KEY, TRUE);
			baseSearchRequest.getExpDataMap().put(MP_MOVE_TO_TDS_STRUCTURE_EXP_KEY, TRUE);
			baseSearchRequest.getExpDataMap().put(MP_GST_SAVING_EXP_KEY, TRUE);
			baseSearchRequest.getExpDataMap().put(MP_CUSTOMER_GST_EXP_KEY, FALSE);
			logger.debug("Overriding MyPartner experiments !!");
		}
	}

	private String getVariantKeys(PokusExperimentResponse pokusExperimentResponse){
		if(pokusExperimentResponse!=null && MapUtils.isNotEmpty(pokusExperimentResponse.getPerLobMap())
				&& pokusExperimentResponse.getPerLobMap().containsKey("HOTEL")
				&& StringUtils.isNotEmpty(pokusExperimentResponse.getPerLobMap().get("HOTEL").getVariantKey())){
			return pokusExperimentResponse.getPerLobMap().get("HOTEL").getVariantKey();
		}
		return "";
	}

	private List<String> getValidExpList(PokusExperimentResponse pokusExperimentResponse){
		List<String> validExpList = new ArrayList<>();
		if(pokusExperimentResponse!=null && MapUtils.isNotEmpty(pokusExperimentResponse.getPerLobMap())
				&& pokusExperimentResponse.getPerLobMap().containsKey("HOTEL")
				&& CollectionUtils.isNotEmpty(pokusExperimentResponse.getPerLobMap().get("HOTEL").getExperimentDetailsList())){
			for (PokusExperimentDetails pokusExperimentDetails:pokusExperimentResponse.getPerLobMap().get("HOTEL").getExperimentDetailsList()){
				StringBuilder temp = new StringBuilder();
				temp.append(String.valueOf(pokusExperimentDetails.getExperimentId()))
						.append(PIPE_SEPARATOR)
						.append(String.valueOf(pokusExperimentDetails.getExperimentVersion()))
						.append(PIPE_SEPARATOR)
						.append(String.valueOf(pokusExperimentDetails.getVariantId()));
				validExpList.add(String.valueOf(temp));
			}
		}
		return validExpList;
	}
	
	private Map<String, String> updateExperimentDataMap(PokusExperimentResponse pokusExperimentResponse,
										   CommonModifierRequest commonModifierRequest, String funnelSource) throws JsonParseException {
		if (pokusExperimentResponse == null || MapUtils.isEmpty(pokusExperimentResponse.getPerLobMap())) {
			Map<String, String> existingExpData = new HashMap<>();
			if (commonModifierRequest != null && !StringUtils.isEmpty(commonModifierRequest.getExpData())) {
				String experimentString = commonModifierRequest.getExpData().replaceAll("^\"|\"$", "");
				Type type = new TypeToken<Map<String, String>>() {
				}.getType();
				//TODO
				existingExpData = gson.fromJson(experimentString, type);
				if(DEVICE_IOS.equalsIgnoreCase(commonModifierRequest.getBookingDevice()) || DEVICE_OS_ANDROID.equalsIgnoreCase(commonModifierRequest.getBookingDevice())){
					existingExpData.put(EXP_CONTEXTUAL_FILTER,EXP_CONTEXTUAL_FILTER_ENABLE_VALUE);
				}
			}
			if(existingExpData.containsKey("ldrsegregation")){
				MDC.put(MDCHelper.MDCKeys.LDRSEGREGATION.getStringValue(), existingExpData.get("ldrsegregation"));
			}
			return existingExpData;
		}
		PokusAssignVariantResponse hotelLobExp = pokusExperimentResponse.getPerLobMap().get("HOTEL");
		Map<String, String> expData = new HashMap<>();
		Map<String, String> manthanExpData = new HashMap<>();
		Map<String, String> contentExpData = new HashMap<>();
		for (String key : hotelLobExp.getMetadataValues().keySet()) {
			expData.put(key, String.valueOf(hotelLobExp.getMetadataValues().get(key)));
			if (key.startsWith("ddAPI")) {
				manthanExpData.put(key, String.valueOf(hotelLobExp.getMetadataValues().get(key)));
			}
			if (key.startsWith("HSC")) {
				contentExpData.put(key, String.valueOf(hotelLobExp.getMetadataValues().get(key)));
			}
			if (key.startsWith("dynamichcp")) {
				manthanExpData.put(key, String.valueOf(hotelLobExp.getMetadataValues().get(key)));
			}
		}
		updateExpDataForPricerV2(expData);

		if (StringUtils.isNotBlank(hotelLobExp.getVariantKey())) {
			commonModifierRequest.setExpVariantKeys(hotelLobExp.getVariantKey());
		}
		if(expData.containsKey("ldrsegregation")){
			MDC.put(MDCHelper.MDCKeys.LDRSEGREGATION.getStringValue(), expData.get("ldrsegregation"));
		}
		logger.warn("Total pokus experiment data count : {} and manthan experimant data count : {} and content experimant data count : {}", expData.size(), manthanExpData.size(), contentExpData.size());
		commonModifierRequest.setManthanExpDataMap(manthanExpData);
		commonModifierRequest.setContentExpDataMap(contentExpData);
		Map<String, String> existingExpData = new HashMap<>();
		if (StringUtils.isNotEmpty(commonModifierRequest.getExpData())) {
			String experimentString = commonModifierRequest.getExpData().replaceAll("^\"|\"$", "");
			Type type = new TypeToken<Map<String, String>>() {
			}.getType();
			existingExpData = gson.fromJson(experimentString, type);
		}

		if (existingExpData.containsKey(Constants.CHAT_GPT_SUMMARY_EXP) && (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(commonModifierRequest.getBookingDevice())
				|| Constants.DEVICE_OS_PWA.equalsIgnoreCase(commonModifierRequest.getBookingDevice()))) {
			expData.put(Constants.CHAT_GPT_SUMMARY_EXP, existingExpData.get(Constants.CHAT_GPT_SUMMARY_EXP));
		}

		existingExpData.putAll(expData);
		//Adding default ALC experiment is not received from client
		if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)) {
			existingExpData.put(Constants.HOMESTAY_PERSUASION_ALC, "T");
		}else {
			existingExpData.put(Constants.HOMESTAY_PERSUASION_ALC,"F");
		}
		if(Constants.TRAFFIC_SOURCE_FLIGHTS_THANKYOU_PAGE.equalsIgnoreCase(commonModifierRequest.getTrafficSource())
		|| TRAFFIC_SOURCE_BUSES_THANKYOU_PAGE.equalsIgnoreCase(commonModifierRequest.getTrafficSource())
		|| TRAFFIC_SOURCE_TRAINS_THANKYOU_PAGE.equalsIgnoreCase(commonModifierRequest.getTrafficSource())
		|| TRAFFIC_SOURCE_VISTARA_FLIGHTS.equalsIgnoreCase(commonModifierRequest.getTrafficSource())){
			existingExpData.put(Constants.PRICE_DISPLAY_OPTION, Constants.PER_NIGHT);
		}
		if(DEVICE_IOS.equalsIgnoreCase(commonModifierRequest.getBookingDevice()) || DEVICE_OS_ANDROID.equalsIgnoreCase(commonModifierRequest.getBookingDevice())){
			existingExpData.put(EXP_CONTEXTUAL_FILTER,EXP_CONTEXTUAL_FILTER_ENABLE_VALUE);
		}
		existingExpData.put(UNIFIED_USER_RATING, "true");
		existingExpData.put(NEW_PDT_LOGGING_ENABLED, "true");

		if(!utility.isExperimentOn(existingExpData, ClientExpDataKeys.CLIENT_EXP_LISTING_API_MIGRATION)) {
			existingExpData.put(ExperimentKeys.EXP_OFFERS_UI_REDESIGN.getKey(), "false");
		}
		updateConcludedExperiments(existingExpData);
		return existingExpData;
	}

	private void updateConcludedExperiments(Map<String, String> existingExpData) {
		Map<String, String> concludedExperiments = commonConfigConsul.getConcludedExperiments();
		if(MapUtils.isNotEmpty(concludedExperiments)) {
			logger.debug("Total concluded experiments: {}",concludedExperiments.size());
			existingExpData.putAll(concludedExperiments);
		}
	}

	private void updateExpDataForPricerV2(Map<String, String> expData) {
		expData.put("pricerV2", "TRUE");
	}

	private  CommonModifierRequest buildCommonRequest(RatePreviewRequest rateRequest , Map<String,String> httpHeaderMap, String pageContext){
		CommonModifierRequest commonModifierRequest = new CommonModifierRequest();
		commonModifierRequest.setCorrelationKey(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue()));
		commonModifierRequest.setRegion(httpHeaderMap.get("REGION"));
		commonModifierRequest.setBookingDevice(rateRequest.getClient());
		commonModifierRequest.setPageContext(pageContext);
		commonModifierRequest.setChannel(rateRequest.getChannel());
		commonModifierRequest.setCorpAuthCode(null);
		commonModifierRequest.setUuid(null);
		commonModifierRequest.setCityCode(rateRequest.getCityCode());
		commonModifierRequest.setLocationId(rateRequest.getCityCode());
		commonModifierRequest.setIdContext(rateRequest.getIdContext());
		commonModifierRequest.setAppVersion(rateRequest.getAppVersion());
		commonModifierRequest.setDeviceId(rateRequest.getDeviceId());
		commonModifierRequest.setCountryCode(rateRequest.getCountryCode());
		commonModifierRequest.setCurrency(rateRequest.getCurrency());
		commonModifierRequest.setExpData("{}");
		String checkIn = null;
		String checkOut = null;
		if (rateRequest != null) {
			checkOut = rateRequest.getCheckout();
			checkIn = rateRequest.getCheckin();
			commonModifierRequest.setCheckInDate(checkIn);
			commonModifierRequest.setApWindow(getAdvancePurchaseParam(checkIn));
			if (CollectionUtils.isNotEmpty(rateRequest.getRoomCriteria())) {
				List<ModifiedRoomStayCandidate> roomStayCandidatesList = (rateRequest.getRoomCriteria().stream().map(RateReviewSearchCriteria::getRoomStayCandidates).flatMap(List::stream).collect(Collectors.toList()));
				commonModifierRequest.setRoomCount(CollectionUtils.isNotEmpty(roomStayCandidatesList) ? roomStayCandidatesList.size() : 0);
				Pair<Integer, Integer> adultsAndChildCount = utility.getTotalAdultsAndChildFromBKGModRequest(roomStayCandidatesList);
				commonModifierRequest.setAdultCount(adultsAndChildCount.getKey());
				commonModifierRequest.setChildCount(adultsAndChildCount.getValue());
			}
			buildLos(commonModifierRequest,checkIn,checkOut);
		}

		return commonModifierRequest;
	}

	private CommonModifierRequest buildCommonRequest(BaseSearchRequest baseSearchRequest, SearchCriteria searchCriteria, String mmtAuth, String mcid){
		CommonModifierRequest commonModifierRequest = new CommonModifierRequest();
		commonModifierRequest.setMmtAuth(mmtAuth);
		commonModifierRequest.setMcid(mcid);
		commonModifierRequest.setCorrelationKey(baseSearchRequest.getCorrelationKey());
		commonModifierRequest.setRegion(baseSearchRequest.getRequestDetails().getSiteDomain());
		commonModifierRequest.setBookingDevice(baseSearchRequest.getDeviceDetails().getBookingDevice());
		commonModifierRequest.setPageContext(baseSearchRequest.getRequestDetails().getPageContext());
		commonModifierRequest.setSubPageContext(baseSearchRequest.getRequestDetails().getSubPageContext());
		commonModifierRequest.setChannel(baseSearchRequest.getRequestDetails().getChannel());
		commonModifierRequest.setCorpAuthCode(baseSearchRequest.getRequestDetails().getCorpAuthCode());
		commonModifierRequest.setUserIntent(baseSearchRequest.getRequestDetails().isPremium() ? PREMIUM : DEFAULT);
		commonModifierRequest.setUuid(baseSearchRequest.getRequestDetails().getUuid());
		if(baseSearchRequest.getRequestDetails() != null && StringUtils.isNotBlank(baseSearchRequest.getRequestDetails().getRequestor())) {
			commonModifierRequest.setRequester(baseSearchRequest.getRequestDetails().getRequestor());
		}

		Map<String, String> expDataMap = new HashMap<>();
		if (baseSearchRequest != null && StringUtils.isNotEmpty(baseSearchRequest.getExpData())) {
			expDataMap = utility.getExpDataMap(baseSearchRequest.getExpData());
		}
		if (utility.isExperimentOn(expDataMap, BEDROOM_COUNT_AVAILABLE) && CollectionUtils.isNotEmpty(searchCriteria.getRoomStayCandidates()) && Objects.nonNull(searchCriteria.getRoomStayCandidates().get(0))) {
			int bedRoomCount = searchCriteria.getRoomStayCandidates().get(0).getRooms() == null ? 0 : searchCriteria.getRoomStayCandidates().get(0).getRooms();
			commonModifierRequest.setBedRoomCount(bedRoomCount);
		}
		if(searchCriteria!=null) {
			commonModifierRequest.setCityCode(searchCriteria.getCityCode());
			commonModifierRequest.setLocationId(searchCriteria.getLocationId());
			commonModifierRequest.setLocationType(searchCriteria.getLocationType());
		}
		commonModifierRequest.setIdContext(baseSearchRequest.getRequestDetails().getIdContext());
		commonModifierRequest.setAppVersion(baseSearchRequest.getDeviceDetails().getAppVersion());
		commonModifierRequest.setDeviceId(baseSearchRequest.getDeviceDetails().getDeviceId());
		commonModifierRequest.setVisitorId(baseSearchRequest.getRequestDetails().getVisitorId());
		commonModifierRequest.setExpData(baseSearchRequest.getExpData());
		if(searchCriteria!=null) {
			commonModifierRequest.setApWindow(getAdvancePurchaseParam(searchCriteria.getCheckIn()));
			commonModifierRequest.setCurrency(null != searchCriteria.getCurrency() ? searchCriteria.getCurrency() : Constants.DEFAULT_CUR_INR);
			commonModifierRequest.setCountryCode(searchCriteria.getCountryCode());
		}
		commonModifierRequest.setTrafficSource(ObjectUtils.isNotEmpty(baseSearchRequest.getRequestDetails().getTrafficSource())?(StringUtils.isNotEmpty(baseSearchRequest.getRequestDetails().getTrafficSource().getSource()) ? baseSearchRequest.getRequestDetails().getTrafficSource().getSource(): ""):"");
		commonModifierRequest.setTrafficAudience(baseSearchRequest.getRequestDetails() != null &&
			baseSearchRequest.getRequestDetails().getTrafficSource() != null ?
			baseSearchRequest.getRequestDetails().getTrafficSource().getAud() : null);
		if(searchCriteria!=null && StringUtils.isNotEmpty(searchCriteria.getCheckIn())) {
			commonModifierRequest.setCheckInDate(searchCriteria.getCheckIn());
		}
		String checkIn = null;
		String checkOut = null;
		if (searchCriteria != null) {
			checkOut = searchCriteria.getCheckOut();
			checkIn = searchCriteria.getCheckIn();
			// In Review hit, rooms node is null in roomStayCandidates. Room Count is figured out by size of the list
			if(baseSearchRequest != null && baseSearchRequest.getRequestDetails() != null && PAGE_CONTEXT_REVIEW.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getPageContext()))
				commonModifierRequest.setRoomCount(CollectionUtils.isNotEmpty(searchCriteria.getRoomStayCandidates()) ? searchCriteria.getRoomStayCandidates().size() : 0);
			else
				commonModifierRequest.setRoomCount(CollectionUtils.isNotEmpty(searchCriteria.getRoomStayCandidates()) && searchCriteria.getRoomStayCandidates().get(0).getRooms() != null ? searchCriteria.getRoomStayCandidates().get(0).getRooms() : 0);
			commonModifierRequest.setAdultCount(utility.getTotalAdultsFromRequest(searchCriteria.getRoomStayCandidates()));
			commonModifierRequest.setChildCount(utility.getTotalChildrenFromRequest(searchCriteria.getRoomStayCandidates()));
			buildLos(commonModifierRequest,checkIn,checkOut);
			if(commonModifierRequest.getLos() != null && commonModifierRequest.getRoomCount() != null)
				commonModifierRequest.setRnCount(commonModifierRequest.getLos() * commonModifierRequest.getRoomCount());
		}
		return commonModifierRequest;
	}
	private void buildLos(CommonModifierRequest commonModifierRequest, String checkIn, String checkOut) {
		if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
			try {
				commonModifierRequest.setLos(dateUtil.getDaysDiff(checkIn, checkOut));
			} catch (Exception ex) {
				logger.debug("Error While Building Los {}",ex.getMessage());
			}
		}
	}

	private PokusExperimentResponse getPokusExperimentResponse(
															   Map<String, String> httpHeaderMap, CommonModifierRequest commonModifierRequest,String funnelSource,List<UserServiceResponse> userServiceResponseList) throws ClientGatewayException, ParseException {
		String cityCode = getCityCodeForLocus(commonModifierRequest.getCityCode(),commonModifierRequest.getLocationId());
		PokusExperimentRequest pokusExperimentRequest = new PokusExperimentRequest();
		Map<String,Object> attributes = new HashMap<>();
		attributes.put("city",cityCode);
		attributes.put("locationId",commonModifierRequest.getLocationId());
		attributes.put("locationType",commonModifierRequest.getLocationType());
		attributes.put("contextId",commonModifierRequest.getIdContext());
		attributes.put("idContext",commonModifierRequest.getIdContext());
		attributes.put("funnel",commonModifierRequest.getBookingDevice());
		attributes.put("device_os", getDeviceOS(commonModifierRequest.getBookingDevice()));
		attributes.put("currency", commonModifierRequest.getCurrency());
		attributes.put("region", commonModifierRequest.getRegion());
		attributes.put("trafficSource", commonModifierRequest.getTrafficSource());
		if(StringUtils.isNotBlank(commonModifierRequest.getRequester())) {
			attributes.put("requester", commonModifierRequest.getRequester());
		} else if (httpHeaderMap.containsKey(SRC_CLIENT) && REQUEST_SOURCE_SCION.equalsIgnoreCase(httpHeaderMap.get(SRC_CLIENT))) {
			attributes.put("requester", REQUEST_SOURCE_SCION);
		}

		// Add trafficAudience parameter if available in commonModifierRequest
		if (StringUtils.isNotEmpty(commonModifierRequest.getTrafficAudience())) {
			attributes.put("trafficAudience", commonModifierRequest.getTrafficAudience());
		}

		if(StringUtils.isNotEmpty(commonModifierRequest.getCheckInDate())){
			attributes.put("checkindate", changeDateStringToInteger(commonModifierRequest.getCheckInDate()));
		}

		if(StringUtils.isNotEmpty(funnelSource)) {
			attributes.put("funnel_src", getFunnelSource(funnelSource,userServiceResponseList));
		}
		attributes.put("countryCode",commonModifierRequest.getCountryCode());
		if(null!=commonModifierRequest.getApWindow()){
			attributes.put("apWindow",commonModifierRequest.getApWindow());
		}
		if(Constants.ANDROID.equalsIgnoreCase(commonModifierRequest.getBookingDevice()) || Constants.DEVICE_IOS.equalsIgnoreCase(commonModifierRequest.getBookingDevice()))
			attributes.put("app_version",commonModifierRequest.getAppVersion());
		CollectionUtils.filter(attributes.values(), PredicateUtils.notNullPredicate());
		if (commonModifierRequest.getLos() != null && commonModifierRequest.getLos()!=0) {
			attributes.put("los", commonModifierRequest.getLos());
		}
		if (commonModifierRequest.getAdultCount() != null && commonModifierRequest.getAdultCount()!=0) {
			attributes.put("adultCount", commonModifierRequest.getAdultCount());
		}
		if (commonModifierRequest.getChildCount() != null) {
			attributes.put("childCount", commonModifierRequest.getChildCount());
		}
		if (commonModifierRequest.getRoomCount() != null && commonModifierRequest.getRoomCount()!=0) {
			attributes.put("roomCount", commonModifierRequest.getRoomCount());
		}
		if(commonModifierRequest.getBedRoomCount() != null && FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(funnelSource)){
			attributes.put("roomCount", commonModifierRequest.getBedRoomCount());
		}

		if (commonModifierRequest.getRnCount() != 0) {
			attributes.put("rnCount", commonModifierRequest.getRnCount());
		}
		attributes.put("is_logged_in", checkIfLoggedIn(userServiceResponseList) ? 1 : 0);
		attributes.put("user_intent", StringUtils.isNotEmpty(commonModifierRequest.getUserIntent()) ? commonModifierRequest.getUserIntent() : DEFAULT);
		if(CORP_ID_CONTEXT.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.IDCONTEXT.getStringValue()))){
			attributes.put(ORG_ID, getOrganizationID(userServiceResponseList));
		}
		pokusExperimentRequest.setAttributes(attributes);
		PokusUserRequest pokusUserRequest = new PokusUserRequest();
		String deviceInfo = getDeviceinfo(commonModifierRequest.getDeviceId(), commonModifierRequest.getMcid(), commonModifierRequest.getVisitorId());
		if (StringUtils.isBlank(deviceInfo))
			return null;

		pokusUserRequest.setDeviceId(deviceInfo);
		if(StringUtils.isNotEmpty(httpHeaderMap.get("mmt-auth"))) {
			pokusUserRequest.setMmtAuth(httpHeaderMap.get("mmt-auth"));
		}
		pokusExperimentRequest.setUser(pokusUserRequest);
		if (StringUtils.isNotEmpty(commonModifierRequest.getPageContext())) {
			PokusContextRequest pokusContextRequest = new PokusContextRequest();
			pokusContextRequest.setPageName(commonModifierRequest.getPageContext());
			pokusContextRequest.setSubPageName(commonModifierRequest.getSubPageContext());
			pokusExperimentRequest.setContext(pokusContextRequest);
		}
		PokusExperimentResponse response = null;
		try {
			response = pokusExperimentExecutor.getPokusExperimentResponse(pokusExperimentRequest, httpHeaderMap, commonModifierRequest.getMmtAuth());
		} catch (Exception e){
			logger.error("Error in pokus response - ", e);
		}
		return response;
	}

	private String getOrganizationID(List<UserServiceResponse> userServiceResponseList) throws JsonParseException {
		if (CollectionUtils.isNotEmpty(userServiceResponseList)) {
			UserServiceResponse userServiceResponse = userServiceResponseList.get(0);
			if(userServiceResponse != null && userServiceResponse.getResult() != null
					&& userServiceResponse.getResult().getExtendedUser() != null &&
					StringUtils.isNotEmpty(userServiceResponse.getResult().getExtendedUser().getCorporateData())) {
				Map<String, Object> corporateData = objectMapperUtil.getObjectFromJsonWithType(userServiceResponse.getResult().getExtendedUser().getCorporateData(), new TypeReference<Map<String, Object>>() {
				}, DependencyLayer.CLIENTGATEWAY);
				if(MapUtils.isNotEmpty(corporateData) && corporateData.containsKey(EMPLOYEE)) {
					String employeeDataJSON = gson.toJson((Map<String, String>)corporateData.get(EMPLOYEE), HashMap.class);
					Employee employeeData = gson.fromJson(employeeDataJSON, Employee.class);

					if(employeeData != null) {
						return employeeData.getOrganizationId();
					}
				}
			}
		}
		return null;
	}

	private int changeDateStringToInteger(String checkInDate) throws ParseException {
		try {
			String[] parsedDate = (checkInDate.replaceAll("[-:/.]","").split(" "));
			if(parsedDate.length>0){
				return Integer.parseInt(parsedDate[0]);
			}
		}catch (Exception ex){
			logger.warn("Error in parsing date {}",ex.getMessage());
		}
		return 0;
	}

	private Integer getAdvancePurchaseParam(String checkin) {
		Integer apWindow=null;
		try {
			if (StringUtils.isNotBlank(checkin)) {
				String checkinDate = checkin.split(" ")[0];
				apWindow = dateUtil.getDaysDiff(LocalDate.now(), LocalDate.parse(checkinDate));
			}
		} catch (Exception e) {
			logger.error("Exception while getting apwindow for pokus request", e);
		}
		return apWindow;
	}

	private String getDeviceinfo(String deviceId, String mcId, String visitorId) {

		String deviceInfo = "";
		if (StringUtils.isNotEmpty(deviceId)) {
			deviceInfo = deviceId;
		} else if (StringUtils.isNotEmpty(visitorId)) {
			deviceInfo = visitorId;
		} else {
			deviceInfo = mcId;
		}
		return deviceInfo;
	}

	private String getDeviceOS(String bookingDevice) {
		String deviceOS = null;
		if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)) {
			deviceOS = Constants.DEVICE_OS_ANDROID;
		} else if (Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
			deviceOS = Constants.DEVICE_OS_IOS;
		} else if (Constants.DEVICE_MSITE.equalsIgnoreCase(bookingDevice)) {
			deviceOS = Constants.DEVICE_OS_PWA;
		} else {
			deviceOS = Constants.DEVICE_OS_DESKTOP;
		}
		return deviceOS;
	}

	private String getCityCodeForLocus(String cityCode, String locationId) {
		if (StringUtils.isNotBlank(locationId))
			return locationId;
		return cityCode;
	}

	public void getUserDetailsAsync(Map<String, String> httpHeaderMap,
									CountDownLatch countDownLatchAsync, List<UserServiceResponse> userServiceResponseList, CommonModifierRequest commonModifierRequest, Map<String, String> contextMap) {
		try {
//			userServiceThreadPool.submit(() -> { // moving to sync call as pokus needs identifier for mypartner -HTL-36268
				try {
					if(MapUtils.isNotEmpty(contextMap)) {
						MDC.setContextMap(contextMap);
					}
					UserServiceResponse userServiceResponse = null;

					if (Constants.PAGE_CONTEXT_REVIEW.equalsIgnoreCase(commonModifierRequest.getPageContext())){

						if (!StringUtils.isBlank(commonModifierRequest.getMmtAuth())) {
							userServiceResponse = getUserDetails(commonModifierRequest.getMmtAuth(),null,null,
									commonModifierRequest.getChannel(), commonModifierRequest.getCorrelationKey(), commonModifierRequest.getIdContext(), commonModifierRequest.getRegion(), commonModifierRequest.getUuid(), httpHeaderMap);

						} else if (StringUtils.isNotBlank(commonModifierRequest.getCorpAuthCode())){
							String corpAuthCodeDecoded = null;
							try {
								if (commonModifierRequest.getCorpAuthCode().contains("%"))
									corpAuthCodeDecoded= (URLDecoder.decode(commonModifierRequest.getCorpAuthCode(), "UTF-8"));
							}catch(Exception ex){
								logger.error("error in decoding auth code corp :{}" , commonModifierRequest.getCorpAuthCode());
							}
							WorkflowInfoResponse workflowInfo = corporateExecutor.getWorkflowInfoByAuthCode(corpAuthCodeDecoded,null,commonModifierRequest.getCorrelationKey());
							logger.info("workflow response :{}" , workflowInfo);
							if(workflowInfo != null && workflowInfo.getApproverInfo() != null
									&& StringUtils.isNotBlank(workflowInfo.getApproverInfo().getEmailId())) {
								String emailId  = workflowInfo.getApproverInfo().getEmailId();
								logger.debug("workflow response email :{}" , emailId);
								userServiceResponse = getUserDetails(null, emailId, null, null,
										commonModifierRequest.getCorrelationKey(), Constants.CORP_ID_CONTEXT, commonModifierRequest.getRegion(), commonModifierRequest.getUuid(), httpHeaderMap);
							}
						}
					}else {
						userServiceResponse = getUserDetails(commonModifierRequest.getMmtAuth(),null,null,commonModifierRequest.getChannel(), commonModifierRequest.getCorrelationKey(), commonModifierRequest.getIdContext(),commonModifierRequest.getRegion(), commonModifierRequest.getUuid(), httpHeaderMap);
					}

					if (userServiceResponse != null)
						userServiceResponseList.add(userServiceResponse);
				}catch (Exception e){
					if (e instanceof ClientGatewayException)
						metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getUserDetailsAsync");
					else
						metricErrorLogger.logGeneralException(e, "getUserDetailsAsync", DependencyLayer.CLIENTGATEWAY, 
		    					ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
					
				}finally {
				//	MDC.clear(); //commenting for sync call
                    countDownLatchAsync.countDown();
                }
//			});
		}catch(Exception e) {
			metricErrorLogger.logGeneralException(e, "getUserDetailsAsync", DependencyLayer.CLIENTGATEWAY, 
    					ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
			countDownLatchAsync.countDown();
		}
	}

	public UserServiceResponse getUserDetails(String mmtAuth,String emailId,String mobile,String channel, String correlationKey, String idContext, String siteDomain, String uuid, Map<String, String> httpHeaderMap) throws ClientGatewayException {

		UserServiceResponse userServiceResponse = userServiceExecutor.getUserServiceResponse(mmtAuth, emailId , mobile, channel,  correlationKey, idContext, siteDomain, uuid, httpHeaderMap);

		return userServiceResponse;
    }

	public String getMMTAuth(Map<String, String> httpHeaderMap, String bookingDevice) {
		String mmtAuth = null;
		if ("ANDROID".equalsIgnoreCase(bookingDevice)) {
			String androidAuth = httpHeaderMap.get("backup_auth");
			if (!StringUtils.isEmpty(androidAuth) && androidAuth.indexOf("mmtAuth") > -1)
				mmtAuth = androidAuth.substring(androidAuth.indexOf("mmtAuth") + 9, androidAuth.length() - 1);
		}
		if(StringUtils.isBlank(mmtAuth)) {
			mmtAuth= httpHeaderMap.get("mmt-auth");
		}

		return mmtAuth;
	}

	public void updateCurrencyAndSource(SearchCriteria searchCriteria, RequestDetails requestDetails, Map<String, String> httpHeaderMap, DeviceDetails deviceDetails) {
		if (searchCriteria != null && StringUtils.isNotEmpty(searchCriteria.getCurrency())) {
			searchCriteria.setCurrency(searchCriteria.getCurrency().toUpperCase());
		}

        Map<String, String> countryStateAndCity = getCountryAndCityCodeFromHeader(httpHeaderMap);
        String countryCode = countryStateAndCity.get(Constants.HEADER_COUNTRY_CODE);
        String stateCode = countryStateAndCity.get(Constants.REGION_CODE);
        String cityCode = countryStateAndCity.get(Constants.HEADER_CITY);

        if (StringUtils.isEmpty(requestDetails.getSrCon()))
            requestDetails.setSrCon(countryCode);
        if (StringUtils.isEmpty(requestDetails.getSrCty()))
            requestDetails.setSrCty(cityCode);
        if (StringUtils.isEmpty(requestDetails.getSrcState()))
            requestDetails.setSrcState(stateCode);

        if (StringUtils.isNotBlank(countryCode)) {
            SourceRegionSpecificDataConfig sourceRegionSpecificDataConfig = new SourceRegionSpecificDataConfig();
            if (mobConfigHelper.getSourceRegionSpecificDataMapping().containsKey(countryCode.toUpperCase())) {
                sourceRegionSpecificDataConfig = mobConfigHelper.getSourceRegionSpecificDataMapping().get(countryCode.toUpperCase());
            } else {
                sourceRegionSpecificDataConfig = mobConfigHelper.getSourceRegionSpecificDataMapping().get(Constants.DEFAULT_DOMAIN);
            }

            if (searchCriteria!=null && StringUtils.isEmpty(searchCriteria.getCurrency()))
                searchCriteria.setCurrency(sourceRegionSpecificDataConfig.getCurrency().toUpperCase());
        } else {
            if (searchCriteria!=null && StringUtils.isEmpty(searchCriteria.getCurrency()))
                searchCriteria.setCurrency(Constants.DEFAULT_CUR_INR);
        }
		if (searchCriteria != null && httpHeaderMap.containsKey(Constants.USER_CURRENCY) && httpHeaderMap.containsKey(Constants.CURRENCY)) {
			searchCriteria.setMultiCurrencyInfo(getMultiCurrencyInfo(httpHeaderMap.get(Constants.USER_CURRENCY), httpHeaderMap.get(Constants.CURRENCY)));
		}
		if (searchCriteria != null && !httpHeaderMap.containsKey(Constants.USER_CURRENCY) && httpHeaderMap.containsKey(Constants.CURRENCY)) {
			searchCriteria.setMultiCurrencyInfo(getMultiCurrencyInfo(searchCriteria.getCurrency(), httpHeaderMap.get(Constants.CURRENCY)));
		}
		//Setting Header's user-currency to request-body currency
		if (searchCriteria != null && StringUtils.isNotEmpty(httpHeaderMap.get(Constants.USER_CURRENCY))) {
			searchCriteria.setCurrency(httpHeaderMap.get(Constants.USER_CURRENCY).toUpperCase());
		}
		//Setting default currency basis region for specific App versions
		if (searchCriteria != null && utility.checkAppVersionForCurrency(deviceDetails.getBookingDevice(), deviceDetails.getAppVersion())) {
			if (StringUtils.isEmpty(httpHeaderMap.get(Constants.USER_CURRENCY)) && httpHeaderMap.containsKey(Constants.CURRENCY)) {
				searchCriteria.setCurrency(httpHeaderMap.get(Constants.CURRENCY).toUpperCase());
				searchCriteria.setMultiCurrencyInfo(getMultiCurrencyInfo(httpHeaderMap.get(Constants.CURRENCY), httpHeaderMap.get(Constants.CURRENCY)));
			}
		}
    }

	private MultiCurrencyInfo getMultiCurrencyInfo(String userCurrency, String regionCurrency) {
		MultiCurrencyInfo multiCurrencyInfo = new MultiCurrencyInfo();
		multiCurrencyInfo.setRegionCurrency(regionCurrency.toUpperCase());
		multiCurrencyInfo.setUserCurrency(userCurrency.toUpperCase());
		return multiCurrencyInfo;
	}

    // georegion=104,country_code=IN,region_code=HR,city=GURGAON,lat=28.47,long=77.03,timezone=GMT+5.50,continent=AS,throughput=vhigh,bw=5000,asnum=9498,location_id=0
    public Map<String, String> getCountryAndCityCodeFromHeader(Map<String, String> httpHeaderMap) {
        Map<String, String> cityStateAndRegionMap = new HashMap<>();
		if(MapUtils.isEmpty(httpHeaderMap)){
			return cityStateAndRegionMap;
		}
        String headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI);
        if (StringUtils.isEmpty(headerValue))
            headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI.toLowerCase());
        String countryCode = null;
        String cityCode = null;
        String stateCode = null;
        if (!StringUtils.isEmpty(headerValue)) {
            logger.warn("akamai header value : {}", headerValue);
            String[] values = headerValue.split(Constants.COMMA);
            for (String value : values) {
                if (value.contains(Constants.HEADER_COUNTRY_CODE)) {
                    countryCode = value.split(Constants.EQUI)[1];
                }
                if (value.contains(Constants.HEADER_CITY)) {
                    cityCode = value.split(Constants.EQUI)[1];
                }
                if (value.contains(Constants.REGION_CODE)) {
                    stateCode = value.split(Constants.EQUI)[1];
                }
            }
        }
        cityStateAndRegionMap.put(Constants.HEADER_CITY, cityCode);
        cityStateAndRegionMap.put(Constants.HEADER_COUNTRY_CODE, countryCode);
        cityStateAndRegionMap.put(Constants.REGION_CODE, stateCode);
        return cityStateAndRegionMap;
    }

	public void updateLatLngFromHeader(SearchCriteria searchCriteria, Map<String, String> httpHeaderMap) {
		Pair<Double, Double> countryAndCity = getLatLngFromHeader(httpHeaderMap);
		Double lat = countryAndCity.getLeft();
		Double lng = countryAndCity.getRight();
		if (null != lat && null != lng) {
			searchCriteria.setLat(lat);
			searchCriteria.setLng(lng);
		}
	}

	public Pair<Double, Double> getLatLngFromHeader(Map<String, String> httpHeaderMap) {
		String headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI);
		if (StringUtils.isEmpty(headerValue))
			headerValue = httpHeaderMap.get(Constants.HEADER_AKAMAI.toLowerCase());
		Double lat = null;
		Double lng = null;
		try {
			if (!StringUtils.isEmpty(headerValue)) {
				String[] values = headerValue.split(Constants.COMMA);
				for (String value : values) {
					if (value.contains(Constants.HEADER_LAT)) {
						lat = Double.valueOf(value.split(Constants.EQUI)[1]);
					}

					if (value.contains(Constants.HEADER_LONG)) {
						lng = Double.valueOf(value.split(Constants.EQUI)[1]);
					}
				}
			}
		} catch (Exception e) {
			logger.error("Error while fetching lat lng from akamai header");
		}

		return Pair.of(lat, lng);
	}


	public HydraResponse executeHydraService(String cityCode, String bookingDevice,String correlationKey, String trafficSource, int firstTimeUserState, String countryCode,  ExtendedUser extendedUser, Map<String, String> httpHeaderMap, String mcId) {
		try {
			if (Constants.COSMOS.equals(trafficSource) || Constants.CBINTERNAL.equals(trafficSource)) {
				return null;
			}
			Map<String, String> contextMap = MDC.getCopyOfContextMap();
			String entityName = null;
			if (MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(ENTITY_NAME)) {
				entityName = httpHeaderMap.get(Constants.ENTITY_NAME);
			}
			Set<String> matchedSegment = getHydraMatchedSegment(extendedUser, bookingDevice, correlationKey, mcId, entityName);
			boolean userFirstTimeState = getUserFirstTimeState(bookingDevice, firstTimeUserState, countryCode, extendedUser, mcId, contextMap, matchedSegment);
			HydraResponse hydraResponse = new HydraResponse();
			hydraResponse.setFlightBooker(false);
			hydraResponse.setFirstUserState(userFirstTimeState);
			hydraResponse.setHydraMatchedSegment(matchedSegment);
			return hydraResponse;
		} catch (Exception e) {
			if (e instanceof ClientGatewayException)
    			metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "executeHydraService");
    		else
    			metricErrorLogger.logGeneralException(e, "executeHydraService", DependencyLayer.CLIENTGATEWAY,
    					ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
		}
		return null;
	}


	private boolean getUserFirstTimeState(String bookingDevice, int firstTimeUserState,  String countryCode, ExtendedUser extendedUser, String mcId, Map<String, String> contextMap, Set<String> matchedSegment) {
		String hydraDeviceId = null; 
		if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)
				|| Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
			hydraDeviceId = mcId;
		}
		
		if (extendedUser != null && StringUtils.isBlank(extendedUser.getUuid()) && StringUtils.isBlank(hydraDeviceId)) {
			return false;
		}
		if (firstTimeUserState == 3) {
			String segmentToCheck = "r1063"; // Intl segment code for more than 1 booking
			if ("IN".equalsIgnoreCase(countryCode))
				segmentToCheck = "r1043";  // Dom segment code for more than 1 booking
			if (CollectionUtils.isNotEmpty(matchedSegment)) {
				for(String segmentId: matchedSegment) {
					if (segmentId.equalsIgnoreCase(segmentToCheck))
						return false;
				}
			}
			return true;
		}else if (firstTimeUserState == 1)
			return false;
		else return firstTimeUserState == 2;
	}

	private Set<String> getHydraMatchedSegment(ExtendedUser extendedUser, String bookingDevice, String correlationKey, String mcId, String entityName) {
		try {
			String hydraDeviceId = null; 
			if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)
					|| Constants.DEVICE_IOS.equalsIgnoreCase(bookingDevice)) {
				hydraDeviceId = mcId;
			}
			HydraUserSegmentRequest hydraUserSegmentRequest = buildUserSegmentRequest(extendedUser, mcId, hydraDeviceId);
		
			if (hydraUserSegmentRequest == null)
				return null;

			HydraUserSegmentResponse response = null;
			boolean retryReq = true;
			int curRetryCount = 0;
			do {
				try {
					response = hydraExecutor.getHydraMatchedSegment(hydraUserSegmentRequest, correlationKey, entityName);
					logger.debug("Response received in {} retry",curRetryCount);
					retryReq = false;
				}
				catch(RestConnectorException exception){
					logger.debug("Error in connecting to Hydra service in {} retry : {}",curRetryCount,exception);
					curRetryCount++;
					retryReq = true;
				}
			} while (retryReq && curRetryCount < commonConfigHelper.getHydraRetryCount());

			Set<String> hydraMatchingSegmentCodeSet = null;
			if(response == null){
				logger.error("Failed to get response from hydra service for ck: {}", correlationKey);
			}
			else {
				if (response.getCode() == 200 && response.getData() != null) {
					Set<HydraSegment> hydraRespSegments = response.getData().getSegments();
					if (!CollectionUtils.isEmpty(hydraRespSegments)) {
						hydraMatchingSegmentCodeSet = new HashSet<>();
						for (HydraSegment hydraSegment : hydraRespSegments) {
							hydraMatchingSegmentCodeSet.add(hydraSegment.getSegmentCode());
						}
					}
				}
			}
			return hydraMatchingSegmentCodeSet;
		} catch (Exception e) {
			if (e instanceof ClientGatewayException)
    			metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "getHydraMatchedSegment");
    		else
    			metricErrorLogger.logGeneralException(e, "getHydraMatchedSegment", DependencyLayer.CLIENTGATEWAY, 
    					ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
		}
		return null;
	}

	private HydraUserSegmentRequest buildUserSegmentRequest(ExtendedUser extendedUser, String visitorId, String deviceId) {
		if ((extendedUser == null || StringUtils.isEmpty(extendedUser.getUuid())) && StringUtils.isEmpty(visitorId))
			return null;
		HydraUserSegmentRequest hydraUserSegmentRequest = new HydraUserSegmentRequest();
		hydraUserSegmentRequest.setSegments(commonConfigHelper.getHydraAllSegmentsList());
		hydraUserSegmentRequest.setService("hydra");
		HydraUserIdObject hydraUserIdObject = new HydraUserIdObject();
		hydraUserIdObject.setDeviceId(deviceId);

		if (extendedUser != null) {
			hydraUserIdObject.setProfileType(extendedUser.getProfileType());
			hydraUserIdObject.setUuid(extendedUser.getUuid());
		}
		hydraUserIdObject.setVisitorId(visitorId);
		hydraUserSegmentRequest.setUserId(hydraUserIdObject);
		return hydraUserSegmentRequest;
	}

	private boolean checkValidUserId(ExtendedUser extendedUser, String deviceId) {
		if (extendedUser != null && !StringUtils.isEmpty(extendedUser.getUuid()) && !StringUtils.isEmpty(extendedUser.getProfileType()))
			return true;
		return !StringUtils.isEmpty(deviceId) && !Constants.HYDRA_BLOCKED_DEVICEID_LIST.contains(deviceId);
	}

	private HydraRequest buildUserStateFeatureRequest(BaseSearchRequest baseSearchRequest, SearchCriteria searchCriteria, ExtendedUser extendedUser, String hydraDeviceId) {
		HydraRequest featuresRequest = new HydraRequest();
		List<HydraEntityWrapper> entities = new ArrayList<HydraEntityWrapper>();
		HydraEntityWrapper entityWrapr = new HydraEntityWrapper();
		List<String> features = new ArrayList<>();
		HydraEntity entity = new HydraEntity();
		String lob = null;
		if ("in".equalsIgnoreCase(searchCriteria.getCountryCode())) {
			lob = "dh";
		} else {
			lob = "ih";
		}
		if(extendedUser != null && !StringUtils.isEmpty(extendedUser.getUuid())){
			List<String> userLobs = new ArrayList<>();
			userLobs.add(getUserLobKey(extendedUser.getUuid(), extendedUser.getProfileType(), lob));
			entity.setUserLob(userLobs);
			features.add(Constants.USER_LOB_LAST_BOOKED_TS);

		} else {
			List<String> deviceLobs = new ArrayList<>();
			deviceLobs.add(getDeviceLobKey(hydraDeviceId, lob));
			entity.setDeviceLob(deviceLobs);
			features.add(Constants.DEVICE_LOB_LAST_BOOKED_TS);
		}
		entityWrapr.setFeatures(features);
		entityWrapr.setEntity(entity);
		entities.add(entityWrapr);
		featuresRequest.setEntities(entities);
		return featuresRequest;
	}

	private HydraRequest buildFlightBookerRequest( ExtendedUser extendedUser, String hydraDeviceId) {
		HydraRequest hydraRequest = new HydraRequest();
		List<HydraEntityWrapper> entities = new ArrayList<HydraEntityWrapper>();
		HydraEntityWrapper entityWrapr = new HydraEntityWrapper();
		List<String> features = new ArrayList<>();
		HydraEntity entity = new HydraEntity();
		if (extendedUser !=null && !StringUtils.isEmpty(extendedUser.getUuid())) {
			List<String> userLobs = new ArrayList<>();
			userLobs.add(getUserLobKey(extendedUser.getUuid(), extendedUser.getProfileType(), Constants.LOB_DOM_FLIGHTS));
			userLobs.add(getUserLobKey(extendedUser.getUuid(), extendedUser.getProfileType(), Constants.LOB_INTL_FLIGHTS));
			entity.setUserLob(userLobs);
			features.add(Constants.USER_LOB_LAST_BOOKED_SC);
		} else {
			List<String> deviceLobs = new ArrayList<>();
			deviceLobs.add(getDeviceLobKey(hydraDeviceId, Constants.LOB_DOM_FLIGHTS));
			deviceLobs.add(getDeviceLobKey(hydraDeviceId, Constants.LOB_INTL_FLIGHTS));
			entity.setDeviceLob(deviceLobs);
			features.add(Constants.DEVICE_LOB_LAST_BOOKED_SC);
		}
		entityWrapr.setFeatures(features);
		entityWrapr.setEntity(entity);
		entities.add(entityWrapr);
		hydraRequest.setEntities(entities);
		return hydraRequest;
	}

	private String getDeviceLobKey(String deviceId, String lob) {
		return deviceId + "/" + lob;
	}

	private String getUserLobKey(String uuid, String profileType, String lob) {
		return uuid + "/" + profileType + "/" + lob;
	}

	public String getMcId(Map<String, String> httpHeaderMap, String bookingDevice) {
		if (Constants.ANDROID.equalsIgnoreCase(bookingDevice)) {
			return httpHeaderMap.get(Constants.ANDROID_MCID);
		}
		return httpHeaderMap.get(Constants.COMMON_MCID);
	}


	public int getApplicationId(String pageContext) {

		StringBuilder sb = new StringBuilder();
		String key = sb.append(Constants.APPLICATION_ID).append(Constants.DOT)
				.append(pageContext.toUpperCase()).toString();
		if(env.getProperty(key)==null)
		{
			logger.warn("ApplicationId not found for key {},returning default 410",key);
			return 410; // TODO :- To be reverted
		}
		return Integer.parseInt(env.getProperty(key));

	}

	public void setDefaultSearchContext(ListingSearchRequestV2 searchHotelsRequest) {
		Map<String,Integer> defaultSearchContext =  commonConfigHelper.getDefaultSearchContext();
		logger.info("search context not present setting default values {}", gson.toJson(defaultSearchContext));
		if(MapUtils.isEmpty(defaultSearchContext))
			return;
		TreelsSearchCriteria searchCriteria = new TreelsSearchCriteria();
		if(defaultSearchContext.containsKey(checkInAddition)) {
			searchCriteria.setCheckIn(String.valueOf(LocalDate.now().plusDays(defaultSearchContext.get(checkInAddition))));
		}
		if(defaultSearchContext.containsKey(checkOutAddition)) {
			searchCriteria.setCheckOut(String.valueOf(LocalDate.now().plusDays(defaultSearchContext.get(checkOutAddition))));
		}
		searchCriteria.setRoomStayCandidates(buildDefaultRoomStayCandidates(defaultSearchContext));
		searchHotelsRequest.setSearchCriteria(searchCriteria);
	}

	private List<RoomStayCandidate>  buildDefaultRoomStayCandidates(Map<String,Integer> defaultSearchContext) {
		List<RoomStayCandidate> roomStayCandidateList = new ArrayList<>();
		RoomStayCandidate roomStayCandidate = new RoomStayCandidate();
		if(defaultSearchContext.containsKey(defaultRooms)) {
			roomStayCandidate.setRooms(defaultSearchContext.get(defaultRooms));
		}
		if(defaultSearchContext.containsKey(defaultGuest)) {
			roomStayCandidate.setAdultCount(defaultSearchContext.get(defaultGuest));
		}
		roomStayCandidateList.add(roomStayCandidate);
		return roomStayCandidateList;
	}


	public CommonModifierResponse processRequest(SearchCriteria searchCriteria, BaseSearchRequest baseSearchRequest, Map<String, String> httpHeaderMap) throws AuthenticationException {
		/* Temp check for OLD CB APIs */
		if (baseSearchRequest!=null && baseSearchRequest.getRequestDetails()!=null
				&& StringUtils.isBlank(baseSearchRequest.getRequestDetails().getSiteDomain())){
			baseSearchRequest.getRequestDetails().setSiteDomain(httpHeaderMap.get(Constants.REGION));
		}

		//Certain old apps send B2B as idcontext, in order to maintain compatibility, update B2B as CORP"
		if(baseSearchRequest!=null && baseSearchRequest.getRequestDetails()!=null &&
				Constants.B2B_ID_CONTEXT.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getIdContext())) {
			baseSearchRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
		}
		
		// Validate WishListedSearch and apply feature flags if necessary
		validateAndUpdateTheWishListedSearch(searchCriteria, baseSearchRequest);
		
		// Ensure expData is updated from expDataMap for WishListedSearch
		if (searchCriteria instanceof SearchHotelsCriteria && 
			((SearchHotelsCriteria) searchCriteria).isWishListedSearch() && 
			MapUtils.isNotEmpty(baseSearchRequest.getExpDataMap())) {
			try {
				baseSearchRequest.setExpData(objectMapperUtil.getJsonFromObject(baseSearchRequest.getExpDataMap(), DependencyLayer.CLIENTGATEWAY));
				logger.info("Updated expData for WishListedSearch: {}", baseSearchRequest.getExpData());
			} catch (Exception e) {
				logger.error("Error updating expData from expDataMap for WishListedSearch", e);
			}
		}
		
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		if(baseSearchRequest != null && baseSearchRequest.getRequestDetails() != null && baseSearchRequest.getRequestDetails().getTrafficSource() != null){
			commonModifierResponse.setOriginalTrafficSource(baseSearchRequest.getRequestDetails().getTrafficSource().getSource());
		}
		updateTrafficSource(baseSearchRequest.getRequestDetails(),baseSearchRequest.getDeviceDetails());

		if(baseSearchRequest != null && baseSearchRequest.getRequestDetails() != null && baseSearchRequest.getRequestDetails().getTrafficSource() != null){
			commonModifierResponse.setTrafficSource(baseSearchRequest.getRequestDetails().getTrafficSource().getSource());
		}
		updateCurrencyAndSource(searchCriteria, baseSearchRequest.getRequestDetails(), httpHeaderMap, baseSearchRequest.getDeviceDetails());
		updateUserGlobalInfo(searchCriteria, httpHeaderMap);

		/* Update lat lng from akamai header
		* For "nearby getaways" request - there may be no city/country in the request.
		* For such cases - pick lat/long from akamai header. */
		if (searchCriteria!=null && null == searchCriteria.getLat() && null == searchCriteria.getLng()) {
			updateLatLngFromHeader(searchCriteria, httpHeaderMap);
		}
		commonModifierResponse.setLanguage(httpHeaderMap.get(LANGUAGE));
		commonModifierResponse.setDeviceId(httpHeaderMap.get(DEVICE_ID));
		commonModifierResponse.setRegion(httpHeaderMap.get(REGION));
		commonModifierResponse.setUserCountry(httpHeaderMap.get(USER_COUNTRY));
		commonModifierResponse.setMmtAuth(getMMTAuth(httpHeaderMap, baseSearchRequest.getDeviceDetails().getBookingDevice()));
		commonModifierResponse.setMcId(getMcId(httpHeaderMap, baseSearchRequest.getDeviceDetails().getBookingDevice()));
		commonModifierResponse.setExtendedUser(executePokusAndUserDetails(searchCriteria, baseSearchRequest, baseSearchRequest.getCorrelationKey(),
				httpHeaderMap, commonModifierResponse.getMmtAuth(), commonModifierResponse.getMcId()));
		commonModifierResponse.setManthanExpDataMap(baseSearchRequest.getManthanExpDataMap());
		commonModifierResponse.setContentExpDataMap(baseSearchRequest.getContentExpDataMap());

		ExtendedUser extendedUser = commonModifierResponse.getExtendedUser();
		if (extendedUser == null && PAGE_CONTEXT_REVIEW.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getPageContext()) && FUNNEL_SOURCE_MYPARTNER.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.FUNNEL_SOURCE.getStringValue()))) {
			throw new AuthenticationException(DependencyLayer.USERSERVICE,ErrorType.CONNECTIVITY,
					AuthenticationErrors.CONNECTION_FAILURE.getErrorCode(), AuthenticationErrors.CONNECTION_FAILURE.getErrorMsg());
		}

		if (extendedUser != null && CollectionUtils.isNotEmpty(extendedUser.getLoginInfoList())) {
			for (UserLoginInfo loginInfo : extendedUser.getLoginInfoList()) {
				if (Constants.LOGIN_TYPE_MOBILE.equalsIgnoreCase(loginInfo.getLoginType())) {
					commonModifierResponse.setMobile(loginInfo.getLoginId());
				}
			}
		}
		
		/*
		 * HTL-41137 Setting user location details like city,state and country
		 * for personalised discounting if it is not null in the request.
		 * Otherwise,setting the value from Akamai Headers.
		 * 
		 */
		if(baseSearchRequest.getUserLocation()!=null) {
			commonModifierResponse.setUserLocation(baseSearchRequest.getUserLocation());
		} else {
			commonModifierResponse.setUserLocation(buildUserLocationFromHeader(httpHeaderMap));
		}

		if (commonModifierResponse.getExtendedUser() == null ||
				StringUtils.isBlank(commonModifierResponse.getExtendedUser().getUuid()) ) {
			if (Constants.CORP_ID_CONTEXT.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getIdContext()) &&
					!baseSearchRequest.getRequestDetails().isSeoCorp()) {
//				throw new AuthenticationException(DependencyLayer.USERSERVICE,ErrorType.AUTHENTICATION, AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());

			}else
				baseSearchRequest.getRequestDetails().setLoggedIn(false);
		}

    	addFiltersToRemove(baseSearchRequest, commonModifierResponse.getExtendedUser(), searchCriteria);
    	
    	if (baseSearchRequest.getCohertVar() != null && !baseSearchRequest.getRequestDetails().isLoggedIn()) {
    		baseSearchRequest.getRequestDetails().setLoggedIn(baseSearchRequest.getCohertVar().isLoggedIn());
		}

    	String trafficSource = baseSearchRequest.getRequestDetails().getTrafficSource() != null && StringUtils.isNotBlank(baseSearchRequest.getRequestDetails().getTrafficSource().getSource()) ? baseSearchRequest.getRequestDetails().getTrafficSource().getSource().toUpperCase():"";
    	String cityCode = (searchCriteria!=null && StringUtils.isNotBlank(searchCriteria.getLocationId()))?searchCriteria.getLocationId():"";
		if(commonModifierResponse.getExtendedUser() != null && commonModifierResponse.getExtendedUser().getUuid() != null && searchCriteria!=null) {
			commonModifierResponse.setHydraResponse(executeHydraService(cityCode, baseSearchRequest.getDeviceDetails().getBookingDevice(),
					baseSearchRequest.getCorrelationKey(), trafficSource, baseSearchRequest.getRequestDetails().getFirstTimeUserState(),
					searchCriteria.getCountryCode(), commonModifierResponse.getExtendedUser(), httpHeaderMap, commonModifierResponse.getMcId()));
		}
		// Overriding Pokus Experiment values based on consul config
		if (baseSearchRequest.getRequestDetails() != null && null != searchCriteria && StringUtils.isNotEmpty(searchCriteria.getCountryCode())) {
			String language = MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue());
			overridePokusExperiment(baseSearchRequest, language, searchCriteria.getCountryCode());
		}

		if (baseSearchRequest.getRequestDetails() != null && (PAGE_CONTEXT_REVIEW).equalsIgnoreCase(baseSearchRequest.getRequestDetails().getPageContext())) {
			// Add type check before casting to prevent ClassCastException
			if (searchCriteria instanceof AvailPriceCriteria) {
				setPricerV2Flag((AvailPriceCriteria) searchCriteria, baseSearchRequest);
			} else {
				logger.debug("Skipping setPricerV2Flag as searchCriteria is not an instance of AvailPriceCriteria. Actual type: {}", 
					searchCriteria != null ? searchCriteria.getClass().getSimpleName() : "null");
			}
		}
		//
		convertExpDataToString(baseSearchRequest);
    	commonModifierResponse.setFilterRules(commonConfigHelper.getFilterRuleObject());

    	String profileType = commonModifierResponse.getExtendedUser() !=null ? commonModifierResponse.getExtendedUser().getProfileType() : null;
    	commonModifierResponse.setCdfContextId(getCdfContextId(baseSearchRequest.getDeviceDetails().getBookingDevice(), baseSearchRequest.getRequestDetails().getIdContext(), profileType));

    	commonModifierResponse.setCategoryRequired("MOBILE".equalsIgnoreCase(baseSearchRequest.getDeviceDetails().getDeviceType()));
	    	commonModifierResponse.setNumberOfCoupons(getNumberOfCoupons(baseSearchRequest));

	    if (null != baseSearchRequest.getRequestDetails().getTrafficSource()) {
			commonModifierResponse.setDomain(commonConfigHelper.getCdfDomainMapping().get(baseSearchRequest.getRequestDetails().getTrafficSource().getSource())==null ?
					commonConfigHelper.getCdfDomainMapping().get("DEFAULT") : commonConfigHelper.getCdfDomainMapping().get(baseSearchRequest.getRequestDetails().getTrafficSource().getSource()));
		} else {
			commonModifierResponse.setDomain("B2C");
		}

    	if (searchCriteria!=null && ((StringUtils.isNotBlank(searchCriteria.getCountryCode()) && commonConfigHelper.getConList().contains(searchCriteria.getCountryCode().toUpperCase())) || (StringUtils.isNotBlank(searchCriteria.getCityCode()) && commonConfigHelper.getConList().contains(searchCriteria.getCityCode().toUpperCase())))) {
    		commonModifierResponse.setCityTaxExclusive(true);
		}
    	commonModifierResponse.setApplicationId(getApplicationId(baseSearchRequest.getRequestDetails().getPageContext()));
		commonModifierResponse.setExpDataMap(buildExpDataMap(baseSearchRequest.getExpData()));
		commonModifierResponse.setReviewPriceRequest(MapUtils.isNotEmpty(httpHeaderMap) && httpHeaderMap.containsKey(REVIEW_PRICE_REQUEST) && Constants.TRUE.equalsIgnoreCase(httpHeaderMap.get(REVIEW_PRICE_REQUEST)));
		commonModifierResponse.setHomestayV2Flow(MapUtils.isNotEmpty(httpHeaderMap) && TRUE.equalsIgnoreCase(httpHeaderMap.get(Constants.HOMESTAY_V2_FLOW)));
		return commonModifierResponse;
	}

	protected void updateTrafficSource(RequestDetails requestDetails, DeviceDetails deviceDetails) {
		if (requestDetails != null && requestDetails.getTrafficSource() != null && deviceDetails != null) {
			requestDetails.getTrafficSource().setSource(utility.buildSourceTraffic(requestDetails.getTrafficSource().getSource(), deviceDetails));
		}
	}

	private void updateUserGlobalInfo(SearchCriteria searchCriteria, Map<String, String> httpHeaderMap) {
		if (searchCriteria != null && httpHeaderMap.containsKey(Constants.USER_COUNTRY) && httpHeaderMap.containsKey(Constants.ENTITY_NAME)) {
			UserGlobalInfo userGlobalInfo = new UserGlobalInfo();
			userGlobalInfo.setUserCountry(httpHeaderMap.get(Constants.USER_COUNTRY));
			userGlobalInfo.setEntityName(httpHeaderMap.get(Constants.ENTITY_NAME));
			searchCriteria.setUserGlobalInfo(userGlobalInfo);
		}
	}

	public void overridePokusExperiment(BaseSearchRequest baseSearchRequest, String language, String countryCode) {
		Map<String, Set<String>> configMap;
		Map<String, Map<String, Set<String>>> overridePokusConfig = commonConfigHelper.getOverridePokusConfig();
		if (MapUtils.isNotEmpty(overridePokusConfig)) {
			for (String key : overridePokusConfig.keySet()) {
				configMap = overridePokusConfig.get(key);
				if (MapUtils.isNotEmpty(configMap)) {
					Set<String> funnelSources = configMap.get("funnel");
					Set<String> trafficSources = configMap.get("traffic");
					Set<String> idContext = configMap.get("idContext");
					Set<String> domain = configMap.get("domain");
					Set<String> langauges = configMap.get("language");
					Set<String> countryCodes = configMap.get("countryCode");
					List<String> expValue;
					if (CollectionUtils.isNotEmpty(configMap.get("expValue"))) {
						expValue = new ArrayList<>(configMap.get("expValue"));
					} else {
						expValue = new ArrayList<>();
						expValue.add("TRUE");
					}
					if (utility.checkCondition(baseSearchRequest.getRequestDetails().getIdContext(), idContext)
							&& CollectionUtils.isNotEmpty(expValue)
							&& utility.checkCondition(baseSearchRequest.getRequestDetails().getSiteDomain(), domain)
							&& utility.checkCondition(language, langauges)
							&& utility.checkCondition(countryCode, countryCodes)
							&& utility.checkCondition(baseSearchRequest.getRequestDetails().getFunnelSource(), funnelSources)
							&& (baseSearchRequest.getRequestDetails().getTrafficSource() == null ||
							utility.checkCondition(baseSearchRequest.getRequestDetails().getTrafficSource().getSource(), trafficSources))) {
						logger.warn("Overriding Pokus Experiment for {}: {}", key, expValue.get(0));
						if(baseSearchRequest.getExpDataMap() == null) {
							baseSearchRequest.setExpDataMap(new HashMap<>());
						}
						baseSearchRequest.getExpDataMap().put(key, expValue.get(0));
					}
				}
			}
		}
	}

	private void setPricerV2Flag(AvailPriceCriteria searchCriteria, BaseSearchRequest baseSearchRequest) {
		if (searchCriteria != null && CollectionUtils.isNotEmpty(searchCriteria.getRoomCriteria()) && searchCriteria.getRoomCriteria().get(0) != null) {
			String rpc = searchCriteria.getRoomCriteria().get(0).getRatePlanCode();
			if (MapUtils.isEmpty(baseSearchRequest.getExpDataMap())) {
				baseSearchRequest.setExpDataMap(new HashMap<>());
			}
			if (rpc.contains("MSE")) {
				baseSearchRequest.getExpDataMap().put("pricerV2","FALSE");
				baseSearchRequest.getExpDataMap().put("pricerIntlV2","FALSE");
			}
		}
	}

	private void convertExpDataToString(BaseSearchRequest baseSearchRequest) {
		try {
			if (MapUtils.isNotEmpty(baseSearchRequest.getExpDataMap())) {
				baseSearchRequest.setExpData(objectMapperUtil.getJsonFromObject(baseSearchRequest.getExpDataMap(), DependencyLayer.CLIENTGATEWAY));
			}
		} catch (Exception e) {
			if (e instanceof ClientGatewayException)
				metricErrorLogger.logClientGatewayException((ClientGatewayException) e, "convertExpDataToString");
			else
				metricErrorLogger.logGeneralException(e, "convertExpDataToString", DependencyLayer.CLIENTGATEWAY,
						ErrorType.UNEXPECTED, UnexpectedErrors.INTERNAL_ERROR.getErrorCode(), UnexpectedErrors.INTERNAL_ERROR.getErrorMsg());
		}
	}



	private LinkedHashMap<String, String> buildExpDataMap(String expData) {
		LinkedHashMap<String, String> experimentDataMap = null;
		try {
			if(StringUtils.isNotBlank(expData))
			{
				String experimentString = expData.replaceAll("^\"|\"$", "");
				Type type = new com.google.gson.reflect.TypeToken<LinkedHashMap<String, String>>(){}.getType();
				experimentDataMap = new Gson().fromJson(experimentString, type);
			}
		}
		catch (Exception ex)
		{
			logger.error("Error while Creating Experiment Data");
		}
		return experimentDataMap;
	}

	public CommonModifierResponse processRequestForBkgMod(Map<String, String> httpHeaderMap, RatePreviewRequest rateRequest, String pageContext) throws AuthenticationException {
		CommonModifierRequest commonModifierRequest = buildCommonRequest(rateRequest, httpHeaderMap, pageContext);

		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setMmtAuth(getMMTAuth(httpHeaderMap, commonModifierRequest.getBookingDevice()));
		commonModifierResponse.setMcId(getMcId(httpHeaderMap, commonModifierRequest.getBookingDevice()));
		commonModifierRequest.setMcid(commonModifierResponse.getMcId());
		commonModifierRequest.setVisitorId(commonModifierResponse.getMcId());
		commonModifierRequest.setMmtAuth(commonModifierResponse.getMmtAuth());
		BaseSearchRequest baseSearchRequest = new BaseSearchRequest();
		commonModifierResponse.setExtendedUser(executePokusAndUserDetails(commonModifierRequest, httpHeaderMap, "", baseSearchRequest));
		//[HTL-47579] Making pricerV2 true when modify booking new flow exp is true
		if (MapUtils.isNotEmpty(baseSearchRequest.getExpDataMap()) && baseSearchRequest.getExpDataMap().containsKey(Constants.PRICER_V2)
				&& Constants.TRUE.equalsIgnoreCase(baseSearchRequest.getExpDataMap().get(Constants.NBMF)) ) {
			baseSearchRequest.getExpDataMap().put(Constants.PRICER_V2, "true");
		}

		convertExpDataToString(baseSearchRequest);
		commonModifierResponse.setExpData(baseSearchRequest.getExpData());
		ExtendedUser extendedUser = commonModifierResponse.getExtendedUser();

		if (extendedUser != null && CollectionUtils.isNotEmpty(extendedUser.getLoginInfoList())) {
			for (UserLoginInfo loginInfo : extendedUser.getLoginInfoList()) {
				if (Constants.LOGIN_TYPE_MOBILE.equalsIgnoreCase(loginInfo.getLoginType())) {
					commonModifierResponse.setMobile(loginInfo.getLoginId());
				}
			}
		}

		if (commonModifierResponse.getExtendedUser() == null ||
				StringUtils.isBlank(commonModifierResponse.getExtendedUser().getUuid()) ) {
				throw new AuthenticationException(DependencyLayer.USERSERVICE,ErrorType.AUTHENTICATION,
						AuthenticationErrors.UUID_NOT_FOUND.getErrorCode(), AuthenticationErrors.UUID_NOT_FOUND.getErrorMsg());
		}

		String profileType = commonModifierResponse.getExtendedUser() !=null ? commonModifierResponse.getExtendedUser().getProfileType() : null;
		String uuid = commonModifierResponse.getExtendedUser() !=null ? commonModifierResponse.getExtendedUser().getUuid() : null;
		commonModifierResponse.setHydraResponse(executeHydraService(commonModifierRequest.getCityCode(),
				commonModifierRequest.getBookingDevice(), commonModifierRequest.getCorrelationKey(), "",
				1, rateRequest.getCountryCode(), commonModifierResponse.getExtendedUser(), httpHeaderMap,
				commonModifierResponse.getMcId()));

		String idContext = "B2C";
		if(StringUtils.isNotBlank(profileType) && Constants.PROFILE_CORPORATE.equalsIgnoreCase(profileType))
			idContext = "CORP";
		commonModifierRequest.setIdContext(idContext);
		commonModifierResponse.setCdfContextId(getCdfContextId(commonModifierRequest.getBookingDevice(),idContext, profileType));

		commonModifierResponse.setNumberOfCoupons(1);
		commonModifierResponse.setDomain("B2C");

		if (commonConfigHelper.getConList().contains(rateRequest.getCountryCode()) || commonConfigHelper.getConList().contains(commonModifierRequest.getCityCode())) {
			commonModifierResponse.setCityTaxExclusive(true);
		}
		commonModifierResponse.setApplicationId(getApplicationId(pageContext));

		return commonModifierResponse;
	}

	public String getAuthToken(Map<String, String> httpHeaderMap) {
		if (MapUtils.isEmpty(httpHeaderMap)) {
			return null;
		}
		String authToken = null;
		try {
			if (httpHeaderMap.containsKey("backup_auth")) {
				String androidAuth = httpHeaderMap.get("backup_auth");
				if (!StringUtils.isEmpty(androidAuth) && androidAuth.indexOf("mmtAuth") > -1) {
					authToken = androidAuth.substring(androidAuth.indexOf("mmtAuth") + 9, androidAuth.length() - 1);
				}

			} else {
				authToken = httpHeaderMap.get("mmt-auth");
			}

		} catch (Exception e) {
			logger.error("Error Occured in getAuthTockenHeader=", e);
		}
		if(authToken !=null && StringUtils.isEmpty(httpHeaderMap.get("mmt-auth"))) {
			httpHeaderMap.put("mmt-auth", authToken);
		}
		return authToken;
	}

	public String sanitizeInput(String data){
		if (StringUtils.isNotBlank(data))
			data = data.replaceAll(Constants.XSS_DISALLOWED_CHARACTERS_IN_URL, "");
		return  data;
	}


	private int getNumberOfCoupons (BaseSearchRequest baseSearchRequest){
		int noOfCupon =0;
		FeatureFlags featureFlags = baseSearchRequest.getFeatureFlags();
		if(featureFlags != null
				&& null !=baseSearchRequest.getRequestDetails()
				&& StringUtils.isNotBlank(baseSearchRequest.getRequestDetails().getPageContext())) {
			switch (baseSearchRequest.getRequestDetails().getPageContext().toUpperCase()) {
				case "LISTING":
					noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
							featureFlags.getNoOfCoupons() : 1;
					break;
				case "DETAIL":
					noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
							featureFlags.getNoOfCoupons() : 2;
					break;
				case "REVIEW":
					noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
							featureFlags.getNoOfCoupons() : 3;
					break;
				case "ALTACCOLANDING":
					noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
							featureFlags.getNoOfCoupons() : 1;
					break;
				case "PAGE_CONTEXT_HOMEPAGE":
					noOfCupon = (featureFlags != null && featureFlags.getNoOfCoupons() > 0) ?
							featureFlags.getNoOfCoupons() : 1;
					break;
				default:
					noOfCupon = 0;
					break;
			}
		}
		return noOfCupon;
	}

	private String getCdfContextId (String bookingDevice, String idContext, String profileType){
		String cdfContextId = null;
		if ("BUSINESS".equalsIgnoreCase(profileType)) {
			cdfContextId = "CORP";
			return cdfContextId;
		}
		if(Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice)) {

			return StringUtils.isNotBlank(idContext) ? idContext : Constants.B2C;
		}
		cdfContextId = StringUtils.isNotBlank(profileType) ? "MOB_LI" : Constants.MOB_ID_CONTEXT;
		return cdfContextId;
	}

	private void addFiltersToRemove (BaseSearchRequest baseSearchRequest, ExtendedUser extendedUser, SearchCriteria
			searchCriteria){
		baseSearchRequest.setFiltersToRemove(new ArrayList<>());
		List<FilterGroup> filterGroupsToRemove = new ArrayList<>();

		Map<String, List<FilterGroup>> filtersToRemoveByDomain = new HashMap<>();
		filtersToRemoveByDomain.put("US", Arrays.asList(FilterGroup.BLACKDEALS, FilterGroup.EMI, FilterGroup.GLAZE_FILTERS));
		filtersToRemoveByDomain.put("AE", Arrays.asList(FilterGroup.EMI, FilterGroup.GLAZE_FILTERS));
		String siteDomain = baseSearchRequest.getRequestDetails().getSiteDomain();
		if (filtersToRemoveByDomain.containsKey(siteDomain)) {
			filterGroupsToRemove.addAll(filtersToRemoveByDomain.get(siteDomain));
		}

		if (blockPAHExperimentOn(baseSearchRequest.getExpDataMap())){
			filterGroupsToRemove.add(FilterGroup.PAY_AT_HOTEL_AVAIL);
			filterGroupsToRemove.add(FilterGroup.PAY_AT_HOTEL);
		}
		String profileType = (extendedUser != null && extendedUser.getProfileType() != null) ? extendedUser.getProfileType() : null;
		String subProfileType = extendedUser != null ? extendedUser.getAffiliateId() : null;
		boolean isMyPartnerRequest =  Utility.isMyPartnerRequest(profileType, subProfileType);
		if ("BUSINESS".equalsIgnoreCase(profileType)) {
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.MMT_Assured));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.MMT_Assured));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.COUPLE_FRIENDLY));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.COUPLE_FRIENDLY));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.STAYCATION));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.STAYCATION));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.GREATVALUE));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.GREATVALUE));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.STAR_RATING, Constants.UNRATED_SR));
		} else if(!isMyPartnerRequest) {
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.MyBiz_Assured));
			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.MyBiz_Assured));
		}

		if (Constants.FUNNEL_SOURCE_HOMESTAY.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getFunnelSource())
			|| Constants.FUNNEL_SOURCE_HOMESTAY_NEW.equalsIgnoreCase(baseSearchRequest.getRequestDetails().getFunnelSource())) {
			if (CollectionUtils.isEmpty(baseSearchRequest.getFilterCriteria())) {
				baseSearchRequest.setFilterCriteria(new ArrayList<>());
			}
//			boolean propCategoryFilterApplied = false;
//			for (Filter filter : baseSearchRequest.getFilterCriteria()) {
//				if (FilterGroup.PROPERTY_CATEGORY.equals(filter.getFilterGroup())) {
//					propCategoryFilterApplied = true;
//					break;
//				}
//			}
//			if (!propCategoryFilterApplied) {
//				Filter homeStayfilter = new Filter(FilterGroup.PROPERTY_CATEGORY, "Alt_Acco_properties");
//				baseSearchRequest.getFilterCriteria().add(homeStayfilter);
//			}
			// Removing the above check so to apply alt_acco_properties filter even if some other property_category filter is applied
//			 Removing this filter now as Singularity will Take care of rendering PropertyType for homestay funnel;
//			Filter homeStayfilter = new Filter(FilterGroup.ALT_ACCO_PROPERTY, Constants.ALTACCO);
//			baseSearchRequest.getFilterCriteria().add(0, homeStayfilter);
//			baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.ALT_ACCO_PROPERTY, Constants.ALTACCO));
			if ("IN".equalsIgnoreCase(searchCriteria.getCountryCode())) {
				filterGroupsToRemove.add(FilterGroup.STAR_RATING);
				/* Preventing explicit check for not allowing properties in HOMESTAY Funnel.
				List<Filter> propTypeFiltersForDomHomestay = getPropTypeFiltersForHomestay();
				baseSearchRequest.getFiltersToRemove().addAll(propTypeFiltersForDomHomestay);*/
			}
		}
		baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.HOTEL_CATEGORY, Constants.KID_FRIENDLY));
		baseSearchRequest.getFiltersToRemove().add(new Filter(FilterGroup.MMT_OFFERING, Constants.KID_FRIENDLY));
		filterGroupsToRemove.add(FilterGroup.VIBE);
		baseSearchRequest.setFilterGroupsToRemove(filterGroupsToRemove);
	}

	public boolean blockPAHExperimentOn(Map<String, String> expData) {
		if (org.apache.commons.collections.MapUtils.isNotEmpty(expData)) {
			return "TRUE".equalsIgnoreCase(expData.get("blockPAH"));
		}
		return false;
	}
	
	public String getInboundCurrencyCode(String srcCountry, String destCountry, String deviceType) {
		if (isApplicableForInboundCheck(srcCountry, destCountry, deviceType)) {
			if (commonConfigHelper.getCurrCityMap().containsKey(srcCountry.trim())) {
				return commonConfigHelper.getCurrCityMap().get(srcCountry);
			} else {
				return Constants.DEFAULT_CUR_USD;
			}
		} else {
			return Constants.DEFAULT_CUR_INR;
		}
	}
	
	private boolean isApplicableForInboundCheck(String srcCountry, String destCountry, String bookingDevice) {
		String srcCountryType = getCountryType(srcCountry);
		String desCountryType = getCountryType(destCountry);
		if (StringUtils.isEmpty(srcCountry) || StringUtils.isEmpty(destCountry)) {
			return false;
		} else if (!commonConfigHelper.getEbableInboundExpDeviceList().contains(bookingDevice)) {
			return false;
		} else
			return !commonConfigHelper.isDisableSrcDesIntlExp() || !"INTL".equalsIgnoreCase(srcCountryType) || !"INTL".equalsIgnoreCase(desCountryType);
	}
	
	private String getCountryType(String countryNameOrCode) {
		String countryType = null;
		if (countryNameOrCode != null) {
			if ("IN".contentEquals(countryNameOrCode) || "India".equalsIgnoreCase(countryNameOrCode)) {
				countryType = "IN";
			} else {
				countryType = "INTL";
			}
		}
		return countryType;
	}
	
    public String updateIdContext(String idContext, String client) {
    	if(Constants.B2C.equalsIgnoreCase(idContext) && 
        		(Constants.ANDROID.equalsIgnoreCase(client) ||
            		Constants.DEVICE_IOS.equalsIgnoreCase(client)||
            			Constants.DEVICE_MSITE.equalsIgnoreCase(client) ||
            				Constants.DEVICE_OS_PWA.equalsIgnoreCase(client))) {
    		return Constants.MOB_ID_CONTEXT;
    	}
    	return idContext;
    }
	
	/**
	 * It would return true if the input string is json
	 * 
	 * @param inputString
	 * @return
	 */
	public boolean isJsonString(String inputString) {
		try {
			if (!StringUtils.isEmpty(inputString)) {
				JSONParser parser = new JSONParser();
				parser.parse(inputString);
				return true;
			} else {
				return false;

			}
		} catch (Exception e) {
			return false;
		}
	}

	public void updateReviewsRequest(Map<String, String> httpHeaderMap, FlyFishReviewsRequest flyFishReviewsRequest) {
		String mmtAuth = getAuthToken(httpHeaderMap);
		if(CollectionUtils.isEmpty(flyFishReviewsRequest.getUserInfo())) {
			flyFishReviewsRequest.setUserInfo(new ArrayList<>());
			flyFishReviewsRequest.getUserInfo().add(new UserInfoDTO());
		}
		flyFishReviewsRequest.getUserInfo().get(0).setMmtAuth(mmtAuth);
	}

	public CommonModifierResponse processRequest(WishListedHotelsDetailRequest wishListedHotelsDetailRequest, Map<String, String> httpHeaderMap) throws AuthenticationException {
		CommonModifierResponse commonModifierResponse = new CommonModifierResponse();
		commonModifierResponse.setMmtAuth(getMMTAuth(httpHeaderMap, wishListedHotelsDetailRequest.getDeviceDetails().getBookingDevice()));
		commonModifierResponse.setMcId(getMcId(httpHeaderMap, wishListedHotelsDetailRequest.getDeviceDetails().getBookingDevice()));
		SearchCriteria searchCriteria = new SearchCriteria();
		searchCriteria.setCityCode(wishListedHotelsDetailRequest.getSearchCriteria().getCityCode());
		searchCriteria.setLocationId(wishListedHotelsDetailRequest.getSearchCriteria().getLocationId());
		searchCriteria.setLocationType(wishListedHotelsDetailRequest.getSearchCriteria().getLocationType());
		commonModifierResponse.setExtendedUser(executePokusAndUserDetails(searchCriteria, wishListedHotelsDetailRequest, wishListedHotelsDetailRequest.getCorrelationKey(),
				httpHeaderMap, commonModifierResponse.getMmtAuth(), commonModifierResponse.getMcId()));
		return commonModifierResponse;
	}

	public String getFunnelSource(String funnelSource,List<UserServiceResponse> userServiceResponseList) {
		if (CollectionUtils.isNotEmpty(userServiceResponseList)) {
			UserServiceResponse userServiceResponse = userServiceResponseList.get(0);
			if(userServiceResponse!=null && userServiceResponse.getResult()!= null){
				if(userServiceResponse.getResult().getExtendedUser()!=null && StringUtils.isNotEmpty(userServiceResponse.getResult().getExtendedUser().getProfileType()) ){
					if(Constants.PROFILE_TYPE_CTA.equalsIgnoreCase(userServiceResponse.getResult().getExtendedUser().getProfileType()) && Constants.MYPARTNER.equalsIgnoreCase(userServiceResponse.getResult().getExtendedUser().getAffiliateId())){
						return Constants.MYPARTNER;
					}
				}
			}
		}
		return funnelSource;
	}

	public boolean checkValidHotel(ListingSearchRequest searchHotelsRequest, HotelDetails hotelEntity) {
		if (searchHotelsRequest != null && searchHotelsRequest.getMatchMakerDetails() != null) {
			if (CollectionUtils.isEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels())) {
				return true;
			} else {
				String hotelId = null;
				List<InputHotel> hotels = searchHotelsRequest.getMatchMakerDetails().getHotels();
				for (InputHotel inputHotel : hotels) {
					hotelId = inputHotel.getHotelId();
				}
				return !(StringUtils.isNotEmpty(hotelEntity.getId()) && hotelEntity.getId().equalsIgnoreCase(hotelId));
			}
		}
		return false;
	}

	public boolean checkValidHotel(ListingSearchRequest searchHotelsRequest, SearchWrapperHotelEntity hotelEntity){
		if(searchHotelsRequest != null && searchHotelsRequest.getMatchMakerDetails() != null) {
			if (CollectionUtils.isEmpty(searchHotelsRequest.getMatchMakerDetails().getHotels())) {
				return true;
			} else {
				String hotelId = null;
				List<InputHotel> hotels = searchHotelsRequest.getMatchMakerDetails().getHotels();
				for (InputHotel inputHotel : hotels) {
					hotelId = inputHotel.getHotelId();
				}
				return !(StringUtils.isNotEmpty(hotelEntity.getId()) && hotelId.equalsIgnoreCase(hotelEntity.getId()));
			}
		}
		return false;
	}

	public UserLocation buildUserLocationFromHeader(Map<String, String> httpHeaderMap) {
        Map<String, String> countryStateAndCity = getCountryAndCityCodeFromHeader(httpHeaderMap);
        String countryCode = countryStateAndCity.get(Constants.HEADER_COUNTRY_CODE);
        String stateCode = countryStateAndCity.get(Constants.REGION_CODE);
        String cityCode = countryStateAndCity.get(Constants.HEADER_CITY);
        UserLocation userLocation = new UserLocation();
        userLocation.setCity(cityCode);
        userLocation.setCountry(countryCode);
        userLocation.setState(stateCode);
        return userLocation;
    }
	/**
	 * @param userServiceResponseList
	 * @return
	 * Method checks if a user is logged in basis presence of UUID in user service response
	 */
	public boolean checkIfLoggedIn(List<UserServiceResponse> userServiceResponseList) {
		if (CollectionUtils.isNotEmpty(userServiceResponseList)) {
			UserServiceResponse userServiceResponse = userServiceResponseList.get(0);
			return userServiceResponse != null && userServiceResponse.getResult() != null
					&& userServiceResponse.getResult().getExtendedUser() != null &&
					StringUtils.isNotEmpty(userServiceResponse.getResult().getExtendedUser().getUuid());

		}
		return false;
	}

	private String isMetaPricerV2(BaseSearchRequest baseSearchRequest, String trafficSource, SearchCriteria searchCriteria) {
		String isMetaPricerV2 = FALSE;
		try {
			if(trafficSource.toUpperCase().startsWith(GOOGLEHOTELDFINDER)) {
				if (null != baseSearchRequest.getRequestDetails() && null != baseSearchRequest.getRequestDetails().getFunnelSource() && baseSearchRequest.getRequestDetails().getFunnelSource().equalsIgnoreCase(FUNNEL_SOURCE_HOMESTAY)) {
					isMetaPricerV2 = (null != baseSearchRequest.getExpDataMap() && null != baseSearchRequest.getExpDataMap().get(ALT_META) ? baseSearchRequest.getExpDataMap().get(ALT_META) : TRUE);
				} else if (trafficSource.startsWith(GOOGLEHOTELDFINDER + UNDERSCORE + DOM_HOTEL + UNDERSCORE + AE)) {
					isMetaPricerV2 = (null != baseSearchRequest.getExpDataMap() && null != baseSearchRequest.getExpDataMap().get(GCC_META) ? baseSearchRequest.getExpDataMap().get(GCC_META) : TRUE);
				}  else if (trafficSource.startsWith(GOOGLEHOTELDFINDER + UNDERSCORE + DOM_HOTEL + UNDERSCORE + KSA)) {
					isMetaPricerV2 = (null != baseSearchRequest.getExpDataMap() && null != baseSearchRequest.getExpDataMap().get(GCC_META) ? baseSearchRequest.getExpDataMap().get(GCC_META) : TRUE);
				} else if (!searchCriteria.getCountryCode().equalsIgnoreCase(Constants.DOM_COUNTRY)) {
					isMetaPricerV2 = (null != baseSearchRequest.getExpDataMap() && null != baseSearchRequest.getExpDataMap().get(INTEL_META) ? baseSearchRequest.getExpDataMap().get(INTEL_META) : TRUE);
				} else {
					isMetaPricerV2 = (null != baseSearchRequest.getExpDataMap() && null != baseSearchRequest.getExpDataMap().get(DOM_META) ? baseSearchRequest.getExpDataMap().get(DOM_META) : TRUE);
				}
			} else if (getRateTraffic.stream().anyMatch(ts -> trafficSource.toUpperCase().contains(ts.toUpperCase()))) {
				isMetaPricerV2 = (null != baseSearchRequest.getExpDataMap() && null != baseSearchRequest.getExpDataMap().get(GET_RATE_V2) ? baseSearchRequest.getExpDataMap().get(GET_RATE_V2) : TRUE);
			} else if (StringUtils.containsIgnoreCase(trafficSource, "SEO")
					|| StringUtils.containsIgnoreCase(trafficSource, "SEM")
					|| StringUtils.containsIgnoreCase(trafficSource, "ADTECH_BRAND_STORE_CLARKS")
					|| StringUtils.containsIgnoreCase(trafficSource, "WISHLIST")) {
				isMetaPricerV2 = TRUE;
			}
		} catch (NullPointerException exp){
			logger.error("Exception in isMetaPricerV2: {}", exp.getMessage());
			isMetaPricerV2 = TRUE;
		}
		return isMetaPricerV2;
	}

	public CouponInfo getAppliedCoupon(PersistedMultiRoomData persistedData) {
		CouponInfo appliedCoupon = null;

		if (MapUtils.isNotEmpty(persistedData.getCouponInfo())) {
			List<CouponInfo> appliedCoupons = persistedData.getCouponInfo().get(CouponStatus.APPLIED);
			if (CollectionUtils.isNotEmpty(appliedCoupons)) {
				appliedCoupon = appliedCoupons.get(0);
			}
		}

		return appliedCoupon;
	}

  public void buildFiltersToRemove(Map<com.mmt.hotels.filter.FilterGroup, Set<com.mmt.hotels.filter.Filter>> removeFilterMapCB, List<com.mmt.hotels.clientgateway.request.Filter> filtersToRemove) {
		if(CollectionUtils.isEmpty(filtersToRemove)) {
			return;
		}

		for(com.mmt.hotels.clientgateway.request.Filter filter : filtersToRemove) {
			com.mmt.hotels.filter.Filter filterToAdd = new com.mmt.hotels.filter.Filter();
			filterToAdd.setFilterGroup(com.mmt.hotels.filter.FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name()));
			filterToAdd.setFilterValue(filter.getFilterValue());
			if(!removeFilterMapCB.containsKey(com.mmt.hotels.filter.FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name()))) {
				removeFilterMapCB.put(com.mmt.hotels.filter.FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name()), new HashSet<>());
			}
			removeFilterMapCB.get(com.mmt.hotels.filter.FilterGroup.getFilterGroupFromFilterName(filter.getFilterGroup().name())).add(filterToAdd);
		}
	}

	/**
	 * Validates if WishListedSearch is true and featureFlag is false, then creates feature flag with specified values
	 * 
	 * @param searchCriteria The search criteria containing wishlist info
	 * @param baseSearchRequest The base search request containing feature flags
	 */
	private void validateAndUpdateTheWishListedSearch(SearchCriteria searchCriteria, BaseSearchRequest baseSearchRequest) {
		if (searchCriteria != null && searchCriteria instanceof SearchHotelsCriteria) {
			SearchHotelsCriteria hotelsCriteria = (SearchHotelsCriteria) searchCriteria;
			if (hotelsCriteria.isWishListedSearch()) {
				setupExpDataForWishListedSearch(baseSearchRequest);
				setupFeatureFlagsForWishListedSearch(baseSearchRequest);
				setupImageDetailsForWishListedSearch(baseSearchRequest);
				logger.info("Applied wishlist feature flags for WishListedSearch request");
			}
		}
	}
	
	/**
	 * Sets up experiment data map for wish listed search
	 * 
	 * @param baseSearchRequest The base search request
	 */
	private void setupExpDataForWishListedSearch(BaseSearchRequest baseSearchRequest) {
		// Initialize or update expDataMap with required values
		if (baseSearchRequest.getExpDataMap() == null) {
			baseSearchRequest.setExpDataMap(new HashMap<>());
		}
		
		Map<String, String> expDataMap = baseSearchRequest.getExpDataMap();
		
		// Add all required experiment values for listing if not already present
		addExperimentValueIfMissing(expDataMap,"PDO", "PN");
		addExperimentValueIfMissing(expDataMap, "BNPL", "T");
		addExperimentValueIfMissing(expDataMap, "MRS", "T");
		addExperimentValueIfMissing(expDataMap, "MCUR", "T");
		addExperimentValueIfMissing(expDataMap, "ADDON", "T");
		addExperimentValueIfMissing(expDataMap, "CHPC", "T");
		addExperimentValueIfMissing(expDataMap, "AARI", "T");
		addExperimentValueIfMissing(expDataMap, "NLP", "Y");
		addExperimentValueIfMissing(expDataMap, "RCPN", "T");
		addExperimentValueIfMissing(expDataMap, "MMRVER", "V3");
		addExperimentValueIfMissing(expDataMap, "BLACK", "T");
		addExperimentValueIfMissing(expDataMap, "FLTRPRCBKT", "T");
		addExperimentValueIfMissing(expDataMap, "LSTNRBY", "T");
		addExperimentValueIfMissing(expDataMap, "HIS", "DEFAULT");
		addExperimentValueIfMissing(expDataMap, "AIP", "T");
		addExperimentValueIfMissing(expDataMap, "APT", "T");
		addExperimentValueIfMissing(expDataMap, "SOU", "F");
		addExperimentValueIfMissing(expDataMap, "CV2", "F");
		addExperimentValueIfMissing(expDataMap, "CRI", "F");
		
		// Convert expDataMap to expData string and set it in the request
		convertExpDataMapToString(baseSearchRequest, expDataMap);
	}
	
	/**
	 * Converts experiment data map to a string and sets it in the request
	 * 
	 * @param baseSearchRequest The base search request
	 * @param expDataMap The experiment data map
	 */
	private void convertExpDataMapToString(BaseSearchRequest baseSearchRequest, Map<String, String> expDataMap) {
		try {
			// Create expData string in format "KEY1:VALUE1,KEY2:VALUE2,..."
			StringBuilder expDataBuilder = new StringBuilder();
			for (Map.Entry<String, String> entry : expDataMap.entrySet()) {
				if (expDataBuilder.length() > 0) {
					expDataBuilder.append(",");
				}
				expDataBuilder.append(entry.getKey()).append(":").append(entry.getValue());
			}
			baseSearchRequest.setExpData(expDataBuilder.toString());
			logger.info("Set expData in request: {}", expDataBuilder.toString());
		} catch (Exception e) {
			logger.error("Error converting expDataMap to expData string", e);
		}
	}
	
	/**
	 * Sets up feature flags for wish listed search
	 * 
	 * @param baseSearchRequest The base search request
	 */
	private void setupFeatureFlagsForWishListedSearch(BaseSearchRequest baseSearchRequest) {
		FeatureFlags featureFlags = baseSearchRequest.getFeatureFlags();
		if (featureFlags == null) {
			featureFlags = createDefaultFeatureFlagsForWishlist();
			baseSearchRequest.setFeatureFlags(featureFlags);
		}
	}
	
	/**
	 * Creates default feature flags for wishlist search
	 * 
	 * @return The default feature flags
	 */
	private FeatureFlags createDefaultFeatureFlagsForWishlist() {
		FeatureFlags featureFlags = new FeatureFlags();
		// Set feature flags as specified
		featureFlags.setAddOnRequired(false);
		featureFlags.setApplyAbsorption(false);
		featureFlags.setComparator(false);
		featureFlags.setCorpMMRRequired(false);
		featureFlags.setCoupon(true);
		featureFlags.setDealOfTheDayRequired(true);
		featureFlags.setDetailMap(false);
		featureFlags.setExtraAltAccoRequired(false);
		featureFlags.setFlashDealClaimed(false);
		// Not all methods are available - using only those seen in examples
		featureFlags.setHotelCatAndPropNotRequiredInMeta(true);
		featureFlags.setLimitedFilterCall(false);
		featureFlags.setMmtPrime(false);
		featureFlags.setNoOfAddons(1);
		featureFlags.setNoOfCoupons(2);
		featureFlags.setNoOfPersuasions(0);
		// Skipping unavailable methods
		featureFlags.setPersuasionSuppression(false);
		featureFlags.setPersuasionsEngineHit(true);
		featureFlags.setPoisRequiredOnMap(false);
		// featureFlags.setPriceInfoReq(true); - Method not available
		featureFlags.setReviewSummaryRequired(true);
		featureFlags.setRoomInfoRequired(false);
		featureFlags.setShortlistingRequired(true);
		// featureFlags.setShortlistRequired(true); - Method not available
		// featureFlags.setShowRushDealsBottomSheet(false); - Method not available
		// featureFlags.setSortPersuasion(false); - Method not available
		featureFlags.setStaticData(true);
		// featureFlags.setTopRatedCommentRequired(false); - Method not available
		featureFlags.setUnmodifiedAmenities(true);
		featureFlags.setUpsellRateplanRequired(false);
		featureFlags.setWalletRequired(true);
		return featureFlags;
	}
	

	
	/**
	 * Sets up image details for wish listed search
	 * 
	 * @param baseSearchRequest The base search request
	 */
	private void setupImageDetailsForWishListedSearch(BaseSearchRequest baseSearchRequest) {
		if (baseSearchRequest instanceof ListingSearchRequest) {
			ListingSearchRequest listingRequest = (ListingSearchRequest) baseSearchRequest;
			if (listingRequest.getImageDetails() == null) {
				listingRequest.setImageDetails(createDefaultImageDetails());
				logger.info("Added default image details to WishListedSearch request");
			}
		}
	}
	
	/**
	 * Creates default image details for wishlist search
	 * 
	 * @return The default image details
	 */
	private ImageDetails createDefaultImageDetails() {
		// Create image details object
		ImageDetails imageDetails = new ImageDetails();
		
		// Create the categories list
		List<ImageCategory> categories = new ArrayList<>();
		ImageCategory category = new ImageCategory();
		category.setCount(4);
		category.setHeight(330);
		category.setImageFormat("webp");
		category.setType("H");
		category.setWidth(602);
		categories.add(category);
		imageDetails.setCategories(categories);
		
		// Create the types list
		List<String> types = new ArrayList<>();
		types.add("professional");
		imageDetails.setTypes(types);
		
		return imageDetails;
	}
	
	/**
	 * Helper method to add an experiment value to the map only if it doesn't already exist
	 * 
	 * @param expDataMap The experiment data map
	 * @param key The experiment key
	 * @param value The experiment value
	 */
	private void addExperimentValueIfMissing(Map<String, String> expDataMap, String key, String value) {
		if (!expDataMap.containsKey(key)) {
			expDataMap.put(key, value);
			logger.debug("Added default experiment value {}={} to WishListedSearch request", key, value);
		}
	}
}
