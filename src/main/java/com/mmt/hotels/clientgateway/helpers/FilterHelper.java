package com.mmt.hotels.clientgateway.helpers;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.mmt.hotels.clientgateway.businessobjects.*;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.matchmaker.LatLngObject;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.request.matchmaker.Tags;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.ArrayList;

import static com.mmt.hotels.clientgateway.constants.Constants.*;

@Component
public class FilterHelper {

    private final Gson gson = new Gson();

    @Autowired
    PolyglotService polyglotService;


    public FilterConfiguration getFilterConfig(String... filterConfigList) {
        FilterConfiguration filterConfiguration = null;

        if (filterConfigList != null && filterConfigList.length > 0) {
            String idContext = filterConfigList[filterConfigList.length -1];
            for (int i = 0; i < filterConfigList.length - 1 ; i++) {
                FilterConfiguration fConfig = null;

                if (StringUtils.isNotEmpty(filterConfigList[i])) {
                    fConfig = gson.fromJson(filterConfigList[i], new TypeToken<FilterConfiguration>() {
                    }.getType());
                }

                if (filterConfiguration == null)
                    filterConfiguration = fConfig;
                else {
                    filterConfiguration = mergeFilterConfig(filterConfiguration, fConfig, idContext);
                }
            }
        }
        return filterConfiguration;
    }

    public FilterConfigurationV2 getFilterConfigV2(String... filterConfigList) {
        FilterConfigurationV2 filterConfiguration = null;

        if(filterConfigList == null || filterConfigList.length ==0) {
            return filterConfiguration;
        }
        String idContext = filterConfigList[filterConfigList.length - 1];
        for (int i = 0; i < filterConfigList.length - 1; i++) {

            if (StringUtils.isEmpty(filterConfigList[i])) {
                continue;
            }
            try {
                FilterConfigurationV2 fConfig = gson.fromJson(filterConfigList[i], new TypeToken<FilterConfigurationV2>() {
                }.getType());

                if (filterConfiguration == null)
                    filterConfiguration = fConfig;
                else {
                    filterConfiguration = mergeFilterPages(filterConfiguration, fConfig, idContext);
                }
            } catch (Exception exception) {

            }

        }
        return filterConfiguration;
    }

    private FilterConfigurationV2 mergeFilterPages(FilterConfigurationV2 srcFilterConfig, FilterConfigurationV2 addedFilterConfig, String idContext) {
        FilterConfigurationV2 mergedFilterConfig = new FilterConfigurationV2();

        if (addedFilterConfig == null) {
            mergedFilterConfig = srcFilterConfig;
            return mergedFilterConfig;
        }
        if(!Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            mergedFilterConfig.setHomestayBannerIconUrl(srcFilterConfig.getHomestayBannerIconUrl());
        }
        mergePages(mergedFilterConfig, srcFilterConfig, addedFilterConfig, Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext));
        mergedFilterConfig = calculatePagesToShow(mergedFilterConfig, addedFilterConfig);
        return mergedFilterConfig;
    }

    private FilterConfigurationV2 calculatePagesToShow(FilterConfigurationV2 mergedFilterConfig, FilterConfigurationV2 addedFilterConfig) {
        if (MapUtils.isEmpty(addedFilterConfig.getPagesToShow()) || mergedFilterConfig == null || MapUtils.isEmpty(mergedFilterConfig.getFilterPages())) {
            return mergedFilterConfig;
        }
        FilterConfigurationV2 finalConfig = new FilterConfigurationV2();
        finalConfig.setConditions(mergedFilterConfig.getConditions());
        finalConfig.setRankOrder(addedFilterConfig.getRankOrder());
        finalConfig.setHomestayBannerIconUrl(mergedFilterConfig.getHomestayBannerIconUrl());
        finalConfig.setFilterPages(new LinkedHashMap<>());

        for (Map.Entry<String, FilterPage> pageEntry : addedFilterConfig.getPagesToShow().entrySet()) {
            FilterPage filterPage = mergedFilterConfig.getFilterPages().get(pageEntry.getKey());
            if (filterPage == null) {
                continue;
            }
            FilterPage finalPage = getFilterPage(pageEntry, filterPage);
            finalConfig.getFilterPages().put(pageEntry.getKey(), finalPage);
        }
        return finalConfig;
    }

    private static FilterPage getFilterPage(Map.Entry<String, FilterPage> pageEntry, FilterPage filterPage) {
        FilterPage finalPage = new FilterPage();
        if (pageEntry.getValue() != null) {
            LinkedHashMap<String, FilterConfigCategory> filtersToShow = pageEntry.getValue().getFilters();
            finalPage.setFilters(filtersToShow == null ? filterPage.getFilters() : filtersToShow);
            finalPage.setPage_id(filterPage.getPage_id());
            finalPage.setTitle(filterPage.getTitle());
            finalPage.setSearchable(filterPage.isSearchable());
            finalPage.setNewTagIcon(filterPage.getNewTagIcon());
            finalPage.setFiltersToShow(new LinkedHashMap<>());
        }
        return finalPage;
    }

    private void mergePages(FilterConfigurationV2 mergedFilterConfig, FilterConfigurationV2 srcFilterConfig, FilterConfigurationV2 addedFilterConfig, boolean isCorpIdContext) {
        if (MapUtils.isEmpty(addedFilterConfig.getFilterPages())) {
            mergedFilterConfig.setFilterPages(srcFilterConfig.getFilterPages());
            return;
        }

        if (MapUtils.isNotEmpty(srcFilterConfig.getFilterPages())) {
            mergedFilterConfig.setFilterPages(new LinkedHashMap<>());
            for (Map.Entry<String, FilterPage> entry : srcFilterConfig.getFilterPages().entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                FilterPage filterPage = entry.getValue();
                if (addedFilterConfig.getFilterPages().containsKey(entry.getKey())) {
                    FilterPage fCategoryAdded = addedFilterConfig.getFilterPages().get(entry.getKey());
                    mergedFilterConfig.getFilterPages().put(entry.getKey(), fCategoryAdded);
                } else {
                    mergedFilterConfig.getFilterPages().put(entry.getKey(), filterPage);
                }
            }
        }

        //those which are present in the addedFilterConfig but not present in the srcFilterConfig, so not yet added into mergedFilterConfig
        for (Map.Entry<String, FilterPage> entry : addedFilterConfig.getFilterPages().entrySet()) {
            LinkedHashMap<String, FilterPage> mergedFilterPages = mergedFilterConfig.getFilterPages();
            if (MapUtils.isNotEmpty(mergedFilterPages) && !mergedFilterPages.containsKey(entry.getKey())) {
                mergedFilterPages.put(entry.getKey(), entry.getValue());
            }

        }
    }

    public FilterConfiguration mergeFilterConfig(FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig, String idContext) {
        FilterConfiguration mergedFilterConfig = new FilterConfiguration();

        if (addedFilterConfig == null) {
            mergedFilterConfig = srcFilterConfig;
            return mergedFilterConfig;
        }

        mergeFilters(mergedFilterConfig, srcFilterConfig, addedFilterConfig, Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext));
        mergedFilterConfig = calculateFiltersToShow(mergedFilterConfig, addedFilterConfig);
        overrideRankOrder(mergedFilterConfig, srcFilterConfig, addedFilterConfig);
        addConditions(mergedFilterConfig, srcFilterConfig, addedFilterConfig);
        if(!Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            mergedFilterConfig.setHomestayBannerIconUrl(srcFilterConfig.getHomestayBannerIconUrl());
        }
        addCategoryAttributes(mergedFilterConfig, addedFilterConfig);
        return mergedFilterConfig;
    }

    private void addCategoryAttributes(FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig) {
        if (addedFilterConfig != null && MapUtils.isNotEmpty(addedFilterConfig.getCategoryAttributes()) && srcFilterConfig != null && MapUtils.isNotEmpty(srcFilterConfig.getFilters())) {
            addedFilterConfig.getCategoryAttributes().forEach((category, attributes) -> {
                if (srcFilterConfig.getFilters().containsKey(category)) {
                    FilterConfigCategory filterConfigCategory = srcFilterConfig.getFilters().get(category);
                    filterConfigCategory.setCollapsed(attributes.isCollapsed());
                    filterConfigCategory.setRemoveZeroCountFilters(attributes.isRemoveZeroCountFilters());
                    filterConfigCategory.setSortingRequired(attributes.isSortingRequired());
                    filterConfigCategory.setCountThreshold(attributes.getCountThreshold());
                    filterConfigCategory.setMaxFilterListSize(attributes.getMaxFilterListSize() != null ? attributes.getMaxFilterListSize() : null);
                }
            });
        }
    }


    private FilterConfiguration calculateFiltersToShow(FilterConfiguration mergedFilterConfig, FilterConfiguration addedFilterConfig) {
        if (MapUtils.isNotEmpty(addedFilterConfig.getFiltersToShow()) && mergedFilterConfig != null && mergedFilterConfig.getFilters() != null) {
            FilterConfiguration finalConfig = new FilterConfiguration();
            finalConfig.setConditions(mergedFilterConfig.getConditions());
            finalConfig.setRankOrder(mergedFilterConfig.getRankOrder());
            finalConfig.setFilters(new LinkedHashMap<>());
            for (Map.Entry<String, LinkedHashMap<String, List<String>>> catEntry : addedFilterConfig.getFiltersToShow().entrySet()) {
                FilterConfigCategory filterCat = mergedFilterConfig.getFilters().get(catEntry.getKey());
                if (filterCat != null) {
                    FilterConfigCategory finalCat = new FilterConfigCategory();
                    finalCat.setCondition(filterCat.getCondition());
                    finalCat.setTitle(filterCat.getTitle());
                    finalCat.setViewType(filterCat.getViewType());
                    finalCat.setShowCustomRange(filterCat.isShowCustomRange());
                    finalCat.setCustomRangeTitle(filterCat.getCustomRangeTitle());
                    finalCat.setVisible(filterCat.isVisible());
                    finalCat.setShowMore(filterCat.isShowMore());
                    finalCat.setMinItemsToShow(filterCat.getMinItemsToShow());
                    finalCat.setShowImageUrl(filterCat.isShowImageUrl());
                    finalCat.setIconUrl(filterCat.getIconUrl());
                    finalCat.setDescription(filterCat.getDescription());
                    finalCat.setRemoveZeroCountFilters(filterCat.isRemoveZeroCountFilters());
                    finalCat.setMaxFilterListSize(filterCat.getMaxFilterListSize() != null ? filterCat.getMaxFilterListSize() : null);
                    finalCat.setCountThreshold(filterCat.getCountThreshold());
                    finalCat.setGroups(new LinkedHashMap<>());
                    finalCat.setSingleSelection(filterCat.isSingleSelection());
                    for (Map.Entry<String, List<String>> filterGrpEntry : catEntry.getValue().entrySet()) {
                        finalCat.getGroups().put(filterGrpEntry.getKey(), new LinkedHashMap<>());
                        LinkedHashMap<String, FilterConfigDetail> filterGroupValues = filterCat.getGroups().get(filterGrpEntry.getKey());
                        if (MapUtils.isNotEmpty(filterGroupValues)) {
                            if (CollectionUtils.isNotEmpty(filterGrpEntry.getValue())) {
                                for (String filterGrpVal : filterGrpEntry.getValue()) {
                                    String filterValue = filterGrpVal.split(":")[0];
                                    FilterConfigDetail finalConfigDetail = filterGroupValues.get(filterValue);
                                    finalCat.getGroups().get(filterGrpEntry.getKey()).put(filterGrpVal, finalConfigDetail);
                                }
                            }
                        }
                    }
                    finalConfig.getFilters().put(catEntry.getKey(), finalCat);
                }
            }


            return finalConfig;
        }
        return mergedFilterConfig;

    }

    private void overrideRankOrder(FilterConfiguration mergedFilterConfig, FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig) {
        if (MapUtils.isNotEmpty(addedFilterConfig.getRankOrder()))
            mergedFilterConfig.setRankOrder(addedFilterConfig.getRankOrder());
        else if (MapUtils.isNotEmpty(srcFilterConfig.getRankOrder()))
            mergedFilterConfig.setRankOrder(srcFilterConfig.getRankOrder());
    }

    private void addConditions(FilterConfiguration mergedFilterConfig, FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig) {
        mergedFilterConfig.setConditions(new LinkedHashMap<>());

        if (MapUtils.isNotEmpty(srcFilterConfig.getConditions()))
            mergedFilterConfig.setConditions(srcFilterConfig.getConditions());
        if (MapUtils.isNotEmpty(addedFilterConfig.getConditions())) {
            for (Map.Entry<String, List<String>> entry : addedFilterConfig.getConditions().entrySet())
                mergedFilterConfig.getConditions().put(entry.getKey(), entry.getValue());
        }

    }

    private void mergeFilters(FilterConfiguration mergedFilterConfig, FilterConfiguration srcFilterConfig, FilterConfiguration addedFilterConfig, boolean isCorpContext) {
        if (MapUtils.isNotEmpty(addedFilterConfig.getFilters())) {
            if (MapUtils.isNotEmpty(srcFilterConfig.getFilters())) {
                mergedFilterConfig.setFilters(new LinkedHashMap<>());
                for (Map.Entry<String, FilterConfigCategory> entry : srcFilterConfig.getFilters().entrySet()) {
                    FilterConfigCategory fCategory = entry.getValue();
                    if (addedFilterConfig.getFilters().containsKey(entry.getKey())) {
                        FilterConfigCategory fCategoryAdded = addedFilterConfig.getFilters().get(entry.getKey());
                        fCategoryAdded.setRemoveZeroCountFilters(fCategory.isRemoveZeroCountFilters());
                        fCategoryAdded.setCountThreshold(fCategory.getCountThreshold());
                        fCategoryAdded.setMaxFilterListSize(fCategory.getMaxFilterListSize() != null ? fCategory.getMaxFilterListSize() : null);
                        mergeConditions(fCategory, fCategoryAdded);
                        if(!isCorpContext) {
                            updateIconUrls(fCategory, fCategoryAdded);
                        }
                        mergedFilterConfig.getFilters().put(entry.getKey(), fCategoryAdded);
                    }else{
                        mergedFilterConfig.getFilters().put(entry.getKey(), fCategory);
                    }
                }
            }

            //those which are present in the addedFilterConfig but not present in the srcFilterConfig, so not yet added into mergedFilterConfig
            for (Map.Entry<String, FilterConfigCategory> entry : addedFilterConfig.getFilters().entrySet()) {
                FilterConfigCategory fCategory = entry.getValue();
                if (!mergedFilterConfig.getFilters().containsKey(entry.getKey())) {
                    mergedFilterConfig.getFilters().put(entry.getKey(), fCategory);
                }

            }
        } else {
            mergedFilterConfig.setFilters(srcFilterConfig.getFilters());
            if (isCorpContext) {
                /*don't set new icon urls*/
                removeIconUrlsAndImageUrls(mergedFilterConfig);
            }
        }
    }

    private void removeIconUrlsAndImageUrls(FilterConfiguration mergedFilterConfig) {
        if (mergedFilterConfig == null || MapUtils.isEmpty(mergedFilterConfig.getFilters())) {
            return;
        }
        mergedFilterConfig.getFilters().forEach((filterGroup, filterConfig) -> {
            filterConfig.setIconUrl(null);
            if (MapUtils.isNotEmpty(filterConfig.getGroups())) {
                filterConfig.getGroups().forEach((filterGroupValue, fConfig) -> {
                    if (MapUtils.isNotEmpty(fConfig)) {
                        fConfig.forEach((filterValue, filterValueConfig) -> {
                            if (filterValueConfig != null) {
                                filterValueConfig.setImageUrl(null);
                                filterValueConfig.setIconList(null);
                            }
                        });
                    }
                });
            }
        });
    }

    private void updateIconUrls(FilterConfigCategory fCategory, FilterConfigCategory fCategoryAdded) {
        if (MapUtils.isNotEmpty(fCategory.getGroups()) && MapUtils.isNotEmpty(fCategoryAdded.getGroups())) {
            fCategoryAdded.getGroups().forEach((filterGroup, filters) -> {
                if (fCategory.getGroups().containsKey(filterGroup) && MapUtils.isNotEmpty(filters)) {
                    LinkedHashMap<String, FilterConfigDetail> filterConfigDetail = fCategory.getGroups().get(filterGroup);
                    filters.forEach((filterValue, filterConfig) -> {
                        FilterConfigDetail configDetail = filterConfigDetail.get(filterValue);
                        if (configDetail != null && filterConfig != null) {
                            filterConfig.setIconList(configDetail.getIconList());
                            filterConfig.setImageUrl(configDetail.getImageUrl());
                        }
                    });
                }
            });
        }
        fCategoryAdded.setIconUrl(fCategory.getIconUrl());
    }

    private void mergeConditions(FilterConfigCategory fCategory, FilterConfigCategory fCategoryAdded) {
        if(MapUtils.isNotEmpty(fCategory.getCondition())){
            LinkedHashMap<String, String> finalCondition=new LinkedHashMap<>(fCategory.getCondition());
            if(MapUtils.isNotEmpty(fCategoryAdded.getCondition()))
                finalCondition.putAll(fCategoryAdded.getCondition());
            fCategoryAdded.setCondition(finalCondition);
        }
    }

    public String fetchPriceTitle(LinkedHashMap<String,String>expDataMap){
        StringBuilder sb = new StringBuilder();

        String pdo = (MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(Constants.PRICE_EXP)) ? expDataMap.get(Constants.PRICE_EXP) : EMPTY_STRING;
        switch (pdo){
            case Constants.PRICE_PN : sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PN_TITLE));
                break;
            case Constants.PRICE_PNT: sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PNT_TITLE));
                break;
            case Constants.PRICE_PRN : sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PRN_TITLE));
                break;
            case Constants.PRICE_PRNT: sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_PRNT_TITLE));
                break;
            case Constants.PRICE_TP: sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TP_TITLE));
                break;
            case Constants.PRICE_TPT: sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_TPT_TITLE));
                break;
            default:
                sb.append(polyglotService.getTranslatedData(ConstantsTranslation.PRICE_FILTER_TITLE));

        }
        return  sb.toString();
    }

    public void updateAppliedFilterMapDptCollections(Map<com.mmt.hotels.filter.FilterGroup, Set<Filter>> appliedFilterMapCB,
                                                     MatchMakerRequest matchMakerDetails, Set<String> dptInlineAppliedCategories) {

        if (MapUtils.isNotEmpty(appliedFilterMapCB)) {
            dptInlineAppliedCategories = dptInlineAppliedCategories != null ? dptInlineAppliedCategories : new HashSet<>();
            boolean dptFilterConfigFromDpt = false;
            if (appliedFilterMapCB.containsKey(com.mmt.hotels.filter.FilterGroup.DPT_COLLECTIONS)) {
                Set<com.mmt.hotels.filter.Filter> collectionFilterValue = appliedFilterMapCB.get(com.mmt.hotels.filter.FilterGroup.DPT_COLLECTIONS);
                for (com.mmt.hotels.filter.Filter filter : collectionFilterValue) {
                    if (StringUtils.isNotEmpty(filter.getFilterValue()) && filter.getFilterValue().contains(FILTER_GROUP_SPLITTER_DPT)) {
                        dptFilterConfigFromDpt = true;
                        String[] collectionFilterMap = filter.getFilterValue().split(FILTER_GROUP_SPLITTER_DPT);
                        if (collectionFilterMap != null && collectionFilterMap.length > 0) {
                            dptInlineAppliedCategories.add(collectionFilterMap[0]);
                            for (int i = 1; i < collectionFilterMap.length; i++) {
                                String[] filters = collectionFilterMap[i].split(FILTER_GROUP_VALUE_SPLITTER_DPT);
                                Set<com.mmt.hotels.filter.Filter> dptFilters = new HashSet<>();
                                if (filters != null && filters.length > 0 && !MATCHMAKER_AREA.equalsIgnoreCase(filters[0]) && !MATCHMAKER_POI.equalsIgnoreCase(filters[0])) {
                                    updateAppliedFilterMapForNonLocationDptFilters(appliedFilterMapCB, filters, dptFilters);
                                } else {
                                    updateAppliedFilterMapForLocationDptFilters(matchMakerDetails, filters);
                                }

                            }
                        }
                    }
                }
                if (dptFilterConfigFromDpt) {
                    appliedFilterMapCB.remove(com.mmt.hotels.filter.FilterGroup.DPT_COLLECTIONS);
                }

            }
            if (appliedFilterMapCB.containsKey(FilterGroup.DPT_PROP_COLLECTIONS)) {
                Set<com.mmt.hotels.filter.Filter> collectionFilterValue = appliedFilterMapCB.get(FilterGroup.DPT_PROP_COLLECTIONS);
                Set<Filter> appliedPropFilters = new HashSet<>();
                for (com.mmt.hotels.filter.Filter filter : collectionFilterValue) {
                    if (filter != null && StringUtils.isNotEmpty(filter.getFilterValue()) && filter.getFilterValue().contains(FILTER_GROUP_SPLITTER_DPT)) {
                        dptFilterConfigFromDpt = true;
                        String[] filterValue = filter.getFilterValue().split(FILTER_GROUP_SPLITTER_DPT);
                        if (filterValue != null && filterValue.length > 1) {
                            Filter propFilter = new Filter();
                            propFilter.setFilterGroup(FilterGroup.DPT_PROP_COLLECTIONS);
                            propFilter.setFilterValue(filterValue[1]);
                            appliedPropFilters.add(propFilter);
                            dptInlineAppliedCategories.add(filterValue[0]);
                        }
                    }
                }
                if (dptFilterConfigFromDpt) {
                    appliedFilterMapCB.remove(FilterGroup.DPT_PROP_COLLECTIONS);
                }
                appliedFilterMapCB.putIfAbsent(FilterGroup.DPT_PROP_COLLECTIONS, appliedPropFilters);
            }
        }
    }

    public static MatchMakerRequest updateAppliedFilterMapForLocationDptFilters(MatchMakerRequest matchMakerDetails, String[] filters) {

        if (filters != null && filters.length > 1) {
            String[] filterValueList = filters[1].split(FILTER_VALUE_SPLITTER_DPT);
            if (matchMakerDetails == null) {
                matchMakerDetails = new MatchMakerRequest();
            }
            if (MATCHMAKER_AREA.equalsIgnoreCase(filters[0])) {
                if (CollectionUtils.isEmpty(matchMakerDetails.getSelectedTags())) {
                    matchMakerDetails.setSelectedTags(new ArrayList<>());
                }
                for (int i = 0; i < filterValueList.length; i++) {
                    Tags areaTag = new Tags();
                    areaTag.setTagAreaId(filterValueList[i]);
                    areaTag.setTagDescription(SELECTED_AREA_TEXT);
                    matchMakerDetails.getSelectedTags().add(areaTag);
                }
            } else if (MATCHMAKER_POI.equalsIgnoreCase(filters[0])) {
                if (CollectionUtils.isEmpty(matchMakerDetails.getLatLng())) {
                    matchMakerDetails.setLatLng(new ArrayList<>());
                }
                for (int i = 0; i < filterValueList.length; i++) {
                    LatLngObject latLngObject = new LatLngObject();
                    latLngObject.setPoiId(filterValueList[i]);
                    matchMakerDetails.getLatLng().add(latLngObject);
                }
            }
        }
        return matchMakerDetails;
    }

    private void updateAppliedFilterMapForNonLocationDptFilters(Map<FilterGroup, Set<Filter>> appliedFilterMapCB, String[] filters, Set<Filter> dptFilters) {
        if (filters != null && filters.length > 1) {
            String[] filterValueList = filters[1].split(FILTER_VALUE_SPLITTER_DPT);
            for (int i = 0; i < filterValueList.length; i++) {
                Filter filter = new Filter();
                filter.setFilterGroup(FilterGroup.getFilterGroupFromFilterName(filters[0]));
                if (FilterGroup.HOTEL_PRICE_BUCKET.name().equalsIgnoreCase(filters[0])) {
                    String[] priceMinMax = filterValueList[i].split(Constants.HYPEN);
                    if (priceMinMax != null && priceMinMax.length == 2) {
                        com.mmt.hotels.filter.FilterRange filterRange = new com.mmt.hotels.filter.FilterRange();
                        filterRange.setMinValue(Integer.parseInt(priceMinMax[0]));
                        filterRange.setMaxValue(Integer.parseInt(priceMinMax[1]));
                        filter.setFilterRange(filterRange);
                        filter.setRangeFilter(true);
                    }
                } else {
                    filter.setFilterValue(filterValueList[i]);
                }
                dptFilters.add(filter);
            }
            if (appliedFilterMapCB.containsKey(FilterGroup.getFilterGroupFromFilterName(filters[0]))) {
                appliedFilterMapCB.get(FilterGroup.getFilterGroupFromFilterName(filters[0])).addAll(dptFilters);
            } else {
                appliedFilterMapCB.putIfAbsent(FilterGroup.getFilterGroupFromFilterName(filters[0]), dptFilters);
            }
        }
    }

    // ================================= Filter Config V2 =========================================

    public FilterConfigurationV2 getFilterConfigV2(
            FilterConfigurationV2 baseFilterConfig,
            FilterConfigurationV2 childFilterConfig,
            String idContext,
            String cohort
    ) {

        if(baseFilterConfig == null ) {
            return null;
        }

        if (childFilterConfig == null) {
            return baseFilterConfig;
        }

        return mergedFilterConfig(baseFilterConfig, childFilterConfig, idContext, cohort);

    }

    private FilterConfigurationV2 mergedFilterConfig(FilterConfigurationV2 baseFilterConfig, FilterConfigurationV2 childFilterConfig, String idContext, String cohort) {

        FilterConfigurationV2 mergedFilterConfig = new FilterConfigurationV2();

        if(!Constants.CORP_ID_CONTEXT.equalsIgnoreCase(idContext)) {
            mergedFilterConfig.setHomestayBannerIconUrl(baseFilterConfig.getHomestayBannerIconUrl());
        }

        if (MapUtils.isNotEmpty(baseFilterConfig.getFilterPages())) {
            mergedFilterConfig.setFilterPages(new LinkedHashMap<>());

            // override baseFilterConfig (override baseFilterConfig item with childFilterConfig item if exists)
            for (Map.Entry<String, FilterPage> baseFilterConfigEntry : baseFilterConfig.getFilterPages().entrySet()) {
                if (baseFilterConfigEntry.getValue() == null) {
                    continue;
                }
                FilterPage filterPage = baseFilterConfigEntry.getValue();
                if (MapUtils.isNotEmpty(childFilterConfig.getFilterPages()) && childFilterConfig.getFilterPages().containsKey(baseFilterConfigEntry.getKey())) {
                    filterPage = childFilterConfig.getFilterPages().get(baseFilterConfigEntry.getKey());
                }
                mergedFilterConfig.getFilterPages().put(baseFilterConfigEntry.getKey(), filterPage);
            }

            // append new item (new item in childConfig which do not exist in base config)
            if(MapUtils.isNotEmpty(childFilterConfig.getFilterPages())){
                for (Map.Entry<String, FilterPage> entry : childFilterConfig.getFilterPages().entrySet()) {
                    if (!mergedFilterConfig.getFilterPages().containsKey(entry.getKey())) {
                        mergedFilterConfig.getFilterPages().put(entry.getKey(), entry.getValue());
                    }
                }
            }

            // filter mergedFilterConfig based on pagesToShow
            if (!MapUtils.isEmpty(childFilterConfig.getPagesToShow())) {
                LinkedHashMap<String, FilterPage> sortedPagesMap = new LinkedHashMap<>();
                for (Map.Entry<String, FilterPage> childFilterConfigEntry : childFilterConfig.getPagesToShow().entrySet()) {
                    FilterPage filterPage = mergedFilterConfig.getFilterPages().get(childFilterConfigEntry.getKey());
                    if (filterPage == null) {
                        continue;
                    }
                    FilterPage finalPage = getFilterPage(childFilterConfigEntry, filterPage);
                    sortedPagesMap.put(childFilterConfigEntry.getKey(), finalPage);
                }
                mergedFilterConfig.setFilterPages(sortedPagesMap);
            }

            // set RankOrder based on cohort
            if(childFilterConfig.getRankOrderV2() != null){
                LinkedHashMap<String, Integer> cohortBasedRankOrder = childFilterConfig.getRankOrderV2().getOrDefault(cohort, childFilterConfig.getRankOrderV2().getOrDefault("default", childFilterConfig.getRankOrder()));
                mergedFilterConfig.setRankOrder(cohortBasedRankOrder);
            }else{
                mergedFilterConfig.setRankOrder(childFilterConfig.getRankOrder());
            }


            return mergedFilterConfig;
        }

        return baseFilterConfig;

    }

    // ==================================================================================================

}
